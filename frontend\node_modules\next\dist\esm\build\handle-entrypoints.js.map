{"version": 3, "sources": ["../../src/build/handle-entrypoints.ts"], "sourcesContent": ["import type { CustomRoutes } from '../lib/load-custom-routes'\nimport type { TurbopackManifestLoader } from '../shared/lib/turbopack/manifest-loader'\nimport type {\n  TurbopackResult,\n  RawEntrypoints,\n  Entrypoints,\n  PageRoute,\n  AppRoute,\n} from './swc/types'\nimport * as Log from './output/log'\nimport { getEntryKey } from '../shared/lib/turbopack/entry-key'\nimport {\n  processIssues,\n  type EntryIssuesMap,\n} from '../shared/lib/turbopack/utils'\n\nexport async function handleEntrypoints({\n  entrypoints,\n  currentEntrypoints,\n  currentEntryIssues,\n  manifestLoader,\n  productionRewrites,\n  logErrors,\n}: {\n  entrypoints: TurbopackResult<RawEntrypoints>\n  currentEntrypoints: Entrypoints\n  currentEntryIssues: EntryIssuesMap\n  manifestLoader: TurbopackManifestLoader\n  productionRewrites: CustomRoutes['rewrites'] | undefined\n  logErrors: boolean\n}) {\n  currentEntrypoints.global.app = entrypoints.pagesAppEndpoint\n  currentEntrypoints.global.document = entrypoints.pagesDocumentEndpoint\n  currentEntrypoints.global.error = entrypoints.pagesErrorEndpoint\n\n  currentEntrypoints.global.instrumentation = entrypoints.instrumentation\n\n  currentEntrypoints.page.clear()\n  currentEntrypoints.app.clear()\n\n  for (const [pathname, route] of entrypoints.routes) {\n    switch (route.type) {\n      case 'page':\n      case 'page-api':\n        currentEntrypoints.page.set(pathname, route)\n        break\n      case 'app-page': {\n        route.pages.forEach((page) => {\n          currentEntrypoints.app.set(page.originalName, {\n            type: 'app-page',\n            ...page,\n          })\n        })\n        break\n      }\n      case 'app-route': {\n        currentEntrypoints.app.set(route.originalName, route)\n        break\n      }\n      default:\n        Log.info(`skipping ${pathname} (${route.type})`)\n        break\n    }\n  }\n\n  const { middleware, instrumentation } = entrypoints\n\n  // We check for explicit true/false, since it's initialized to\n  // undefined during the first loop (middlewareChanges event is\n  // unnecessary during the first serve)\n  if (currentEntrypoints.global.middleware && !middleware) {\n    const key = getEntryKey('root', 'server', 'middleware')\n    // Went from middleware to no middleware\n    currentEntryIssues.delete(key)\n  }\n\n  currentEntrypoints.global.middleware = middleware\n\n  if (instrumentation) {\n    const processInstrumentation = async (\n      name: string,\n      prop: 'nodeJs' | 'edge'\n    ) => {\n      const key = getEntryKey('root', 'server', name)\n\n      const writtenEndpoint = await instrumentation[prop].writeToDisk()\n      processIssues(currentEntryIssues, key, writtenEndpoint, false, logErrors)\n    }\n    await processInstrumentation('instrumentation.nodeJs', 'nodeJs')\n    await processInstrumentation('instrumentation.edge', 'edge')\n    await manifestLoader.loadMiddlewareManifest(\n      'instrumentation',\n      'instrumentation'\n    )\n    await manifestLoader.writeManifests({\n      devRewrites: undefined,\n      productionRewrites,\n      entrypoints: currentEntrypoints,\n    })\n  }\n\n  if (middleware) {\n    const key = getEntryKey('root', 'server', 'middleware')\n\n    const endpoint = middleware.endpoint\n\n    async function processMiddleware() {\n      const writtenEndpoint = await endpoint.writeToDisk()\n      processIssues(currentEntryIssues, key, writtenEndpoint, false, logErrors)\n      await manifestLoader.loadMiddlewareManifest('middleware', 'middleware')\n    }\n    await processMiddleware()\n  } else {\n    manifestLoader.deleteMiddlewareManifest(\n      getEntryKey('root', 'server', 'middleware')\n    )\n  }\n}\n\nexport async function handlePagesErrorRoute({\n  currentEntryIssues,\n  entrypoints,\n  manifestLoader,\n  productionRewrites,\n  logErrors,\n}: {\n  currentEntryIssues: EntryIssuesMap\n  entrypoints: Entrypoints\n  manifestLoader: TurbopackManifestLoader\n  productionRewrites: CustomRoutes['rewrites'] | undefined\n  logErrors: boolean\n}) {\n  if (entrypoints.global.app) {\n    const key = getEntryKey('pages', 'server', '_app')\n    const writtenEndpoint = await entrypoints.global.app.writeToDisk()\n    processIssues(currentEntryIssues, key, writtenEndpoint, false, logErrors)\n  }\n  await manifestLoader.loadBuildManifest('_app')\n  await manifestLoader.loadPagesManifest('_app')\n  await manifestLoader.loadFontManifest('_app')\n\n  if (entrypoints.global.document) {\n    const key = getEntryKey('pages', 'server', '_document')\n    const writtenEndpoint = await entrypoints.global.document.writeToDisk()\n    processIssues(currentEntryIssues, key, writtenEndpoint, false, logErrors)\n  }\n  await manifestLoader.loadPagesManifest('_document')\n\n  if (entrypoints.global.error) {\n    const key = getEntryKey('pages', 'server', '_error')\n    const writtenEndpoint = await entrypoints.global.error.writeToDisk()\n    processIssues(currentEntryIssues, key, writtenEndpoint, false, logErrors)\n  }\n\n  await manifestLoader.loadBuildManifest('_error')\n  await manifestLoader.loadPagesManifest('_error')\n  await manifestLoader.loadFontManifest('_error')\n\n  await manifestLoader.writeManifests({\n    devRewrites: undefined,\n    productionRewrites,\n    entrypoints,\n  })\n}\n\nexport async function handleRouteType({\n  page,\n  route,\n  currentEntryIssues,\n  entrypoints,\n  manifestLoader,\n  productionRewrites,\n  logErrors,\n}: {\n  page: string\n  route: PageRoute | AppRoute\n\n  currentEntryIssues: EntryIssuesMap\n  entrypoints: Entrypoints\n  manifestLoader: TurbopackManifestLoader\n  productionRewrites: CustomRoutes['rewrites'] | undefined\n  logErrors: boolean\n}) {\n  const shouldCreateWebpackStats = process.env.TURBOPACK_STATS != null\n\n  switch (route.type) {\n    case 'page': {\n      const serverKey = getEntryKey('pages', 'server', page)\n\n      if (entrypoints.global.app) {\n        const key = getEntryKey('pages', 'server', '_app')\n\n        const writtenEndpoint = await entrypoints.global.app.writeToDisk()\n        processIssues(\n          currentEntryIssues,\n          key,\n          writtenEndpoint,\n          false,\n          logErrors\n        )\n      }\n      await manifestLoader.loadBuildManifest('_app')\n      await manifestLoader.loadPagesManifest('_app')\n\n      if (entrypoints.global.document) {\n        const key = getEntryKey('pages', 'server', '_document')\n\n        const writtenEndpoint = await entrypoints.global.document.writeToDisk()\n        processIssues(\n          currentEntryIssues,\n          key,\n          writtenEndpoint,\n          false,\n          logErrors\n        )\n      }\n      await manifestLoader.loadPagesManifest('_document')\n\n      const writtenEndpoint = await route.htmlEndpoint.writeToDisk()\n\n      const type = writtenEndpoint?.type\n\n      await manifestLoader.loadBuildManifest(page)\n      await manifestLoader.loadPagesManifest(page)\n      if (type === 'edge') {\n        await manifestLoader.loadMiddlewareManifest(page, 'pages')\n      } else {\n        manifestLoader.deleteMiddlewareManifest(serverKey)\n      }\n      await manifestLoader.loadFontManifest('/_app', 'pages')\n      await manifestLoader.loadFontManifest(page, 'pages')\n\n      if (shouldCreateWebpackStats) {\n        await manifestLoader.loadWebpackStats(page, 'pages')\n      }\n\n      await manifestLoader.writeManifests({\n        devRewrites: undefined,\n        productionRewrites,\n        entrypoints,\n      })\n\n      processIssues(\n        currentEntryIssues,\n        serverKey,\n        writtenEndpoint,\n        false,\n        logErrors\n      )\n\n      break\n    }\n    case 'page-api': {\n      const key = getEntryKey('pages', 'server', page)\n\n      const writtenEndpoint = await route.endpoint.writeToDisk()\n\n      const type = writtenEndpoint.type\n\n      await manifestLoader.loadPagesManifest(page)\n      if (type === 'edge') {\n        await manifestLoader.loadMiddlewareManifest(page, 'pages')\n      } else {\n        manifestLoader.deleteMiddlewareManifest(key)\n      }\n\n      await manifestLoader.writeManifests({\n        devRewrites: undefined,\n        productionRewrites,\n        entrypoints,\n      })\n\n      processIssues(currentEntryIssues, key, writtenEndpoint, true, logErrors)\n\n      break\n    }\n    case 'app-page': {\n      const key = getEntryKey('app', 'server', page)\n      const writtenEndpoint = await route.htmlEndpoint.writeToDisk()\n      const type = writtenEndpoint.type\n\n      if (type === 'edge') {\n        await manifestLoader.loadMiddlewareManifest(page, 'app')\n      } else {\n        manifestLoader.deleteMiddlewareManifest(key)\n      }\n\n      await manifestLoader.loadAppBuildManifest(page)\n      await manifestLoader.loadBuildManifest(page, 'app')\n      await manifestLoader.loadAppPathsManifest(page)\n      await manifestLoader.loadActionManifest(page)\n      await manifestLoader.loadFontManifest(page, 'app')\n\n      if (shouldCreateWebpackStats) {\n        await manifestLoader.loadWebpackStats(page, 'app')\n      }\n\n      await manifestLoader.writeManifests({\n        devRewrites: undefined,\n        productionRewrites,\n        entrypoints,\n      })\n\n      processIssues(currentEntryIssues, key, writtenEndpoint, false, logErrors)\n\n      break\n    }\n    case 'app-route': {\n      const key = getEntryKey('app', 'server', page)\n      const writtenEndpoint = await route.endpoint.writeToDisk()\n      const type = writtenEndpoint.type\n\n      await manifestLoader.loadAppPathsManifest(page)\n\n      if (type === 'edge') {\n        await manifestLoader.loadMiddlewareManifest(page, 'app')\n      } else {\n        manifestLoader.deleteMiddlewareManifest(key)\n      }\n\n      await manifestLoader.writeManifests({\n        devRewrites: undefined,\n        productionRewrites,\n        entrypoints,\n      })\n      processIssues(currentEntryIssues, key, writtenEndpoint, true, logErrors)\n\n      break\n    }\n    default: {\n      throw new Error(`unknown route type ${(route as any).type} for ${page}`)\n    }\n  }\n}\n"], "names": ["Log", "getEntry<PERSON>ey", "processIssues", "handleEntrypoints", "entrypoints", "currentEntrypoints", "currentEntryIssues", "manifest<PERSON><PERSON>der", "productionRewrites", "logErrors", "global", "app", "pagesAppEndpoint", "document", "pagesDocumentEndpoint", "error", "pagesErrorEndpoint", "instrumentation", "page", "clear", "pathname", "route", "routes", "type", "set", "pages", "for<PERSON>ach", "originalName", "info", "middleware", "key", "delete", "processInstrumentation", "name", "prop", "writtenEndpoint", "writeToDisk", "loadMiddlewareManifest", "writeManifests", "devRewrites", "undefined", "endpoint", "processMiddleware", "deleteMiddlewareManifest", "handlePagesErrorRoute", "loadBuildManifest", "loadPagesManifest", "loadFontManifest", "handleRouteType", "shouldCreateWebpackStats", "process", "env", "TURBOPACK_STATS", "server<PERSON>ey", "htmlEndpoint", "loadWebpackStats", "loadAppBuildManifest", "loadAppPathsManifest", "loadActionManifest", "Error"], "mappings": "AASA,YAAYA,SAAS,eAAc;AACnC,SAASC,WAAW,QAAQ,oCAAmC;AAC/D,SACEC,aAAa,QAER,gCAA+B;AAEtC,OAAO,eAAeC,kBAAkB,EACtCC,WAAW,EACXC,kBAAkB,EAClBC,kBAAkB,EAClBC,cAAc,EACdC,kBAAkB,EAClBC,SAAS,EAQV;IACCJ,mBAAmBK,MAAM,CAACC,GAAG,GAAGP,YAAYQ,gBAAgB;IAC5DP,mBAAmBK,MAAM,CAACG,QAAQ,GAAGT,YAAYU,qBAAqB;IACtET,mBAAmBK,MAAM,CAACK,KAAK,GAAGX,YAAYY,kBAAkB;IAEhEX,mBAAmBK,MAAM,CAACO,eAAe,GAAGb,YAAYa,eAAe;IAEvEZ,mBAAmBa,IAAI,CAACC,KAAK;IAC7Bd,mBAAmBM,GAAG,CAACQ,KAAK;IAE5B,KAAK,MAAM,CAACC,UAAUC,MAAM,IAAIjB,YAAYkB,MAAM,CAAE;QAClD,OAAQD,MAAME,IAAI;YAChB,KAAK;YACL,KAAK;gBACHlB,mBAAmBa,IAAI,CAACM,GAAG,CAACJ,UAAUC;gBACtC;YACF,KAAK;gBAAY;oBACfA,MAAMI,KAAK,CAACC,OAAO,CAAC,CAACR;wBACnBb,mBAAmBM,GAAG,CAACa,GAAG,CAACN,KAAKS,YAAY,EAAE;4BAC5CJ,MAAM;4BACN,GAAGL,IAAI;wBACT;oBACF;oBACA;gBACF;YACA,KAAK;gBAAa;oBAChBb,mBAAmBM,GAAG,CAACa,GAAG,CAACH,MAAMM,YAAY,EAAEN;oBAC/C;gBACF;YACA;gBACErB,IAAI4B,IAAI,CAAC,CAAC,SAAS,EAAER,SAAS,EAAE,EAAEC,MAAME,IAAI,CAAC,CAAC,CAAC;gBAC/C;QACJ;IACF;IAEA,MAAM,EAAEM,UAAU,EAAEZ,eAAe,EAAE,GAAGb;IAExC,8DAA8D;IAC9D,8DAA8D;IAC9D,sCAAsC;IACtC,IAAIC,mBAAmBK,MAAM,CAACmB,UAAU,IAAI,CAACA,YAAY;QACvD,MAAMC,MAAM7B,YAAY,QAAQ,UAAU;QAC1C,wCAAwC;QACxCK,mBAAmByB,MAAM,CAACD;IAC5B;IAEAzB,mBAAmBK,MAAM,CAACmB,UAAU,GAAGA;IAEvC,IAAIZ,iBAAiB;QACnB,MAAMe,yBAAyB,OAC7BC,MACAC;YAEA,MAAMJ,MAAM7B,YAAY,QAAQ,UAAUgC;YAE1C,MAAME,kBAAkB,MAAMlB,eAAe,CAACiB,KAAK,CAACE,WAAW;YAC/DlC,cAAcI,oBAAoBwB,KAAKK,iBAAiB,OAAO1B;QACjE;QACA,MAAMuB,uBAAuB,0BAA0B;QACvD,MAAMA,uBAAuB,wBAAwB;QACrD,MAAMzB,eAAe8B,sBAAsB,CACzC,mBACA;QAEF,MAAM9B,eAAe+B,cAAc,CAAC;YAClCC,aAAaC;YACbhC;YACAJ,aAAaC;QACf;IACF;IAEA,IAAIwB,YAAY;QACd,MAAMC,MAAM7B,YAAY,QAAQ,UAAU;QAE1C,MAAMwC,WAAWZ,WAAWY,QAAQ;QAEpC,eAAeC;YACb,MAAMP,kBAAkB,MAAMM,SAASL,WAAW;YAClDlC,cAAcI,oBAAoBwB,KAAKK,iBAAiB,OAAO1B;YAC/D,MAAMF,eAAe8B,sBAAsB,CAAC,cAAc;QAC5D;QACA,MAAMK;IACR,OAAO;QACLnC,eAAeoC,wBAAwB,CACrC1C,YAAY,QAAQ,UAAU;IAElC;AACF;AAEA,OAAO,eAAe2C,sBAAsB,EAC1CtC,kBAAkB,EAClBF,WAAW,EACXG,cAAc,EACdC,kBAAkB,EAClBC,SAAS,EAOV;IACC,IAAIL,YAAYM,MAAM,CAACC,GAAG,EAAE;QAC1B,MAAMmB,MAAM7B,YAAY,SAAS,UAAU;QAC3C,MAAMkC,kBAAkB,MAAM/B,YAAYM,MAAM,CAACC,GAAG,CAACyB,WAAW;QAChElC,cAAcI,oBAAoBwB,KAAKK,iBAAiB,OAAO1B;IACjE;IACA,MAAMF,eAAesC,iBAAiB,CAAC;IACvC,MAAMtC,eAAeuC,iBAAiB,CAAC;IACvC,MAAMvC,eAAewC,gBAAgB,CAAC;IAEtC,IAAI3C,YAAYM,MAAM,CAACG,QAAQ,EAAE;QAC/B,MAAMiB,MAAM7B,YAAY,SAAS,UAAU;QAC3C,MAAMkC,kBAAkB,MAAM/B,YAAYM,MAAM,CAACG,QAAQ,CAACuB,WAAW;QACrElC,cAAcI,oBAAoBwB,KAAKK,iBAAiB,OAAO1B;IACjE;IACA,MAAMF,eAAeuC,iBAAiB,CAAC;IAEvC,IAAI1C,YAAYM,MAAM,CAACK,KAAK,EAAE;QAC5B,MAAMe,MAAM7B,YAAY,SAAS,UAAU;QAC3C,MAAMkC,kBAAkB,MAAM/B,YAAYM,MAAM,CAACK,KAAK,CAACqB,WAAW;QAClElC,cAAcI,oBAAoBwB,KAAKK,iBAAiB,OAAO1B;IACjE;IAEA,MAAMF,eAAesC,iBAAiB,CAAC;IACvC,MAAMtC,eAAeuC,iBAAiB,CAAC;IACvC,MAAMvC,eAAewC,gBAAgB,CAAC;IAEtC,MAAMxC,eAAe+B,cAAc,CAAC;QAClCC,aAAaC;QACbhC;QACAJ;IACF;AACF;AAEA,OAAO,eAAe4C,gBAAgB,EACpC9B,IAAI,EACJG,KAAK,EACLf,kBAAkB,EAClBF,WAAW,EACXG,cAAc,EACdC,kBAAkB,EAClBC,SAAS,EAUV;IACC,MAAMwC,2BAA2BC,QAAQC,GAAG,CAACC,eAAe,IAAI;IAEhE,OAAQ/B,MAAME,IAAI;QAChB,KAAK;YAAQ;gBACX,MAAM8B,YAAYpD,YAAY,SAAS,UAAUiB;gBAEjD,IAAId,YAAYM,MAAM,CAACC,GAAG,EAAE;oBAC1B,MAAMmB,MAAM7B,YAAY,SAAS,UAAU;oBAE3C,MAAMkC,kBAAkB,MAAM/B,YAAYM,MAAM,CAACC,GAAG,CAACyB,WAAW;oBAChElC,cACEI,oBACAwB,KACAK,iBACA,OACA1B;gBAEJ;gBACA,MAAMF,eAAesC,iBAAiB,CAAC;gBACvC,MAAMtC,eAAeuC,iBAAiB,CAAC;gBAEvC,IAAI1C,YAAYM,MAAM,CAACG,QAAQ,EAAE;oBAC/B,MAAMiB,MAAM7B,YAAY,SAAS,UAAU;oBAE3C,MAAMkC,kBAAkB,MAAM/B,YAAYM,MAAM,CAACG,QAAQ,CAACuB,WAAW;oBACrElC,cACEI,oBACAwB,KACAK,iBACA,OACA1B;gBAEJ;gBACA,MAAMF,eAAeuC,iBAAiB,CAAC;gBAEvC,MAAMX,kBAAkB,MAAMd,MAAMiC,YAAY,CAAClB,WAAW;gBAE5D,MAAMb,OAAOY,mCAAAA,gBAAiBZ,IAAI;gBAElC,MAAMhB,eAAesC,iBAAiB,CAAC3B;gBACvC,MAAMX,eAAeuC,iBAAiB,CAAC5B;gBACvC,IAAIK,SAAS,QAAQ;oBACnB,MAAMhB,eAAe8B,sBAAsB,CAACnB,MAAM;gBACpD,OAAO;oBACLX,eAAeoC,wBAAwB,CAACU;gBAC1C;gBACA,MAAM9C,eAAewC,gBAAgB,CAAC,SAAS;gBAC/C,MAAMxC,eAAewC,gBAAgB,CAAC7B,MAAM;gBAE5C,IAAI+B,0BAA0B;oBAC5B,MAAM1C,eAAegD,gBAAgB,CAACrC,MAAM;gBAC9C;gBAEA,MAAMX,eAAe+B,cAAc,CAAC;oBAClCC,aAAaC;oBACbhC;oBACAJ;gBACF;gBAEAF,cACEI,oBACA+C,WACAlB,iBACA,OACA1B;gBAGF;YACF;QACA,KAAK;YAAY;gBACf,MAAMqB,MAAM7B,YAAY,SAAS,UAAUiB;gBAE3C,MAAMiB,kBAAkB,MAAMd,MAAMoB,QAAQ,CAACL,WAAW;gBAExD,MAAMb,OAAOY,gBAAgBZ,IAAI;gBAEjC,MAAMhB,eAAeuC,iBAAiB,CAAC5B;gBACvC,IAAIK,SAAS,QAAQ;oBACnB,MAAMhB,eAAe8B,sBAAsB,CAACnB,MAAM;gBACpD,OAAO;oBACLX,eAAeoC,wBAAwB,CAACb;gBAC1C;gBAEA,MAAMvB,eAAe+B,cAAc,CAAC;oBAClCC,aAAaC;oBACbhC;oBACAJ;gBACF;gBAEAF,cAAcI,oBAAoBwB,KAAKK,iBAAiB,MAAM1B;gBAE9D;YACF;QACA,KAAK;YAAY;gBACf,MAAMqB,MAAM7B,YAAY,OAAO,UAAUiB;gBACzC,MAAMiB,kBAAkB,MAAMd,MAAMiC,YAAY,CAAClB,WAAW;gBAC5D,MAAMb,OAAOY,gBAAgBZ,IAAI;gBAEjC,IAAIA,SAAS,QAAQ;oBACnB,MAAMhB,eAAe8B,sBAAsB,CAACnB,MAAM;gBACpD,OAAO;oBACLX,eAAeoC,wBAAwB,CAACb;gBAC1C;gBAEA,MAAMvB,eAAeiD,oBAAoB,CAACtC;gBAC1C,MAAMX,eAAesC,iBAAiB,CAAC3B,MAAM;gBAC7C,MAAMX,eAAekD,oBAAoB,CAACvC;gBAC1C,MAAMX,eAAemD,kBAAkB,CAACxC;gBACxC,MAAMX,eAAewC,gBAAgB,CAAC7B,MAAM;gBAE5C,IAAI+B,0BAA0B;oBAC5B,MAAM1C,eAAegD,gBAAgB,CAACrC,MAAM;gBAC9C;gBAEA,MAAMX,eAAe+B,cAAc,CAAC;oBAClCC,aAAaC;oBACbhC;oBACAJ;gBACF;gBAEAF,cAAcI,oBAAoBwB,KAAKK,iBAAiB,OAAO1B;gBAE/D;YACF;QACA,KAAK;YAAa;gBAChB,MAAMqB,MAAM7B,YAAY,OAAO,UAAUiB;gBACzC,MAAMiB,kBAAkB,MAAMd,MAAMoB,QAAQ,CAACL,WAAW;gBACxD,MAAMb,OAAOY,gBAAgBZ,IAAI;gBAEjC,MAAMhB,eAAekD,oBAAoB,CAACvC;gBAE1C,IAAIK,SAAS,QAAQ;oBACnB,MAAMhB,eAAe8B,sBAAsB,CAACnB,MAAM;gBACpD,OAAO;oBACLX,eAAeoC,wBAAwB,CAACb;gBAC1C;gBAEA,MAAMvB,eAAe+B,cAAc,CAAC;oBAClCC,aAAaC;oBACbhC;oBACAJ;gBACF;gBACAF,cAAcI,oBAAoBwB,KAAKK,iBAAiB,MAAM1B;gBAE9D;YACF;QACA;YAAS;gBACP,MAAM,qBAAkE,CAAlE,IAAIkD,MAAM,CAAC,mBAAmB,EAAE,AAACtC,MAAcE,IAAI,CAAC,KAAK,EAAEL,MAAM,GAAjE,qBAAA;2BAAA;gCAAA;kCAAA;gBAAiE;YACzE;IACF;AACF"}