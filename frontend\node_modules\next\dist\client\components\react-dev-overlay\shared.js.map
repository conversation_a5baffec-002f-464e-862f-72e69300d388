{"version": 3, "sources": ["../../../../src/client/components/react-dev-overlay/shared.ts"], "sourcesContent": ["import { useReducer } from 'react'\n\nimport type { StackFrame } from 'next/dist/compiled/stacktrace-parser'\nimport type { VersionInfo } from '../../../server/dev/parse-version-info'\nimport type { SupportedErrorEvent } from './ui/container/runtime-error/render-error'\nimport type { ComponentStackFrame } from './utils/parse-component-stack'\nimport type { DebugInfo } from './types'\nimport type { DevIndicatorServerState } from '../../../server/dev/dev-indicator-server-state'\n\ntype FastRefreshState =\n  /** No refresh in progress. */\n  | { type: 'idle' }\n  /** The refresh process has been triggered, but the new code has not been executed yet. */\n  | { type: 'pending'; errors: SupportedErrorEvent[] }\n\nexport interface OverlayState {\n  nextId: number\n  buildError: string | null\n  errors: SupportedErrorEvent[]\n  refreshState: FastRefreshState\n  rootLayoutMissingTags: typeof window.__next_root_layout_missing_tags\n  versionInfo: VersionInfo\n  notFound: boolean\n  staticIndicator: boolean\n  disableDevIndicator: boolean\n  debugInfo: DebugInfo\n  routerType: 'pages' | 'app'\n}\n\nexport const ACTION_STATIC_INDICATOR = 'static-indicator'\nexport const ACTION_BUILD_OK = 'build-ok'\nexport const ACTION_BUILD_ERROR = 'build-error'\nexport const ACTION_BEFORE_REFRESH = 'before-fast-refresh'\nexport const ACTION_REFRESH = 'fast-refresh'\nexport const ACTION_VERSION_INFO = 'version-info'\nexport const ACTION_UNHANDLED_ERROR = 'unhandled-error'\nexport const ACTION_UNHANDLED_REJECTION = 'unhandled-rejection'\nexport const ACTION_DEBUG_INFO = 'debug-info'\nexport const ACTION_DEV_INDICATOR = 'dev-indicator'\n\nexport const STORAGE_KEY_THEME = '__nextjs-dev-tools-theme'\nexport const STORAGE_KEY_POSITION = '__nextjs-dev-tools-position'\n\ninterface StaticIndicatorAction {\n  type: typeof ACTION_STATIC_INDICATOR\n  staticIndicator: boolean\n}\n\ninterface BuildOkAction {\n  type: typeof ACTION_BUILD_OK\n}\ninterface BuildErrorAction {\n  type: typeof ACTION_BUILD_ERROR\n  message: string\n}\ninterface BeforeFastRefreshAction {\n  type: typeof ACTION_BEFORE_REFRESH\n}\ninterface FastRefreshAction {\n  type: typeof ACTION_REFRESH\n}\n\nexport interface UnhandledErrorAction {\n  type: typeof ACTION_UNHANDLED_ERROR\n  reason: Error\n  frames: StackFrame[]\n  componentStackFrames?: ComponentStackFrame[]\n  warning?: [string, string, string]\n}\nexport interface UnhandledRejectionAction {\n  type: typeof ACTION_UNHANDLED_REJECTION\n  reason: Error\n  frames: StackFrame[]\n}\n\nexport interface DebugInfoAction {\n  type: typeof ACTION_DEBUG_INFO\n  debugInfo: any\n}\n\ninterface VersionInfoAction {\n  type: typeof ACTION_VERSION_INFO\n  versionInfo: VersionInfo\n}\n\ninterface DevIndicatorAction {\n  type: typeof ACTION_DEV_INDICATOR\n  devIndicator: DevIndicatorServerState\n}\n\nexport type BusEvent =\n  | BuildOkAction\n  | BuildErrorAction\n  | BeforeFastRefreshAction\n  | FastRefreshAction\n  | UnhandledErrorAction\n  | UnhandledRejectionAction\n  | VersionInfoAction\n  | StaticIndicatorAction\n  | DebugInfoAction\n  | DevIndicatorAction\n\nfunction pushErrorFilterDuplicates(\n  errors: SupportedErrorEvent[],\n  err: SupportedErrorEvent\n): SupportedErrorEvent[] {\n  return [\n    ...errors.filter((e) => {\n      // Filter out duplicate errors\n      return e.event.reason.stack !== err.event.reason.stack\n    }),\n    err,\n  ]\n}\n\nconst shouldDisableDevIndicator =\n  process.env.__NEXT_DEV_INDICATOR?.toString() === 'false'\n\nexport const INITIAL_OVERLAY_STATE: Omit<OverlayState, 'routerType'> = {\n  nextId: 1,\n  buildError: null,\n  errors: [],\n  notFound: false,\n  staticIndicator: false,\n  // To prevent flickering, set the initial state to disabled.\n  disableDevIndicator: true,\n  refreshState: { type: 'idle' },\n  rootLayoutMissingTags: [],\n  versionInfo: { installed: '0.0.0', staleness: 'unknown' },\n  debugInfo: { devtoolsFrontendUrl: undefined },\n}\n\nfunction getInitialState(\n  routerType: 'pages' | 'app'\n): OverlayState & { routerType: 'pages' | 'app' } {\n  return {\n    ...INITIAL_OVERLAY_STATE,\n    routerType,\n  }\n}\n\nexport function useErrorOverlayReducer(routerType: 'pages' | 'app') {\n  return useReducer((_state: OverlayState, action: BusEvent): OverlayState => {\n    switch (action.type) {\n      case ACTION_DEBUG_INFO: {\n        return { ..._state, debugInfo: action.debugInfo }\n      }\n      case ACTION_STATIC_INDICATOR: {\n        return { ..._state, staticIndicator: action.staticIndicator }\n      }\n      case ACTION_BUILD_OK: {\n        return { ..._state, buildError: null }\n      }\n      case ACTION_BUILD_ERROR: {\n        return { ..._state, buildError: action.message }\n      }\n      case ACTION_BEFORE_REFRESH: {\n        return { ..._state, refreshState: { type: 'pending', errors: [] } }\n      }\n      case ACTION_REFRESH: {\n        return {\n          ..._state,\n          buildError: null,\n          errors:\n            // Errors can come in during updates. In this case, UNHANDLED_ERROR\n            // and UNHANDLED_REJECTION events might be dispatched between the\n            // BEFORE_REFRESH and the REFRESH event. We want to keep those errors\n            // around until the next refresh. Otherwise we run into a race\n            // condition where those errors would be cleared on refresh completion\n            // before they can be displayed.\n            _state.refreshState.type === 'pending'\n              ? _state.refreshState.errors\n              : [],\n          refreshState: { type: 'idle' },\n        }\n      }\n      case ACTION_UNHANDLED_ERROR:\n      case ACTION_UNHANDLED_REJECTION: {\n        switch (_state.refreshState.type) {\n          case 'idle': {\n            return {\n              ..._state,\n              nextId: _state.nextId + 1,\n              errors: pushErrorFilterDuplicates(_state.errors, {\n                id: _state.nextId,\n                event: action,\n              }),\n            }\n          }\n          case 'pending': {\n            return {\n              ..._state,\n              nextId: _state.nextId + 1,\n              refreshState: {\n                ..._state.refreshState,\n                errors: pushErrorFilterDuplicates(_state.refreshState.errors, {\n                  id: _state.nextId,\n                  event: action,\n                }),\n              },\n            }\n          }\n          default:\n            // eslint-disable-next-line @typescript-eslint/no-unused-vars\n            const _: never = _state.refreshState\n            return _state\n        }\n      }\n      case ACTION_VERSION_INFO: {\n        return { ..._state, versionInfo: action.versionInfo }\n      }\n      case ACTION_DEV_INDICATOR: {\n        return {\n          ..._state,\n          disableDevIndicator:\n            shouldDisableDevIndicator || !!action.devIndicator.disabledUntil,\n        }\n      }\n      default: {\n        return _state\n      }\n    }\n  }, getInitialState(routerType))\n}\n\nexport const REACT_REFRESH_FULL_RELOAD_FROM_ERROR =\n  '[Fast Refresh] performing full reload because your application had an unrecoverable error'\n"], "names": ["ACTION_BEFORE_REFRESH", "ACTION_BUILD_ERROR", "ACTION_BUILD_OK", "ACTION_DEBUG_INFO", "ACTION_DEV_INDICATOR", "ACTION_REFRESH", "ACTION_STATIC_INDICATOR", "ACTION_UNHANDLED_ERROR", "ACTION_UNHANDLED_REJECTION", "ACTION_VERSION_INFO", "INITIAL_OVERLAY_STATE", "REACT_REFRESH_FULL_RELOAD_FROM_ERROR", "STORAGE_KEY_POSITION", "STORAGE_KEY_THEME", "useErrorOverlayReducer", "process", "pushErrorFilterDuplicates", "errors", "err", "filter", "e", "event", "reason", "stack", "shouldDisableDevIndicator", "env", "__NEXT_DEV_INDICATOR", "toString", "nextId", "buildError", "notFound", "staticIndicator", "disableDevIndicator", "refreshState", "type", "rootLayoutMissingTags", "versionInfo", "installed", "staleness", "debugInfo", "devtoolsFrontendUrl", "undefined", "getInitialState", "routerType", "useReducer", "_state", "action", "message", "id", "_", "devIndicator", "disabledUntil"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;IAgCaA,qBAAqB;eAArBA;;IADAC,kBAAkB;eAAlBA;;IADAC,eAAe;eAAfA;;IAOAC,iBAAiB;eAAjBA;;IACAC,oBAAoB;eAApBA;;IALAC,cAAc;eAAdA;;IAJAC,uBAAuB;eAAvBA;;IAMAC,sBAAsB;eAAtBA;;IACAC,0BAA0B;eAA1BA;;IAFAC,mBAAmB;eAAnBA;;IAoFAC,qBAAqB;eAArBA;;IA2GAC,oCAAoC;eAApCA;;IAxLAC,oBAAoB;eAApBA;;IADAC,iBAAiB;eAAjBA;;IAqGGC,sBAAsB;eAAtBA;;;uBA7IW;IAoHzBC;AAvFK,MAAMT,0BAA0B;AAChC,MAAMJ,kBAAkB;AACxB,MAAMD,qBAAqB;AAC3B,MAAMD,wBAAwB;AAC9B,MAAMK,iBAAiB;AACvB,MAAMI,sBAAsB;AAC5B,MAAMF,yBAAyB;AAC/B,MAAMC,6BAA6B;AACnC,MAAML,oBAAoB;AAC1B,MAAMC,uBAAuB;AAE7B,MAAMS,oBAAoB;AAC1B,MAAMD,uBAAuB;AA6DpC,SAASI,0BACPC,MAA6B,EAC7BC,GAAwB;IAExB,OAAO;WACFD,OAAOE,MAAM,CAAC,CAACC;YAChB,8BAA8B;YAC9B,OAAOA,EAAEC,KAAK,CAACC,MAAM,CAACC,KAAK,KAAKL,IAAIG,KAAK,CAACC,MAAM,CAACC,KAAK;QACxD;QACAL;KACD;AACH;AAEA,MAAMM,4BACJT,EAAAA,oCAAAA,QAAQU,GAAG,CAACC,oBAAoB,qBAAhCX,kCAAkCY,QAAQ,QAAO;AAE5C,MAAMjB,wBAA0D;IACrEkB,QAAQ;IACRC,YAAY;IACZZ,QAAQ,EAAE;IACVa,UAAU;IACVC,iBAAiB;IACjB,4DAA4D;IAC5DC,qBAAqB;IACrBC,cAAc;QAAEC,MAAM;IAAO;IAC7BC,uBAAuB,EAAE;IACzBC,aAAa;QAAEC,WAAW;QAASC,WAAW;IAAU;IACxDC,WAAW;QAAEC,qBAAqBC;IAAU;AAC9C;AAEA,SAASC,gBACPC,UAA2B;IAE3B,OAAO;QACL,GAAGjC,qBAAqB;QACxBiC;IACF;AACF;AAEO,SAAS7B,uBAAuB6B,UAA2B;IAChE,OAAOC,IAAAA,iBAAU,EAAC,CAACC,QAAsBC;QACvC,OAAQA,OAAOZ,IAAI;YACjB,KAAK/B;gBAAmB;oBACtB,OAAO;wBAAE,GAAG0C,MAAM;wBAAEN,WAAWO,OAAOP,SAAS;oBAAC;gBAClD;YACA,KAAKjC;gBAAyB;oBAC5B,OAAO;wBAAE,GAAGuC,MAAM;wBAAEd,iBAAiBe,OAAOf,eAAe;oBAAC;gBAC9D;YACA,KAAK7B;gBAAiB;oBACpB,OAAO;wBAAE,GAAG2C,MAAM;wBAAEhB,YAAY;oBAAK;gBACvC;YACA,KAAK5B;gBAAoB;oBACvB,OAAO;wBAAE,GAAG4C,MAAM;wBAAEhB,YAAYiB,OAAOC,OAAO;oBAAC;gBACjD;YACA,KAAK/C;gBAAuB;oBAC1B,OAAO;wBAAE,GAAG6C,MAAM;wBAAEZ,cAAc;4BAAEC,MAAM;4BAAWjB,QAAQ,EAAE;wBAAC;oBAAE;gBACpE;YACA,KAAKZ;gBAAgB;oBACnB,OAAO;wBACL,GAAGwC,MAAM;wBACThB,YAAY;wBACZZ,QACE,mEAAmE;wBACnE,iEAAiE;wBACjE,qEAAqE;wBACrE,8DAA8D;wBAC9D,sEAAsE;wBACtE,gCAAgC;wBAChC4B,OAAOZ,YAAY,CAACC,IAAI,KAAK,YACzBW,OAAOZ,YAAY,CAAChB,MAAM,GAC1B,EAAE;wBACRgB,cAAc;4BAAEC,MAAM;wBAAO;oBAC/B;gBACF;YACA,KAAK3B;YACL,KAAKC;gBAA4B;oBAC/B,OAAQqC,OAAOZ,YAAY,CAACC,IAAI;wBAC9B,KAAK;4BAAQ;gCACX,OAAO;oCACL,GAAGW,MAAM;oCACTjB,QAAQiB,OAAOjB,MAAM,GAAG;oCACxBX,QAAQD,0BAA0B6B,OAAO5B,MAAM,EAAE;wCAC/C+B,IAAIH,OAAOjB,MAAM;wCACjBP,OAAOyB;oCACT;gCACF;4BACF;wBACA,KAAK;4BAAW;gCACd,OAAO;oCACL,GAAGD,MAAM;oCACTjB,QAAQiB,OAAOjB,MAAM,GAAG;oCACxBK,cAAc;wCACZ,GAAGY,OAAOZ,YAAY;wCACtBhB,QAAQD,0BAA0B6B,OAAOZ,YAAY,CAAChB,MAAM,EAAE;4CAC5D+B,IAAIH,OAAOjB,MAAM;4CACjBP,OAAOyB;wCACT;oCACF;gCACF;4BACF;wBACA;4BACE,6DAA6D;4BAC7D,MAAMG,IAAWJ,OAAOZ,YAAY;4BACpC,OAAOY;oBACX;gBACF;YACA,KAAKpC;gBAAqB;oBACxB,OAAO;wBAAE,GAAGoC,MAAM;wBAAET,aAAaU,OAAOV,WAAW;oBAAC;gBACtD;YACA,KAAKhC;gBAAsB;oBACzB,OAAO;wBACL,GAAGyC,MAAM;wBACTb,qBACER,6BAA6B,CAAC,CAACsB,OAAOI,YAAY,CAACC,aAAa;oBACpE;gBACF;YACA;gBAAS;oBACP,OAAON;gBACT;QACF;IACF,GAAGH,gBAAgBC;AACrB;AAEO,MAAMhC,uCACX"}