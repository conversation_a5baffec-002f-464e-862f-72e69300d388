{"version": 3, "sources": ["../../src/lib/needs-experimental-react.ts"], "sourcesContent": ["import type { NextConfig } from '../server/config-shared'\n\nexport function needsExperimentalReact(config: NextConfig) {\n  const { ppr, taint, viewTransition } = config.experimental || {}\n  return Boolean(ppr || taint || viewTransition)\n}\n"], "names": ["needsExperimentalReact", "config", "ppr", "taint", "viewTransition", "experimental", "Boolean"], "mappings": ";;;;+BAEgBA;;;eAAAA;;;AAAT,SAASA,uBAAuBC,MAAkB;IACvD,MAAM,EAAEC,GAAG,EAAEC,KAAK,EAAEC,cAAc,EAAE,GAAGH,OAAOI,YAAY,IAAI,CAAC;IAC/D,OAAOC,QAAQJ,OAAOC,SAASC;AACjC"}