"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[146],{175:(e,t,r)=>{r.d(t,{Q:()=>o});let n=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function o(e){if("string"!=typeof e||e.includes("-"));else if(n.indexOf(e)>-1||/[A-Z]/u.test(e))return!0;return!1}},491:(e,t,r)=>{r.d(t,{$:()=>l});var n=new Set(["id","type","style","title","role","tabIndex","htmlFor","width","height","abbr","accept","acceptCharset","accessKey","action","allowFullScreen","allowTransparency","alt","async","autoComplete","autoFocus","autoPlay","cellPadding","cellSpacing","challenge","charset","checked","cite","class","className","cols","colSpan","command","content","contentEditable","contextMenu","controls","coords","crossOrigin","data","dateTime","default","defer","dir","disabled","download","draggable","dropzone","encType","enterKeyHint","for","form","formAction","formEncType","formMethod","formNoValidate","formTarget","frameBorder","headers","hidden","high","href","hrefLang","httpEquiv","icon","inputMode","isMap","itemId","itemProp","itemRef","itemScope","itemType","kind","label","lang","list","loop","manifest","max","maxLength","media","mediaGroup","method","min","minLength","multiple","muted","name","noValidate","open","optimum","pattern","ping","placeholder","poster","preload","radioGroup","referrerPolicy","readOnly","rel","required","rows","rowSpan","sandbox","scope","scoped","scrolling","seamless","selected","shape","size","sizes","slot","sortable","span","spellCheck","src","srcDoc","srcSet","start","step","target","translate","typeMustMatch","useMap","value","wmode","wrap"]),o=new Set(["onCopy","onCut","onPaste","onLoad","onError","onWheel","onScroll","onCompositionEnd","onCompositionStart","onCompositionUpdate","onKeyDown","onKeyPress","onKeyUp","onFocus","onBlur","onChange","onInput","onSubmit","onClick","onContextMenu","onDoubleClick","onDrag","onDragEnd","onDragEnter","onDragExit","onDragLeave","onDragOver","onDragStart","onDrop","onMouseDown","onMouseEnter","onMouseLeave","onMouseMove","onMouseOut","onMouseOver","onMouseUp","onPointerDown","onPointerEnter","onPointerLeave","onPointerUp","onSelect","onTouchCancel","onTouchEnd","onTouchMove","onTouchStart","onAnimationStart","onAnimationEnd","onAnimationIteration","onTransitionEnd"]),a=/^(data-.*)$/,i=/^(aria-.*)$/,s=/^(on[A-Z].*)$/;function l(e,t={}){let{labelable:r=!0,enabled:u=!0,propNames:d,omitPropNames:c,omitEventNames:f,omitDataProps:p,omitEventProps:g}=t,m={};if(!u)return e;for(let t in e)!((null==c?void 0:c.has(t))||(null==f?void 0:f.has(t))&&s.test(t)||s.test(t)&&!o.has(t)||p&&a.test(t)||g&&s.test(t))&&(Object.prototype.hasOwnProperty.call(e,t)&&(n.has(t)||r&&i.test(t)||(null==d?void 0:d.has(t))||a.test(t))||s.test(t))&&(m[t]=e[t]);return m}},660:(e,t,r)=>{r.d(t,{y:()=>s});var n=r(12115),o=r(33205),a=r(32047);class i{isDefaultPrevented(){return this.nativeEvent.defaultPrevented}preventDefault(){this.defaultPrevented=!0,this.nativeEvent.preventDefault()}stopPropagation(){this.nativeEvent.stopPropagation(),this.isPropagationStopped=()=>!0}isPropagationStopped(){return!1}persist(){}constructor(e,t){this.nativeEvent=t,this.target=t.target,this.currentTarget=t.currentTarget,this.relatedTarget=t.relatedTarget,this.bubbles=t.bubbles,this.cancelable=t.cancelable,this.defaultPrevented=t.defaultPrevented,this.eventPhase=t.eventPhase,this.isTrusted=t.isTrusted,this.timeStamp=t.timeStamp,this.type=e}}function s(e){let t=(0,n.useRef)({isFocused:!1,observer:null});(0,o.N)(()=>{let e=t.current;return()=>{e.observer&&(e.observer.disconnect(),e.observer=null)}},[]);let r=(0,a.J)(t=>{null==e||e(t)});return(0,n.useCallback)(e=>{if(e.target instanceof HTMLButtonElement||e.target instanceof HTMLInputElement||e.target instanceof HTMLTextAreaElement||e.target instanceof HTMLSelectElement){t.current.isFocused=!0;let n=e.target;n.addEventListener("focusout",e=>{t.current.isFocused=!1,n.disabled&&r(new i("blur",e)),t.current.observer&&(t.current.observer.disconnect(),t.current.observer=null)},{once:!0}),t.current.observer=new MutationObserver(()=>{if(t.current.isFocused&&n.disabled){var e;null===(e=t.current.observer)||void 0===e||e.disconnect();let r=n===document.activeElement?null:document.activeElement;n.dispatchEvent(new FocusEvent("blur",{relatedTarget:r})),n.dispatchEvent(new FocusEvent("focusout",{bubbles:!0,relatedTarget:r}))}}),t.current.observer.observe(n,{attributes:!0,attributeFilter:["disabled"]})}},[r])}},672:(e,t,r)=>{function n(e){return Array.isArray(e)}function o(e){let t=typeof e;return null!=e&&("object"===t||"function"===t)&&!n(e)}function a(e){return n(e)?n(e)&&0===e.length:o(e)?o(e)&&0===Object.keys(e).length:null==e||""===e}function i(e){return"function"==typeof e}r.d(t,{Im:()=>a,Tn:()=>i,sE:()=>s});var s=e=>e?"true":void 0},2735:(e,t,r)=>{function n(e){let t=[{},{}];return e?.values.forEach((e,r)=>{t[0][r]=e.get(),t[1][r]=e.getVelocity()}),t}function o(e,t,r,o){if("function"==typeof t){let[a,i]=n(o);t=t(void 0!==r?r:e.custom,a,i)}if("string"==typeof t&&(t=e.variants&&e.variants[t]),"function"==typeof t){let[a,i]=n(o);t=t(void 0!==r?r:e.custom,a,i)}return t}r.d(t,{a:()=>o})},5712:(e,t,r)=>{r.d(t,{$:()=>n});function n(...e){for(var t,r,o=0,a="";o<e.length;)(t=e[o++])&&(r=function e(t){var r,n,o="";if("string"==typeof t||"number"==typeof t)o+=t;else if("object"==typeof t){if(Array.isArray(t))for(r=0;r<t.length;r++)t[r]&&(n=e(t[r]))&&(o&&(o+=" "),o+=n);else for(r in t)t[r]&&(o&&(o+=" "),o+=r)}return o}(t))&&(a&&(a+=" "),a+=r);return a}},6340:(e,t,r)=>{r.d(t,{N:()=>n});function n(e){return null!==e&&"object"==typeof e&&"function"==typeof e.start}},6548:(e,t,r)=>{r.d(t,{zD:()=>o});var n=r(12115);function o(e){let t=(0,n.useRef)(null);return(0,n.useImperativeHandle)(e,()=>t.current),t}"undefined"!=typeof window&&window.document&&window.document.createElement},6642:(e,t,r)=>{r.d(t,{B:()=>o});let n={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},o={};for(let e in n)o[e]={isEnabled:t=>n[e].some(e=>!!t[e])}},7506:(e,t,r)=>{r.d(t,{A:()=>o});var n=r(12115);function o(){let e=(0,n.useRef)(new Map),t=(0,n.useCallback)((t,r,n,o)=>{let a=(null==o?void 0:o.once)?(...t)=>{e.current.delete(n),n(...t)}:n;e.current.set(n,{type:r,eventTarget:t,fn:a,options:o}),t.addEventListener(r,n,o)},[]),r=(0,n.useCallback)((t,r,n,o)=>{var a;let i=(null===(a=e.current.get(n))||void 0===a?void 0:a.fn)||n;t.removeEventListener(r,i,o),e.current.delete(n)},[]),o=(0,n.useCallback)(()=>{e.current.forEach((e,t)=>{r(e.eventTarget,e.type,t,e.options)})},[r]);return(0,n.useEffect)(()=>o,[o]),{addGlobalListener:t,removeGlobalListener:r,removeAllGlobalListeners:o}}},7684:(e,t,r)=>{r.d(t,{O:()=>u});var n=r(60018),o=r(78606);let a=(e,t)=>t&&"number"==typeof e?t.transform(e):e;var i=r(72403);let s={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},l=n.U.length;function u(e,t,r){let{style:u,vars:d,transformOrigin:c}=e,f=!1,p=!1;for(let e in t){let r=t[e];if(n.f.has(e)){f=!0;continue}if((0,o.j)(e)){d[e]=r;continue}{let t=a(r,i.W[e]);e.startsWith("origin")?(p=!0,c[e]=t):u[e]=t}}if(!t.transform&&(f||r?u.transform=function(e,t,r){let o="",u=!0;for(let d=0;d<l;d++){let l=n.U[d],c=e[l];if(void 0===c)continue;let f=!0;if(!(f="number"==typeof c?c===+!!l.startsWith("scale"):0===parseFloat(c))||r){let e=a(c,i.W[l]);if(!f){u=!1;let t=s[l]||l;o+=`${t}(${e}) `}r&&(t[l]=e)}}return o=o.trim(),r?o=r(t,u?"":o):u&&(o="none"),o}(t,e.transform,r):u.transform&&(u.transform="none")),p){let{originX:e="50%",originY:t="50%",originZ:r=0}=c;u.transformOrigin=`${e} ${t} ${r}`}}},9480:(e,t,r)=>{r.d(t,{Y:()=>o});var n=r(6642);function o(e){for(let t in e)n.B[t]={...n.B[t],...e[t]}}},9539:(e,t,r)=>{r.d(t,{j:()=>u});var n=r(51251),o=r(60760),a=r(14356),i=r(95155),s=()=>Promise.all([r.e(829),r.e(224)]).then(r.bind(r,49224)).then(e=>e.default),l=e=>{let{ripples:t=[],motionProps:r,color:l="currentColor",style:u,onClear:d}=e;return(0,i.jsx)(i.Fragment,{children:t.map(e=>{let t=Math.min(Math.max(.01*e.size,.2),e.size>100?.75:.5);return(0,i.jsx)(n.F,{features:s,children:(0,i.jsx)(o.N,{mode:"popLayout",children:(0,i.jsx)(a.m.span,{animate:{transform:"scale(2)",opacity:0},className:"nextui-ripple",exit:{opacity:0},initial:{transform:"scale(0)",opacity:.35},style:{position:"absolute",backgroundColor:l,borderRadius:"100%",transformOrigin:"center",pointerEvents:"none",overflow:"hidden",inset:0,zIndex:0,top:e.y,left:e.x,width:"".concat(e.size,"px"),height:"".concat(e.size,"px"),...u},transition:{duration:t},onAnimationComplete:()=>{d(e.key)},...r})})},e.key)})})};l.displayName="NextUI.Ripple";var u=l},9906:(e,t,r)=>{r.d(t,{M:()=>u});var n=r(12115);let o=!1,a=0;function i(){o=!0,setTimeout(()=>{o=!1},50)}function s(e){"touch"===e.pointerType&&i()}function l(){if("undefined"!=typeof document)return"undefined"!=typeof PointerEvent?document.addEventListener("pointerup",s):document.addEventListener("touchend",i),a++,()=>{--a>0||("undefined"!=typeof PointerEvent?document.removeEventListener("pointerup",s):document.removeEventListener("touchend",i))}}function u(e){let{onHoverStart:t,onHoverChange:r,onHoverEnd:a,isDisabled:i}=e,[s,u]=(0,n.useState)(!1),d=(0,n.useRef)({isHovered:!1,ignoreEmulatedMouseEvents:!1,pointerType:"",target:null}).current;(0,n.useEffect)(l,[]);let{hoverProps:c,triggerHoverEnd:f}=(0,n.useMemo)(()=>{let e=(e,n)=>{if(d.pointerType=n,i||"touch"===n||d.isHovered||!e.currentTarget.contains(e.target))return;d.isHovered=!0;let o=e.currentTarget;d.target=o,t&&t({type:"hoverstart",target:o,pointerType:n}),r&&r(!0),u(!0)},n=(e,t)=>{if(d.pointerType="",d.target=null,"touch"===t||!d.isHovered)return;d.isHovered=!1;let n=e.currentTarget;a&&a({type:"hoverend",target:n,pointerType:t}),r&&r(!1),u(!1)},s={};return"undefined"!=typeof PointerEvent?(s.onPointerEnter=t=>{o&&"mouse"===t.pointerType||e(t,t.pointerType)},s.onPointerLeave=e=>{!i&&e.currentTarget.contains(e.target)&&n(e,e.pointerType)}):(s.onTouchStart=()=>{d.ignoreEmulatedMouseEvents=!0},s.onMouseEnter=t=>{d.ignoreEmulatedMouseEvents||o||e(t,"mouse"),d.ignoreEmulatedMouseEvents=!1},s.onMouseLeave=e=>{!i&&e.currentTarget.contains(e.target)&&n(e,"mouse")}),{hoverProps:s,triggerHoverEnd:n}},[t,r,a,i,d]);return(0,n.useEffect)(()=>{i&&f({currentTarget:d.target},d.pointerType)},[i]),{hoverProps:c,isHovered:s}}},14060:(e,t,r)=>{r.d(t,{I:()=>n});function n(e){if("undefined"==typeof Proxy)return e;let t=new Map;return new Proxy((...t)=>e(...t),{get:(r,n)=>"create"===n?e:(t.has(n)||t.set(n,e(n)),t.get(n))})}},14356:(e,t,r)=>{r.d(t,{m:()=>a});var n=r(14060);let o=(0,r(53880).C)(),a=(0,n.I)(o)},14570:(e,t,r)=>{r.d(t,{S:()=>n});let n=e=>!!(e&&e.getVelocity)},17575:(e,t,r)=>{r.d(t,{v:()=>i});let n=new Map,o=new Set;function a(){if("undefined"==typeof window)return;function e(e){return"propertyName"in e}let t=r=>{if(!e(r)||!r.target)return;let a=n.get(r.target);if(a&&(a.delete(r.propertyName),0===a.size&&(r.target.removeEventListener("transitioncancel",t),n.delete(r.target)),0===n.size)){for(let e of o)e();o.clear()}};document.body.addEventListener("transitionrun",r=>{if(!e(r)||!r.target)return;let o=n.get(r.target);o||(o=new Set,n.set(r.target,o),r.target.addEventListener("transitioncancel",t,{once:!0})),o.add(r.propertyName)}),document.body.addEventListener("transitionend",t)}function i(e){requestAnimationFrame(()=>{0===n.size?e():o.add(e)})}"undefined"!=typeof document&&("loading"!==document.readyState?a():document.addEventListener("DOMContentLoaded",a))},19253:(e,t,r)=>{r.d(t,{O:()=>s,e:()=>i});var n=r(6340),o=r(65305),a=r(98312);function i(e){return(0,n.N)(e.animate)||a._.some(t=>(0,o.w)(e[t]))}function s(e){return!!(i(e)||e.variants)}},19914:(e,t,r)=>{r.d(t,{d:()=>C});var n=r(66680),o=r(87418),a=r(17575);let i="default",s="",l=new WeakMap;function u(e){if((0,n.un)()){if("default"===i){let t=(0,o.T)(e);s=t.documentElement.style.webkitUserSelect,t.documentElement.style.webkitUserSelect="none"}i="disabled"}else(e instanceof HTMLElement||e instanceof SVGElement)&&(l.set(e,e.style.userSelect),e.style.userSelect="none")}function d(e){if((0,n.un)())"disabled"===i&&(i="restoring",setTimeout(()=>{(0,a.v)(()=>{if("restoring"===i){let t=(0,o.T)(e);"none"===t.documentElement.style.webkitUserSelect&&(t.documentElement.style.webkitUserSelect=s||""),s="",i="default"}})},300));else if((e instanceof HTMLElement||e instanceof SVGElement)&&e&&l.has(e)){let t=l.get(e);"none"===e.style.userSelect&&(e.style.userSelect=t),""===e.getAttribute("style")&&e.removeAttribute("style"),l.delete(e)}}var c=r(22706);function f(e,t,r){if(!t.has(e))throw TypeError("attempted to "+r+" private field on non-instance");return t.get(e)}function p(e,t,r){var n=f(e,t,"set");return!function(e,t,r){if(t.set)t.set.call(e,r);else{if(!t.writable)throw TypeError("attempted to set read only private field");t.value=r}}(e,n,r),r}var g=r(81627),m=r(87826),v=r(7506),b=r(32047),y=r(73750),h=r(22989),w=r(58838),x=r(47024),E=r(12115),T=new WeakMap;class k{continuePropagation(){p(this,T,!1)}get shouldStopPropagation(){var e;return(e=f(this,T,"get")).get?e.get.call(this):e.value}constructor(e,t,r,n){var o;!function(e,t,r){(function(e,t){if(t.has(e))throw TypeError("Cannot initialize the same private elements twice on an object")})(e,t),t.set(e,r)}(this,T,{writable:!0,value:void 0}),p(this,T,!0);let a=null!==(o=null==n?void 0:n.target)&&void 0!==o?o:r.currentTarget,i=null==a?void 0:a.getBoundingClientRect(),s,l=0,u,d=null;null!=r.clientX&&null!=r.clientY&&(u=r.clientX,d=r.clientY),i&&(null!=u&&null!=d?(s=u-i.left,l=d-i.top):(s=i.width/2,l=i.height/2)),this.type=e,this.pointerType=t,this.target=r.currentTarget,this.shiftKey=r.shiftKey,this.metaKey=r.metaKey,this.ctrlKey=r.ctrlKey,this.altKey=r.altKey,this.x=s,this.y=l}}let P=Symbol("linkClicked");function C(e){let{onPress:t,onPressChange:r,onPressStart:a,onPressEnd:i,onPressUp:s,isDisabled:l,isPressed:f,preventFocusOnPress:p,shouldCancelOnPointerExit:T,allowTextSelectionOnPress:C,ref:N,..._}=function(e){let t=(0,E.useContext)(c.F);if(t){let{register:r,...n}=t;e=(0,g.v)(n,e),r()}return(0,m.w)(t,e.ref),e}(e),[D,$]=(0,E.useState)(!1),K=(0,E.useRef)({isPressed:!1,ignoreEmulatedMouseEvents:!1,ignoreClickAfterPress:!1,didFirePressStart:!1,isTriggeringEvent:!1,activePointerId:null,target:null,isOverTarget:!1,pointerType:null}),{addGlobalListener:W,removeAllGlobalListeners:F}=(0,v.A)(),H=(0,b.J)((e,t)=>{let n=K.current;if(l||n.didFirePressStart)return!1;let o=!0;if(n.isTriggeringEvent=!0,a){let r=new k("pressstart",t,e);a(r),o=r.shouldStopPropagation}return r&&r(!0),n.isTriggeringEvent=!1,n.didFirePressStart=!0,$(!0),o}),V=(0,b.J)((e,n,o=!0)=>{let a=K.current;if(!a.didFirePressStart)return!1;a.ignoreClickAfterPress=!0,a.didFirePressStart=!1,a.isTriggeringEvent=!0;let s=!0;if(i){let t=new k("pressend",n,e);i(t),s=t.shouldStopPropagation}if(r&&r(!1),$(!1),t&&o&&!l){let r=new k("press",n,e);t(r),s&&(s=r.shouldStopPropagation)}return a.isTriggeringEvent=!1,s}),G=(0,b.J)((e,t)=>{let r=K.current;if(l)return!1;if(s){r.isTriggeringEvent=!0;let n=new k("pressup",t,e);return s(n),r.isTriggeringEvent=!1,n.shouldStopPropagation}return!0}),B=(0,b.J)(e=>{let t=K.current;t.isPressed&&t.target&&(t.isOverTarget&&null!=t.pointerType&&V(O(t.target,e),t.pointerType,!1),t.isPressed=!1,t.isOverTarget=!1,t.activePointerId=null,t.pointerType=null,F(),C||d(t.target))}),U=(0,b.J)(e=>{T&&B(e)}),X=(0,E.useMemo)(()=>{let e=K.current,t={onKeyDown(t){if(M(t.nativeEvent,t.currentTarget)&&t.currentTarget.contains(t.target)){var a;z(t.target,t.key)&&t.preventDefault();let i=!0;if(!e.isPressed&&!t.repeat){e.target=t.currentTarget,e.isPressed=!0,i=H(t,"keyboard");let n=t.currentTarget;W((0,o.T)(t.currentTarget),"keyup",(0,y.c)(t=>{M(t,n)&&!t.repeat&&n.contains(t.target)&&e.target&&G(O(e.target,t),"keyboard")},r),!0)}i&&t.stopPropagation(),t.metaKey&&(0,n.cX)()&&(null===(a=e.metaKeyEvents)||void 0===a||a.set(t.key,t.nativeEvent))}else"Meta"===t.key&&(e.metaKeyEvents=new Map)},onClick(t){if((!t||t.currentTarget.contains(t.target))&&t&&0===t.button&&!e.isTriggeringEvent&&!h.Fe.isOpening){let r=!0;if(l&&t.preventDefault(),!e.ignoreClickAfterPress&&!e.ignoreEmulatedMouseEvents&&!e.isPressed&&("virtual"===e.pointerType||(0,w.Y)(t.nativeEvent))){l||p||(0,x.e)(t.currentTarget);let e=H(t,"virtual"),n=G(t,"virtual"),o=V(t,"virtual");r=e&&n&&o}e.ignoreEmulatedMouseEvents=!1,e.ignoreClickAfterPress=!1,r&&t.stopPropagation()}}},r=t=>{var r,n,o;if(e.isPressed&&e.target&&M(t,e.target)){z(t.target,t.key)&&t.preventDefault();let r=t.target;V(O(e.target,t),"keyboard",e.target.contains(r)),F(),"Enter"!==t.key&&S(e.target)&&e.target.contains(r)&&!t[P]&&(t[P]=!0,(0,h.Fe)(e.target,t,!1)),e.isPressed=!1,null===(n=e.metaKeyEvents)||void 0===n||n.delete(t.key)}else if("Meta"===t.key&&(null===(r=e.metaKeyEvents)||void 0===r?void 0:r.size)){let t=e.metaKeyEvents;for(let r of(e.metaKeyEvents=void 0,t.values()))null===(o=e.target)||void 0===o||o.dispatchEvent(new KeyboardEvent("keyup",r))}};if("undefined"!=typeof PointerEvent){t.onPointerDown=t=>{if(0!==t.button||!t.currentTarget.contains(t.target))return;if((0,w.P)(t.nativeEvent)){e.pointerType="virtual";return}I(t.currentTarget)&&t.preventDefault(),e.pointerType=t.pointerType;let a=!0;e.isPressed||(e.isPressed=!0,e.isOverTarget=!0,e.activePointerId=t.pointerId,e.target=t.currentTarget,l||p||(0,x.e)(t.currentTarget),C||u(e.target),a=H(t,e.pointerType),W((0,o.T)(t.currentTarget),"pointermove",r,!1),W((0,o.T)(t.currentTarget),"pointerup",n,!1),W((0,o.T)(t.currentTarget),"pointercancel",i,!1)),a&&t.stopPropagation()},t.onMouseDown=e=>{e.currentTarget.contains(e.target)&&0===e.button&&(I(e.currentTarget)&&e.preventDefault(),e.stopPropagation())},t.onPointerUp=t=>{t.currentTarget.contains(t.target)&&"virtual"!==e.pointerType&&0===t.button&&A(t,t.currentTarget)&&G(t,e.pointerType||t.pointerType)};let r=t=>{t.pointerId===e.activePointerId&&(e.target&&A(t,e.target)?e.isOverTarget||null==e.pointerType||(e.isOverTarget=!0,H(O(e.target,t),e.pointerType)):e.target&&e.isOverTarget&&null!=e.pointerType&&(e.isOverTarget=!1,V(O(e.target,t),e.pointerType,!1),U(t)))},n=t=>{t.pointerId===e.activePointerId&&e.isPressed&&0===t.button&&e.target&&(A(t,e.target)&&null!=e.pointerType?V(O(e.target,t),e.pointerType):e.isOverTarget&&null!=e.pointerType&&V(O(e.target,t),e.pointerType,!1),e.isPressed=!1,e.isOverTarget=!1,e.activePointerId=null,e.pointerType=null,F(),C||d(e.target),"ontouchend"in e.target&&"mouse"!==t.pointerType&&W(e.target,"touchend",a,{once:!0}))},a=e=>{R(e.currentTarget)&&e.preventDefault()},i=e=>{B(e)};t.onDragStart=e=>{e.currentTarget.contains(e.target)&&B(e)}}else{t.onMouseDown=t=>{if(0===t.button&&t.currentTarget.contains(t.target)){if(I(t.currentTarget)&&t.preventDefault(),e.ignoreEmulatedMouseEvents){t.stopPropagation();return}e.isPressed=!0,e.isOverTarget=!0,e.target=t.currentTarget,e.pointerType=(0,w.Y)(t.nativeEvent)?"virtual":"mouse",l||p||(0,x.e)(t.currentTarget),H(t,e.pointerType)&&t.stopPropagation(),W((0,o.T)(t.currentTarget),"mouseup",r,!1)}},t.onMouseEnter=t=>{if(!t.currentTarget.contains(t.target))return;let r=!0;e.isPressed&&!e.ignoreEmulatedMouseEvents&&null!=e.pointerType&&(e.isOverTarget=!0,r=H(t,e.pointerType)),r&&t.stopPropagation()},t.onMouseLeave=t=>{if(!t.currentTarget.contains(t.target))return;let r=!0;e.isPressed&&!e.ignoreEmulatedMouseEvents&&null!=e.pointerType&&(e.isOverTarget=!1,r=V(t,e.pointerType,!1),U(t)),r&&t.stopPropagation()},t.onMouseUp=t=>{t.currentTarget.contains(t.target)&&!e.ignoreEmulatedMouseEvents&&0===t.button&&G(t,e.pointerType||"mouse")};let r=t=>{if(0===t.button){if(e.isPressed=!1,F(),e.ignoreEmulatedMouseEvents){e.ignoreEmulatedMouseEvents=!1;return}e.target&&A(t,e.target)&&null!=e.pointerType?V(O(e.target,t),e.pointerType):e.target&&e.isOverTarget&&null!=e.pointerType&&V(O(e.target,t),e.pointerType,!1),e.isOverTarget=!1}};t.onTouchStart=t=>{if(!t.currentTarget.contains(t.target))return;let r=function(e){let{targetTouches:t}=e;return t.length>0?t[0]:null}(t.nativeEvent);r&&(e.activePointerId=r.identifier,e.ignoreEmulatedMouseEvents=!0,e.isOverTarget=!0,e.isPressed=!0,e.target=t.currentTarget,e.pointerType="touch",l||p||(0,x.e)(t.currentTarget),C||u(e.target),H(L(e.target,t),e.pointerType)&&t.stopPropagation(),W((0,o.m)(t.currentTarget),"scroll",n,!0))},t.onTouchMove=t=>{if(!t.currentTarget.contains(t.target))return;if(!e.isPressed){t.stopPropagation();return}let r=j(t.nativeEvent,e.activePointerId),n=!0;r&&A(r,t.currentTarget)?e.isOverTarget||null==e.pointerType||(e.isOverTarget=!0,n=H(L(e.target,t),e.pointerType)):e.isOverTarget&&null!=e.pointerType&&(e.isOverTarget=!1,n=V(L(e.target,t),e.pointerType,!1),U(L(e.target,t))),n&&t.stopPropagation()},t.onTouchEnd=t=>{if(!t.currentTarget.contains(t.target))return;if(!e.isPressed){t.stopPropagation();return}let r=j(t.nativeEvent,e.activePointerId),n=!0;r&&A(r,t.currentTarget)&&null!=e.pointerType?(G(L(e.target,t),e.pointerType),n=V(L(e.target,t),e.pointerType)):e.isOverTarget&&null!=e.pointerType&&(n=V(L(e.target,t),e.pointerType,!1)),n&&t.stopPropagation(),e.isPressed=!1,e.activePointerId=null,e.isOverTarget=!1,e.ignoreEmulatedMouseEvents=!0,e.target&&!C&&d(e.target),F()},t.onTouchCancel=t=>{t.currentTarget.contains(t.target)&&(t.stopPropagation(),e.isPressed&&B(L(e.target,t)))};let n=t=>{e.isPressed&&t.target.contains(e.target)&&B({currentTarget:e.target,shiftKey:!1,ctrlKey:!1,metaKey:!1,altKey:!1})};t.onDragStart=e=>{e.currentTarget.contains(e.target)&&B(e)}}return t},[W,l,p,F,C,B,U,V,H,G]);return(0,E.useEffect)(()=>()=>{var e;C||d(null!==(e=K.current.target)&&void 0!==e?e:void 0)},[C]),{isPressed:f||D,pressProps:(0,g.v)(_,X)}}function S(e){return"A"===e.tagName&&e.hasAttribute("href")}function M(e,t){let{key:r,code:n}=e,a=t.getAttribute("role");return("Enter"===r||" "===r||"Spacebar"===r||"Space"===n)&&!(t instanceof(0,o.m)(t).HTMLInputElement&&!_(t,r)||t instanceof(0,o.m)(t).HTMLTextAreaElement||t.isContentEditable)&&!(("link"===a||!a&&S(t))&&"Enter"!==r)}function j(e,t){let r=e.changedTouches;for(let e=0;e<r.length;e++){let n=r[e];if(n.identifier===t)return n}return null}function L(e,t){let r=0,n=0;return t.targetTouches&&1===t.targetTouches.length&&(r=t.targetTouches[0].clientX,n=t.targetTouches[0].clientY),{currentTarget:e,shiftKey:t.shiftKey,ctrlKey:t.ctrlKey,metaKey:t.metaKey,altKey:t.altKey,clientX:r,clientY:n}}function O(e,t){let r=t.clientX,n=t.clientY;return{currentTarget:e,shiftKey:t.shiftKey,ctrlKey:t.ctrlKey,metaKey:t.metaKey,altKey:t.altKey,clientX:r,clientY:n}}function A(e,t){let r,n,o=t.getBoundingClientRect(),a=(r=0,n=0,void 0!==e.width?r=e.width/2:void 0!==e.radiusX&&(r=e.radiusX),void 0!==e.height?n=e.height/2:void 0!==e.radiusY&&(n=e.radiusY),{top:e.clientY-n,right:e.clientX+r,bottom:e.clientY+n,left:e.clientX-r});return!(o.left>a.right)&&!(a.left>o.right)&&!(o.top>a.bottom)&&!(a.top>o.bottom)}function I(e){return!(e instanceof HTMLElement)||!e.hasAttribute("draggable")}function R(e){return!(e instanceof HTMLInputElement)&&(e instanceof HTMLButtonElement?"submit"!==e.type&&"reset"!==e.type:!S(e))}function z(e,t){return e instanceof HTMLInputElement?!_(e,t):R(e)}let N=new Set(["checkbox","radio","range","color","file","image","button","submit","reset"]);function _(e,t){return"checkbox"===e.type||"radio"===e.type?" "===t:N.has(e.type)}},20637:(e,t,r)=>{r.d(t,{$:()=>a,H:()=>o});var n=r(78606);let o={};function a(e){for(let t in e)o[t]=e[t],(0,n.j)(t)&&(o[t].isCSSVariable=!0)}},22706:(e,t,r)=>{r.d(t,{F:()=>n});let n=r(12115).createContext({register:()=>{}});n.displayName="PressResponderContext"},22989:(e,t,r)=>{r.d(t,{Fe:()=>d,_h:()=>f,pg:()=>s,rd:()=>l,sU:()=>u});var n=r(47024),o=r(66680),a=r(12115);let i=(0,a.createContext)({isNative:!0,open:function(e,t){c(e,e=>d(e,t))},useHref:e=>e});function s(e){let{children:t,navigate:r,useHref:n}=e,o=(0,a.useMemo)(()=>({isNative:!1,open:(e,t,n,o)=>{c(e,e=>{u(e,t)?r(n,o):d(e,t)})},useHref:n||(e=>e)}),[r,n]);return a.createElement(i.Provider,{value:o},t)}function l(){return(0,a.useContext)(i)}function u(e,t){let r=e.getAttribute("target");return(!r||"_self"===r)&&e.origin===location.origin&&!e.hasAttribute("download")&&!t.metaKey&&!t.ctrlKey&&!t.altKey&&!t.shiftKey}function d(e,t,r=!0){var a,i;let{metaKey:s,ctrlKey:l,altKey:u,shiftKey:c}=t;(0,o.gm)()&&(null===(i=window.event)||void 0===i?void 0:null===(a=i.type)||void 0===a?void 0:a.startsWith("key"))&&"_blank"===e.target&&((0,o.cX)()?s=!0:l=!0);let f=(0,o.Tc)()&&(0,o.cX)()&&!(0,o.bh)()?new KeyboardEvent("keydown",{keyIdentifier:"Enter",metaKey:s,ctrlKey:l,altKey:u,shiftKey:c}):new MouseEvent("click",{metaKey:s,ctrlKey:l,altKey:u,shiftKey:c,bubbles:!0,cancelable:!0});d.isOpening=r,(0,n.e)(e),e.dispatchEvent(f),d.isOpening=!1}function c(e,t){if(e instanceof HTMLAnchorElement)t(e);else if(e.hasAttribute("data-href")){let r=document.createElement("a");r.href=e.getAttribute("data-href"),e.hasAttribute("data-target")&&(r.target=e.getAttribute("data-target")),e.hasAttribute("data-rel")&&(r.rel=e.getAttribute("data-rel")),e.hasAttribute("data-download")&&(r.download=e.getAttribute("data-download")),e.hasAttribute("data-ping")&&(r.ping=e.getAttribute("data-ping")),e.hasAttribute("data-referrer-policy")&&(r.referrerPolicy=e.getAttribute("data-referrer-policy")),e.appendChild(r),t(r),e.removeChild(r)}}function f(e){var t;let r=l().useHref(null!==(t=null==e?void 0:e.href)&&void 0!==t?t:"");return{href:(null==e?void 0:e.href)?r:void 0,target:null==e?void 0:e.target,rel:null==e?void 0:e.rel,download:null==e?void 0:e.download,ping:null==e?void 0:e.ping,referrerPolicy:null==e?void 0:e.referrerPolicy}}d.isOpening=!1},23387:(e,t,r)=>{r.d(t,{W:()=>n});let n={}},24744:(e,t,r)=>{r.d(t,{Q:()=>n});let n={value:null,addProjectionMetrics:null}},25214:(e,t,r)=>{r.d(t,{Y:()=>n});let n=(0,r(12115).createContext)({strict:!1})},28920:(e,t,r)=>{r.d(t,{o:()=>d});var n=r(56973),o=(0,r(69478).tv)({slots:{base:"relative inline-flex flex-col gap-2 items-center justify-center",wrapper:"relative flex",circle1:["absolute","w-full","h-full","rounded-full","animate-spinner-ease-spin","border-2","border-solid","border-t-transparent","border-l-transparent","border-r-transparent"],circle2:["absolute","w-full","h-full","rounded-full","opacity-75","animate-spinner-linear-spin","border-2","border-dotted","border-t-transparent","border-l-transparent","border-r-transparent"],label:"text-foreground dark:text-foreground-dark font-regular"},variants:{size:{sm:{wrapper:"w-5 h-5",circle1:"border-2",circle2:"border-2",label:"text-small"},md:{wrapper:"w-8 h-8",circle1:"border-3",circle2:"border-3",label:"text-medium"},lg:{wrapper:"w-10 h-10",circle1:"border-3",circle2:"border-3",label:"text-large"}},color:{current:{circle1:"border-b-current",circle2:"border-b-current"},white:{circle1:"border-b-white",circle2:"border-b-white"},default:{circle1:"border-b-default",circle2:"border-b-default"},primary:{circle1:"border-b-primary",circle2:"border-b-primary"},secondary:{circle1:"border-b-secondary",circle2:"border-b-secondary"},success:{circle1:"border-b-success",circle2:"border-b-success"},warning:{circle1:"border-b-warning",circle2:"border-b-warning"},danger:{circle1:"border-b-danger",circle2:"border-b-danger"}},labelColor:{foreground:{label:"text-foreground"},primary:{label:"text-primary"},secondary:{label:"text-secondary"},success:{label:"text-success"},warning:{label:"text-warning"},danger:{label:"text-danger"}}},defaultVariants:{size:"md",color:"primary",labelColor:"foreground"}}),a=r(81467),i=r(5712),s=r(12115),l=r(95155),u=(0,n.Rf)((e,t)=>{let{slots:r,classNames:u,label:d,getSpinnerProps:c}=function(e){let[t,r]=(0,n.rE)(e,o.variantKeys),{children:l,className:u,classNames:d,label:c,...f}=t,p=(0,s.useMemo)(()=>o({...r}),[(0,a.t6)(r)]),g=(0,i.$)(null==d?void 0:d.base,u),m=c||l,v=(0,s.useMemo)(()=>m&&"string"==typeof m?m:f["aria-label"]?"":"Loading",[l,m,f["aria-label"]]),b=(0,s.useCallback)(()=>({"aria-label":v,className:p.base({class:g}),...f}),[v,p,g,f]);return{label:m,slots:p,classNames:d,getSpinnerProps:b}}({...e});return(0,l.jsxs)("div",{ref:t,...c(),children:[(0,l.jsxs)("div",{className:r.wrapper({class:null==u?void 0:u.wrapper}),children:[(0,l.jsx)("i",{className:r.circle1({class:null==u?void 0:u.circle1})}),(0,l.jsx)("i",{className:r.circle2({class:null==u?void 0:u.circle2})})]}),d&&(0,l.jsx)("span",{className:r.label({class:null==u?void 0:u.label}),children:d})]})});u.displayName="NextUI.Spinner";var d=u},28944:(e,t,r)=>{r.d(t,{Cl:()=>T,K7:()=>P,ME:()=>E,pP:()=>x});var n=r(66680),o=r(58838),a=r(87418),i=r(12115);let s=null,l=new Set,u=new Map,d=!1,c=!1,f={Tab:!0,Escape:!0};function p(e,t){for(let r of l)r(e,t)}function g(e){d=!0,!(e.metaKey||!(0,n.cX)()&&e.altKey||e.ctrlKey||"Control"===e.key||"Shift"===e.key||"Meta"===e.key)&&(s="keyboard",p("keyboard",e))}function m(e){s="pointer",("mousedown"===e.type||"pointerdown"===e.type)&&(d=!0,p("pointer",e))}function v(e){(0,o.Y)(e)&&(d=!0,s="virtual")}function b(e){e.target!==window&&e.target!==document&&(d||c||(s="virtual",p("virtual",e)),d=!1,c=!1)}function y(){d=!1,c=!0}function h(e){if("undefined"==typeof window||u.get((0,a.m)(e)))return;let t=(0,a.m)(e),r=(0,a.T)(e),n=t.HTMLElement.prototype.focus;t.HTMLElement.prototype.focus=function(){d=!0,n.apply(this,arguments)},r.addEventListener("keydown",g,!0),r.addEventListener("keyup",g,!0),r.addEventListener("click",v,!0),t.addEventListener("focus",b,!0),t.addEventListener("blur",y,!1),"undefined"!=typeof PointerEvent?(r.addEventListener("pointerdown",m,!0),r.addEventListener("pointermove",m,!0),r.addEventListener("pointerup",m,!0)):(r.addEventListener("mousedown",m,!0),r.addEventListener("mousemove",m,!0),r.addEventListener("mouseup",m,!0)),t.addEventListener("beforeunload",()=>{w(e)},{once:!0}),u.set(t,{focus:n})}let w=(e,t)=>{let r=(0,a.m)(e),n=(0,a.T)(e);t&&n.removeEventListener("DOMContentLoaded",t),u.has(r)&&(r.HTMLElement.prototype.focus=u.get(r).focus,n.removeEventListener("keydown",g,!0),n.removeEventListener("keyup",g,!0),n.removeEventListener("click",v,!0),r.removeEventListener("focus",b,!0),r.removeEventListener("blur",y,!1),"undefined"!=typeof PointerEvent?(n.removeEventListener("pointerdown",m,!0),n.removeEventListener("pointermove",m,!0),n.removeEventListener("pointerup",m,!0)):(n.removeEventListener("mousedown",m,!0),n.removeEventListener("mousemove",m,!0),n.removeEventListener("mouseup",m,!0)),u.delete(r))};function x(){return"pointer"!==s}function E(){return s}function T(e){s=e,p(e,null)}"undefined"!=typeof document&&function(e){let t;let r=(0,a.T)(void 0);"loading"!==r.readyState?h(void 0):(t=()=>{h(e)},r.addEventListener("DOMContentLoaded",t)),()=>w(e,t)}();let k=new Set(["checkbox","radio","range","color","file","image","button","submit","reset"]);function P(e,t,r){h(),(0,i.useEffect)(()=>{let t=(t,n)=>{(function(e,t,r){var n;let o="undefined"!=typeof window?(0,a.m)(null==r?void 0:r.target).HTMLInputElement:HTMLInputElement,i="undefined"!=typeof window?(0,a.m)(null==r?void 0:r.target).HTMLTextAreaElement:HTMLTextAreaElement,s="undefined"!=typeof window?(0,a.m)(null==r?void 0:r.target).HTMLElement:HTMLElement,l="undefined"!=typeof window?(0,a.m)(null==r?void 0:r.target).KeyboardEvent:KeyboardEvent;return!((e=e||(null==r?void 0:r.target)instanceof o&&!k.has(null==r?void 0:null===(n=r.target)||void 0===n?void 0:n.type)||(null==r?void 0:r.target)instanceof i||(null==r?void 0:r.target)instanceof s&&(null==r?void 0:r.target.isContentEditable))&&"keyboard"===t&&r instanceof l&&!f[r.key])})(!!(null==r?void 0:r.isTextInput),t,n)&&e(x())};return l.add(t),()=>{l.delete(t)}},t)}},30502:(e,t,r)=>{r.d(t,{l:()=>s});var n=r(87418),o=r(17575),a=r(47024),i=r(28944);function s(e){let t=(0,n.T)(e);if("virtual"===(0,i.ME)()){let r=t.activeElement;(0,o.v)(()=>{t.activeElement===r&&e.isConnected&&(0,a.e)(e)})}else(0,a.e)(e)}},31788:(e,t,r)=>{r.d(t,{n:()=>n});let n="data-"+(0,r(78450).I)("framerAppearId")},32047:(e,t,r)=>{r.d(t,{J:()=>a});var n=r(33205),o=r(12115);function a(e){let t=(0,o.useRef)(null);return(0,n.N)(()=>{t.current=e},[e]),(0,o.useCallback)((...e)=>{let r=t.current;return null==r?void 0:r(...e)},[])}},32082:(e,t,r)=>{r.d(t,{xQ:()=>a});var n=r(12115),o=r(80845);function a(e=!0){let t=(0,n.useContext)(o.t);if(null===t)return[!0,null];let{isPresent:r,onExitComplete:i,register:s}=t,l=(0,n.useId)();(0,n.useEffect)(()=>{if(e)return s(l)},[e]);let u=(0,n.useCallback)(()=>e&&i&&i(l),[l,i,e]);return!r&&i?[!1,u]:[!0]}},33055:(e,t,r)=>{r.d(t,{z:()=>a});var n=r(60018),o=r(20637);function a(e,{layout:t,layoutId:r}){return n.f.has(e)||e.startsWith("origin")||(t||void 0!==r)&&(!!o.H[e]||"opacity"===e)}},33205:(e,t,r)=>{r.d(t,{N:()=>o});var n=r(12115);let o="undefined"!=typeof document?n.useLayoutEffect:()=>{}},33991:(e,t,r)=>{r.d(t,{X:()=>n});function n(e){return e&&"object"==typeof e&&Object.prototype.hasOwnProperty.call(e,"current")}},34158:(e,t,r)=>{r.d(t,{KN:()=>a,gQ:()=>u,px:()=>i,uj:()=>o,vh:()=>s,vw:()=>l});let n=e=>({test:t=>"string"==typeof t&&t.endsWith(e)&&1===t.split(" ").length,parse:parseFloat,transform:t=>`${t}${e}`}),o=n("deg"),a=n("%"),i=n("px"),s=n("vh"),l=n("vw"),u={...a,parse:e=>a.parse(e)/100,transform:e=>a.transform(100*e)}},34527:(e,t,r)=>{r.d(t,{x:()=>i});var n=r(60018),o=r(14570),a=r(60990);function i(e,t,r){let i=(0,a.x)(e,t,r);for(let r in e)((0,o.S)(e[r])||(0,o.S)(t[r]))&&(i[-1!==n.U.indexOf(r)?"attr"+r.charAt(0).toUpperCase()+r.substring(1):r]=e[r]);return i}},35421:(e,t,r)=>{r.d(t,{Tw:()=>d,Bi:()=>u,X1:()=>c});var n=r(33205),o=r(32047),a=r(12115),i=r(44823);let s=!!("undefined"!=typeof window&&window.document&&window.document.createElement),l=new Map;function u(e){let[t,r]=(0,a.useState)(e),o=(0,a.useRef)(null),u=(0,i.Cc)(t),d=(0,a.useCallback)(e=>{o.current=e},[]);return s&&(l.has(u)&&!l.get(u).includes(d)?l.set(u,[...l.get(u),d]):l.set(u,[d])),(0,n.N)(()=>()=>{l.delete(u)},[u]),(0,a.useEffect)(()=>{let e=o.current;e&&(o.current=null,r(e))}),u}function d(e,t){if(e===t)return e;let r=l.get(e);if(r)return r.forEach(e=>e(t)),t;let n=l.get(t);return n?(n.forEach(t=>t(e)),e):t}function c(e=[]){let t=u(),[r,i]=function(e){let[t,r]=(0,a.useState)(e),i=(0,a.useRef)(null),s=(0,o.J)(()=>{if(!i.current)return;let e=i.current.next();if(e.done){i.current=null;return}t===e.value?s():r(e.value)});(0,n.N)(()=>{i.current&&s()});let l=(0,o.J)(e=>{i.current=e(t),s()});return[t,l]}(t),s=(0,a.useCallback)(()=>{i(function*(){yield t,yield document.getElementById(t)?t:void 0})},[t,i]);return(0,n.N)(s,[t,s,...e]),r}},35925:(e,t,r)=>{r.d(t,{k:()=>a});var n=r(81467),o=r(12115);function a(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},[t,r]=(0,o.useState)([]),a=(0,o.useCallback)(e=>{let t=e.target,o=Math.max(t.clientWidth,t.clientHeight);r(t=>[...t,{key:(0,n.Lz)(t.length.toString()),size:o,x:e.x-o/2,y:e.y-o/2}])},[]);return{ripples:t,onClear:(0,o.useCallback)(e=>{r(t=>t.filter(t=>t.key!==e))},[]),onPress:a,...e}}},42810:(e,t,r)=>{r.d(t,{q:()=>o});var n=r(12115);function o(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{strict:t=!0,errorMessage:r="useContext: `context` is undefined. Seems you forgot to wrap component within the Provider",name:o}=e,a=n.createContext(void 0);return a.displayName=o,[a.Provider,function e(){var o;let i=n.useContext(a);if(!i&&t){let t=Error(r);throw t.name="ContextError",null==(o=Error.captureStackTrace)||o.call(Error,t,e),t}return i},a]}},44823:(e,t,r)=>{r.d(t,{Cc:()=>d,wR:()=>g});var n=r(12115);let o={prefix:String(Math.round(1e10*Math.random())),current:0},a=n.createContext(o),i=n.createContext(!1),s=!!("undefined"!=typeof window&&window.document&&window.document.createElement),l=new WeakMap;function u(e=!1){let t=(0,n.useContext)(a),r=(0,n.useRef)(null);if(null===r.current&&!e){var o,i;let e=null===(i=n.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED)||void 0===i?void 0:null===(o=i.ReactCurrentOwner)||void 0===o?void 0:o.current;if(e){let r=l.get(e);null==r?l.set(e,{id:t.current,state:e.memoizedState}):e.memoizedState!==r.state&&(t.current=r.id,l.delete(e))}r.current=++t.current}return r.current}let d="function"==typeof n.useId?function(e){let t=n.useId(),[r]=(0,n.useState)(g()),a=r?"react-aria":`react-aria${o.prefix}`;return e||`${a}-${t}`}:function(e){let t=(0,n.useContext)(a);t!==o||s||console.warn("When server rendering, you must wrap your application in an <SSRProvider> to ensure consistent ids are generated between the client and server.");let r=u(!!e),i=`react-aria${t.prefix}`;return e||`${i}-${r}`};function c(){return!1}function f(){return!0}function p(e){return()=>{}}function g(){return"function"==typeof n.useSyncExternalStore?n.useSyncExternalStore(p,c,f):(0,n.useContext)(i)}},45756:(e,t,r)=>{r.d(t,{i:()=>i});var n=r(660),o=r(12115),a=r(87418);function i(e){let{isDisabled:t,onFocus:r,onBlur:i,onFocusChange:s}=e,l=(0,o.useCallback)(e=>{if(e.target===e.currentTarget)return i&&i(e),s&&s(!1),!0},[i,s]),u=(0,n.y)(l),d=(0,o.useCallback)(e=>{let t=(0,a.T)(e.target);e.target===e.currentTarget&&t.activeElement===e.target&&(r&&r(e),s&&s(!0),u(e))},[s,r,u]);return{focusProps:{onFocus:!t&&(r||s||i)?d:void 0,onBlur:!t&&(i||s)?l:void 0}}}},47024:(e,t,r)=>{function n(e){if(function(){if(null==o){o=!1;try{document.createElement("div").focus({get preventScroll(){return o=!0,!0}})}catch{}}return o}())e.focus({preventScroll:!0});else{let t=function(e){let t=e.parentNode,r=[],n=document.scrollingElement||document.documentElement;for(;t instanceof HTMLElement&&t!==n;)(t.offsetHeight<t.scrollHeight||t.offsetWidth<t.scrollWidth)&&r.push({element:t,scrollTop:t.scrollTop,scrollLeft:t.scrollLeft}),t=t.parentNode;return n instanceof HTMLElement&&r.push({element:n,scrollTop:n.scrollTop,scrollLeft:n.scrollLeft}),r}(e);e.focus(),function(e){for(let{element:t,scrollTop:r,scrollLeft:n}of e)t.scrollTop=r,t.scrollLeft=n}(t)}}r.d(t,{e:()=>n});let o=null},47701:(e,t,r)=>{r.d(t,{w:()=>o});var n=["small","medium","large"],o={theme:{opacity:["disabled"],spacing:["divider"],borderWidth:n,borderRadius:n},classGroups:{shadow:[{shadow:n}],"font-size":[{text:["tiny",...n]}],"bg-image":["bg-stripe-gradient-default","bg-stripe-gradient-primary","bg-stripe-gradient-secondary","bg-stripe-gradient-success","bg-stripe-gradient-warning","bg-stripe-gradient-danger"]}}},51251:(e,t,r)=>{r.d(t,{F:()=>s});var n=r(95155),o=r(12115),a=r(25214),i=r(9480);function s(e){let{children:t,features:r,strict:s=!1}=e,[,u]=(0,o.useState)(!l(r)),d=(0,o.useRef)(void 0);if(!l(r)){let{renderer:e,...t}=r;d.current=e,(0,i.Y)(t)}return(0,o.useEffect)(()=>{l(r)&&r().then(e=>{let{renderer:t,...r}=e;(0,i.Y)(r),d.current=t,u(!0)})},[]),(0,n.jsx)(a.Y.Provider,{value:{renderer:d.current,strict:s},children:t})}function l(e){return"function"==typeof e}},51508:(e,t,r)=>{r.d(t,{Q:()=>n});let n=(0,r(12115).createContext)({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"})},52596:(e,t,r)=>{function n(){for(var e,t,r=0,n="",o=arguments.length;r<o;r++)(e=arguments[r])&&(t=function e(t){var r,n,o="";if("string"==typeof t||"number"==typeof t)o+=t;else if("object"==typeof t){if(Array.isArray(t)){var a=t.length;for(r=0;r<a;r++)t[r]&&(n=e(t[r]))&&(o&&(o+=" "),o+=n)}else for(n in t)t[n]&&(o&&(o+=" "),o+=n)}return o}(e))&&(n&&(n+=" "),n+=t);return n}r.d(t,{$:()=>n,A:()=>o});let o=n},53678:(e,t,r)=>{r.d(t,{q:()=>n});let n=(e,t,r)=>r>t?t:r<e?e:r},53880:(e,t,r)=>{r.d(t,{C:()=>$});var n=r(95155),o=r(12115),a=r(90869),i=r(25214),s=r(51508);let l=(0,o.createContext)({});var u=r(19253),d=r(65305);function c(e){return Array.isArray(e)?e.join(" "):e}var f=r(68972),p=r(6642),g=r(9480);let m=Symbol.for("motionComponentSymbol");var v=r(33991),b=r(87123),y=r(31788),h=r(80845),w=r(70797),x=r(97494),E=r(33055),T=r(14570),k=r(7684);let P=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function C(e,t,r){for(let n in t)(0,T.S)(t[n])||(0,E.z)(n,r)||(e[n]=t[n])}var S=r(95500),M=r(175),j=r(82076);let L=()=>({...P(),attrs:{}});var O=r(93095),A=r(6340),I=r(2735),R=r(82885),z=r(95902);let N=e=>(t,r)=>{let n=(0,o.useContext)(l),a=(0,o.useContext)(h.t),i=()=>(function({scrapeMotionValuesFromProps:e,createRenderState:t},r,n,o){return{latestValues:function(e,t,r,n){let o={},a=n(e,{});for(let e in a)o[e]=(0,z.u)(a[e]);let{initial:i,animate:s}=e,l=(0,u.e)(e),d=(0,u.O)(e);t&&d&&!l&&!1!==e.inherit&&(void 0===i&&(i=t.initial),void 0===s&&(s=t.animate));let c=!!r&&!1===r.initial,f=(c=c||!1===i)?s:i;if(f&&"boolean"!=typeof f&&!(0,A.N)(f)){let t=Array.isArray(f)?f:[f];for(let r=0;r<t.length;r++){let n=(0,I.a)(e,t[r]);if(n){let{transitionEnd:e,transition:t,...r}=n;for(let e in r){let t=r[e];if(Array.isArray(t)){let e=c?t.length-1:0;t=t[e]}null!==t&&(o[e]=t)}for(let t in e)o[t]=e[t]}}}return o}(r,n,o,e),renderState:t()}})(e,t,n,a);return r?i():(0,R.M)(i)},_={useVisualState:N({scrapeMotionValuesFromProps:r(60990).x,createRenderState:P})},D={useVisualState:N({scrapeMotionValuesFromProps:r(34527).x,createRenderState:L})};function $(e,t){return function(r,{forwardMotionProps:E}={forwardMotionProps:!1}){return function(e){var t,r;let{preloadedFeatures:E,createVisualElement:T,useRender:k,useVisualState:P,Component:C}=e;function S(e,t){var r,g,m;let E;let S={...(0,o.useContext)(s.Q),...e,layoutId:function(e){let{layoutId:t}=e,r=(0,o.useContext)(a.L).id;return r&&void 0!==t?r+"-"+t:t}(e)},{isStatic:M}=S,j=function(e){let{initial:t,animate:r}=function(e,t){if((0,u.e)(e)){let{initial:t,animate:r}=e;return{initial:!1===t||(0,d.w)(t)?t:void 0,animate:(0,d.w)(r)?r:void 0}}return!1!==e.inherit?t:{}}(e,(0,o.useContext)(l));return(0,o.useMemo)(()=>({initial:t,animate:r}),[c(t),c(r)])}(e),L=P(e,M);if(!M&&f.B){g=0,m=0,(0,o.useContext)(i.Y).strict;let e=function(e){let{drag:t,layout:r}=p.B;if(!t&&!r)return{};let n={...t,...r};return{MeasureLayout:(null==t?void 0:t.isEnabled(e))||(null==r?void 0:r.isEnabled(e))?n.MeasureLayout:void 0,ProjectionNode:n.ProjectionNode}}(S);E=e.MeasureLayout,j.visualElement=function(e,t,r,n,a){let{visualElement:u}=(0,o.useContext)(l),d=(0,o.useContext)(i.Y),c=(0,o.useContext)(h.t),f=(0,o.useContext)(s.Q).reducedMotion,p=(0,o.useRef)(null);n=n||d.renderer,!p.current&&n&&(p.current=n(e,{visualState:t,parent:u,props:r,presenceContext:c,blockInitialAnimation:!!c&&!1===c.initial,reducedMotionConfig:f}));let g=p.current,m=(0,o.useContext)(w.N);g&&!g.projection&&a&&("html"===g.type||"svg"===g.type)&&function(e,t,r,n){let{layoutId:o,layout:a,drag:i,dragConstraints:s,layoutScroll:l,layoutRoot:u,layoutCrossfade:d}=t;e.projection=new r(e.latestValues,t["data-framer-portal-id"]?void 0:function e(t){if(t)return!1!==t.options.allowProjection?t.projection:e(t.parent)}(e.parent)),e.projection.setOptions({layoutId:o,layout:a,alwaysMeasureLayout:!!i||s&&(0,v.X)(s),visualElement:e,animationType:"string"==typeof a?a:"both",initialPromotionConfig:n,crossfade:d,layoutScroll:l,layoutRoot:u})}(p.current,r,a,m);let E=(0,o.useRef)(!1);(0,o.useInsertionEffect)(()=>{g&&E.current&&g.update(r,c)});let T=r[y.n],k=(0,o.useRef)(!!T&&!window.MotionHandoffIsComplete?.(T)&&window.MotionHasOptimisedAnimation?.(T));return(0,x.E)(()=>{g&&(E.current=!0,window.MotionIsMounted=!0,g.updateFeatures(),b.k.render(g.render),k.current&&g.animationState&&g.animationState.animateChanges())}),(0,o.useEffect)(()=>{g&&(!k.current&&g.animationState&&g.animationState.animateChanges(),k.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(T)}),k.current=!1))}),g}(C,L,S,T,e.ProjectionNode)}return(0,n.jsxs)(l.Provider,{value:j,children:[E&&j.visualElement?(0,n.jsx)(E,{visualElement:j.visualElement,...S}):null,k(C,e,(r=j.visualElement,(0,o.useCallback)(e=>{e&&L.onMount&&L.onMount(e),r&&(e?r.mount(e):r.unmount()),t&&("function"==typeof t?t(e):(0,v.X)(t)&&(t.current=e))},[r])),L,M,j.visualElement)]})}E&&(0,g.Y)(E),S.displayName="motion.".concat("string"==typeof C?C:"create(".concat(null!==(r=null!==(t=C.displayName)&&void 0!==t?t:C.name)&&void 0!==r?r:"",")"));let M=(0,o.forwardRef)(S);return M[m]=C,M}({...(0,M.Q)(r)?D:_,preloadedFeatures:e,useRender:function(e=!1){return(t,r,n,{latestValues:a},i)=>{let s=((0,M.Q)(t)?function(e,t,r,n){let a=(0,o.useMemo)(()=>{let r=L();return(0,j.B)(r,t,(0,O.n)(n),e.transformTemplate),{...r.attrs,style:{...r.style}}},[t]);if(e.style){let t={};C(t,e.style,e),a.style={...t,...a.style}}return a}:function(e,t){let r={},n=function(e,t){let r=e.style||{},n={};return C(n,r,e),Object.assign(n,function({transformTemplate:e},t){return(0,o.useMemo)(()=>{let r=P();return(0,k.O)(r,t,e),Object.assign({},r.vars,r.style)},[t])}(e,t)),n}(e,t);return e.drag&&!1!==e.dragListener&&(r.draggable=!1,n.userSelect=n.WebkitUserSelect=n.WebkitTouchCallout="none",n.touchAction=!0===e.drag?"none":`pan-${"x"===e.drag?"y":"x"}`),void 0===e.tabIndex&&(e.onTap||e.onTapStart||e.whileTap)&&(r.tabIndex=0),r.style=n,r})(r,a,i,t),l=(0,S.J)(r,"string"==typeof t,e),u=t!==o.Fragment?{...l,...s,ref:n}:{},{children:d}=r,c=(0,o.useMemo)(()=>(0,T.S)(d)?d.get():d,[d]);return(0,o.createElement)(t,{...u,children:c})}}(E),createVisualElement:t,Component:r})}}},56973:(e,t,r)=>{r.d(t,{Rf:()=>o,rE:()=>a});var n=r(12115);function o(e){return(0,n.forwardRef)(e)}var a=(e,t,r=!0)=>{if(!t)return[e,{}];let n=t.reduce((t,r)=>r in e?{...t,[r]:e[r]}:t,{});return r?[Object.keys(e).filter(e=>!t.includes(e)).reduce((t,r)=>({...t,[r]:e[r]}),{}),n]:[e,n]}},57887:(e,t,r)=>{r.d(t,{X4:()=>a,ai:()=>o,hs:()=>i});var n=r(53678);let o={test:e=>"number"==typeof e,parse:parseFloat,transform:e=>e},a={...o,transform:e=>(0,n.q)(0,1,e)},i={...o,default:1}},58437:(e,t,r)=>{r.d(t,{I:()=>i});var n=r(23387);let o=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"];var a=r(24744);function i(e,t){let r=!1,i=!0,s={delta:0,timestamp:0,isProcessing:!1},l=()=>r=!0,u=o.reduce((e,r)=>(e[r]=function(e,t){let r=new Set,n=new Set,o=!1,i=!1,s=new WeakSet,l={delta:0,timestamp:0,isProcessing:!1},u=0;function d(t){s.has(t)&&(c.schedule(t),e()),u++,t(l)}let c={schedule:(e,t=!1,a=!1)=>{let i=a&&o?r:n;return t&&s.add(e),i.has(e)||i.add(e),e},cancel:e=>{n.delete(e),s.delete(e)},process:e=>{if(l=e,o){i=!0;return}o=!0,[r,n]=[n,r],r.forEach(d),t&&a.Q.value&&a.Q.value.frameloop[t].push(u),u=0,r.clear(),o=!1,i&&(i=!1,c.process(e))}};return c}(l,t?r:void 0),e),{}),{setup:d,read:c,resolveKeyframes:f,preUpdate:p,update:g,preRender:m,render:v,postRender:b}=u,y=()=>{let o=n.W.useManualTiming?s.timestamp:performance.now();r=!1,n.W.useManualTiming||(s.delta=i?1e3/60:Math.max(Math.min(o-s.timestamp,40),1)),s.timestamp=o,s.isProcessing=!0,d.process(s),c.process(s),f.process(s),p.process(s),g.process(s),m.process(s),v.process(s),b.process(s),s.isProcessing=!1,r&&t&&(i=!1,e(y))},h=()=>{r=!0,i=!0,s.isProcessing||e(y)};return{schedule:o.reduce((e,t)=>{let n=u[t];return e[t]=(e,t=!1,o=!1)=>(r||h(),n.schedule(e,t,o)),e},{}),cancel:e=>{for(let t=0;t<o.length;t++)u[o[t]].cancel(e)},state:s,steps:u}}},58838:(e,t,r)=>{r.d(t,{P:()=>a,Y:()=>o});var n=r(66680);function o(e){return 0===e.mozInputSource&&!!e.isTrusted||((0,n.m0)()&&e.pointerType?"click"===e.type&&1===e.buttons:0===e.detail&&!e.pointerType)}function a(e){return!(0,n.m0)()&&0===e.width&&0===e.height||1===e.width&&1===e.height&&0===e.pressure&&0===e.detail&&"mouse"===e.pointerType}},60018:(e,t,r)=>{r.d(t,{U:()=>n,f:()=>o});let n=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],o=new Set(n)},60760:(e,t,r)=>{r.d(t,{N:()=>b});var n=r(95155),o=r(12115),a=r(90869),i=r(82885),s=r(97494),l=r(80845),u=r(51508);class d extends o.Component{getSnapshotBeforeUpdate(e){let t=this.props.childRef.current;if(t&&e.isPresent&&!this.props.isPresent){let e=t.offsetParent,r=e instanceof HTMLElement&&e.offsetWidth||0,n=this.props.sizeRef.current;n.height=t.offsetHeight||0,n.width=t.offsetWidth||0,n.top=t.offsetTop,n.left=t.offsetLeft,n.right=r-n.width-n.left}return null}componentDidUpdate(){}render(){return this.props.children}}function c(e){let{children:t,isPresent:r,anchorX:a}=e,i=(0,o.useId)(),s=(0,o.useRef)(null),l=(0,o.useRef)({width:0,height:0,top:0,left:0,right:0}),{nonce:c}=(0,o.useContext)(u.Q);return(0,o.useInsertionEffect)(()=>{let{width:e,height:t,top:n,left:o,right:u}=l.current;if(r||!s.current||!e||!t)return;s.current.dataset.motionPopId=i;let d=document.createElement("style");return c&&(d.nonce=c),document.head.appendChild(d),d.sheet&&d.sheet.insertRule('\n          [data-motion-pop-id="'.concat(i,'"] {\n            position: absolute !important;\n            width: ').concat(e,"px !important;\n            height: ").concat(t,"px !important;\n            ").concat("left"===a?"left: ".concat(o):"right: ".concat(u),"px !important;\n            top: ").concat(n,"px !important;\n          }\n        ")),()=>{document.head.removeChild(d)}},[r]),(0,n.jsx)(d,{isPresent:r,childRef:s,sizeRef:l,children:o.cloneElement(t,{ref:s})})}let f=e=>{let{children:t,initial:r,isPresent:a,onExitComplete:s,custom:u,presenceAffectsLayout:d,mode:f,anchorX:g}=e,m=(0,i.M)(p),v=(0,o.useId)(),b=!0,y=(0,o.useMemo)(()=>(b=!1,{id:v,initial:r,isPresent:a,custom:u,onExitComplete:e=>{for(let t of(m.set(e,!0),m.values()))if(!t)return;s&&s()},register:e=>(m.set(e,!1),()=>m.delete(e))}),[a,m,s]);return d&&b&&(y={...y}),(0,o.useMemo)(()=>{m.forEach((e,t)=>m.set(t,!1))},[a]),o.useEffect(()=>{a||m.size||!s||s()},[a]),"popLayout"===f&&(t=(0,n.jsx)(c,{isPresent:a,anchorX:g,children:t})),(0,n.jsx)(l.t.Provider,{value:y,children:t})};function p(){return new Map}var g=r(32082);let m=e=>e.key||"";function v(e){let t=[];return o.Children.forEach(e,e=>{(0,o.isValidElement)(e)&&t.push(e)}),t}let b=e=>{let{children:t,custom:r,initial:l=!0,onExitComplete:u,presenceAffectsLayout:d=!0,mode:c="sync",propagate:p=!1,anchorX:b="left"}=e,[y,h]=(0,g.xQ)(p),w=(0,o.useMemo)(()=>v(t),[t]),x=p&&!y?[]:w.map(m),E=(0,o.useRef)(!0),T=(0,o.useRef)(w),k=(0,i.M)(()=>new Map),[P,C]=(0,o.useState)(w),[S,M]=(0,o.useState)(w);(0,s.E)(()=>{E.current=!1,T.current=w;for(let e=0;e<S.length;e++){let t=m(S[e]);x.includes(t)?k.delete(t):!0!==k.get(t)&&k.set(t,!1)}},[S,x.length,x.join("-")]);let j=[];if(w!==P){let e=[...w];for(let t=0;t<S.length;t++){let r=S[t],n=m(r);x.includes(n)||(e.splice(t,0,r),j.push(r))}return"wait"===c&&j.length&&(e=j),M(v(e)),C(w),null}let{forceRender:L}=(0,o.useContext)(a.L);return(0,n.jsx)(n.Fragment,{children:S.map(e=>{let t=m(e),o=(!p||!!y)&&(w===S||x.includes(t));return(0,n.jsx)(f,{isPresent:o,initial:(!E.current||!!l)&&void 0,custom:r,presenceAffectsLayout:d,mode:c,onExitComplete:o?void 0:()=>{if(!k.has(t))return;k.set(t,!0);let e=!0;k.forEach(t=>{t||(e=!1)}),e&&(null==L||L(),M(T.current),p&&(null==h||h()),u&&u())},anchorX:b,children:e},t)})})}},60990:(e,t,r)=>{r.d(t,{x:()=>a});var n=r(33055),o=r(14570);function a(e,t,r){let{style:a}=e,i={};for(let s in a)((0,o.S)(a[s])||t.style&&(0,o.S)(t.style[s])||(0,n.z)(s,e)||r?.getValue(s)?.liveStyle!==void 0)&&(i[s]=a[s]);return i}},62293:(e,t,r)=>{r.d(t,{R:()=>a});var n=r(660),o=r(12115);function a(e){let{isDisabled:t,onBlurWithin:r,onFocusWithin:a,onFocusWithinChange:i}=e,s=(0,o.useRef)({isFocusWithin:!1}),l=(0,o.useCallback)(e=>{s.current.isFocusWithin&&!e.currentTarget.contains(e.relatedTarget)&&(s.current.isFocusWithin=!1,r&&r(e),i&&i(!1))},[r,i,s]),u=(0,n.y)(l),d=(0,o.useCallback)(e=>{s.current.isFocusWithin||document.activeElement!==e.target||(a&&a(e),i&&i(!0),s.current.isFocusWithin=!0,u(e))},[a,i,u]);return t?{focusWithinProps:{onFocus:void 0,onBlur:void 0}}:{focusWithinProps:{onFocus:d,onBlur:l}}}},65305:(e,t,r)=>{r.d(t,{w:()=>n});function n(e){return"string"==typeof e||Array.isArray(e)}},66146:(e,t,r)=>{r.d(t,{T:()=>P});var[n,o]=(0,r(42810).q)({name:"ButtonGroupContext",strict:!1}),a=r(75894),i=r(672),s=r(12115),l=r(77151),u=r(73750),d=r(81627),c=r(6548),f=r(491),p=r(70418),g=r(69478),m=r(66232),v=(0,g.tv)({base:["z-0","group","relative","inline-flex","items-center","justify-center","box-border","appearance-none","outline-none","select-none","whitespace-nowrap","min-w-max","font-normal","subpixel-antialiased","overflow-hidden","tap-highlight-transparent","data-[pressed=true]:scale-[0.97]",...m.zb],variants:{variant:{solid:"",bordered:"border-medium bg-transparent",light:"bg-transparent",flat:"",faded:"border-medium",shadow:"",ghost:"border-medium bg-transparent"},size:{sm:"px-3 min-w-16 h-8 text-tiny gap-2 rounded-small",md:"px-4 min-w-20 h-10 text-small gap-2 rounded-medium",lg:"px-6 min-w-24 h-12 text-medium gap-3 rounded-large"},color:{default:"",primary:"",secondary:"",success:"",warning:"",danger:""},radius:{none:"rounded-none",sm:"rounded-small",md:"rounded-medium",lg:"rounded-large",full:"rounded-full"},fullWidth:{true:"w-full"},isDisabled:{true:"opacity-disabled pointer-events-none"},isInGroup:{true:"[&:not(:first-child):not(:last-child)]:rounded-none"},isIconOnly:{true:"px-0 !gap-0",false:"[&>svg]:max-w-[theme(spacing.8)]"},disableAnimation:{true:"!transition-none data-[pressed=true]:scale-100",false:"transition-transform-colors-opacity motion-reduce:transition-none"}},defaultVariants:{size:"md",variant:"solid",color:"default",fullWidth:!1,isDisabled:!1,isInGroup:!1},compoundVariants:[{variant:"solid",color:"default",class:p.k.solid.default},{variant:"solid",color:"primary",class:p.k.solid.primary},{variant:"solid",color:"secondary",class:p.k.solid.secondary},{variant:"solid",color:"success",class:p.k.solid.success},{variant:"solid",color:"warning",class:p.k.solid.warning},{variant:"solid",color:"danger",class:p.k.solid.danger},{variant:"shadow",color:"default",class:p.k.shadow.default},{variant:"shadow",color:"primary",class:p.k.shadow.primary},{variant:"shadow",color:"secondary",class:p.k.shadow.secondary},{variant:"shadow",color:"success",class:p.k.shadow.success},{variant:"shadow",color:"warning",class:p.k.shadow.warning},{variant:"shadow",color:"danger",class:p.k.shadow.danger},{variant:"bordered",color:"default",class:p.k.bordered.default},{variant:"bordered",color:"primary",class:p.k.bordered.primary},{variant:"bordered",color:"secondary",class:p.k.bordered.secondary},{variant:"bordered",color:"success",class:p.k.bordered.success},{variant:"bordered",color:"warning",class:p.k.bordered.warning},{variant:"bordered",color:"danger",class:p.k.bordered.danger},{variant:"flat",color:"default",class:p.k.flat.default},{variant:"flat",color:"primary",class:p.k.flat.primary},{variant:"flat",color:"secondary",class:p.k.flat.secondary},{variant:"flat",color:"success",class:p.k.flat.success},{variant:"flat",color:"warning",class:p.k.flat.warning},{variant:"flat",color:"danger",class:p.k.flat.danger},{variant:"faded",color:"default",class:p.k.faded.default},{variant:"faded",color:"primary",class:p.k.faded.primary},{variant:"faded",color:"secondary",class:p.k.faded.secondary},{variant:"faded",color:"success",class:p.k.faded.success},{variant:"faded",color:"warning",class:p.k.faded.warning},{variant:"faded",color:"danger",class:p.k.faded.danger},{variant:"light",color:"default",class:[p.k.light.default,"data-[hover=true]:bg-default/40"]},{variant:"light",color:"primary",class:[p.k.light.primary,"data-[hover=true]:bg-primary/20"]},{variant:"light",color:"secondary",class:[p.k.light.secondary,"data-[hover=true]:bg-secondary/20"]},{variant:"light",color:"success",class:[p.k.light.success,"data-[hover=true]:bg-success/20"]},{variant:"light",color:"warning",class:[p.k.light.warning,"data-[hover=true]:bg-warning/20"]},{variant:"light",color:"danger",class:[p.k.light.danger,"data-[hover=true]:bg-danger/20"]},{variant:"ghost",color:"default",class:[p.k.ghost.default,"data-[hover=true]:!bg-default"]},{variant:"ghost",color:"primary",class:[p.k.ghost.primary,"data-[hover=true]:!bg-primary data-[hover=true]:!text-primary-foreground"]},{variant:"ghost",color:"secondary",class:[p.k.ghost.secondary,"data-[hover=true]:!bg-secondary data-[hover=true]:!text-secondary-foreground"]},{variant:"ghost",color:"success",class:[p.k.ghost.success,"data-[hover=true]:!bg-success data-[hover=true]:!text-success-foreground"]},{variant:"ghost",color:"warning",class:[p.k.ghost.warning,"data-[hover=true]:!bg-warning data-[hover=true]:!text-warning-foreground"]},{variant:"ghost",color:"danger",class:[p.k.ghost.danger,"data-[hover=true]:!bg-danger data-[hover=true]:!text-danger-foreground"]},{isInGroup:!0,class:"rounded-none first:rounded-s-medium last:rounded-e-medium"},{isInGroup:!0,size:"sm",class:"rounded-none first:rounded-s-small last:rounded-e-small"},{isInGroup:!0,size:"md",class:"rounded-none first:rounded-s-medium last:rounded-e-medium"},{isInGroup:!0,size:"lg",class:"rounded-none first:rounded-s-large last:rounded-e-large"},{isInGroup:!0,isRounded:!0,class:"rounded-none first:rounded-s-full last:rounded-e-full"},{isInGroup:!0,radius:"none",class:"rounded-none first:rounded-s-none last:rounded-e-none"},{isInGroup:!0,radius:"sm",class:"rounded-none first:rounded-s-small last:rounded-e-small"},{isInGroup:!0,radius:"md",class:"rounded-none first:rounded-s-medium last:rounded-e-medium"},{isInGroup:!0,radius:"lg",class:"rounded-none first:rounded-s-large last:rounded-e-large"},{isInGroup:!0,radius:"full",class:"rounded-none first:rounded-s-full last:rounded-e-full"},{isInGroup:!0,variant:["ghost","bordered"],color:"default",className:m.oT.default},{isInGroup:!0,variant:["ghost","bordered"],color:"primary",className:m.oT.primary},{isInGroup:!0,variant:["ghost","bordered"],color:"secondary",className:m.oT.secondary},{isInGroup:!0,variant:["ghost","bordered"],color:"success",className:m.oT.success},{isInGroup:!0,variant:["ghost","bordered"],color:"warning",className:m.oT.warning},{isInGroup:!0,variant:["ghost","bordered"],color:"danger",className:m.oT.danger},{isIconOnly:!0,size:"sm",class:"min-w-8 w-8 h-8"},{isIconOnly:!0,size:"md",class:"min-w-10 w-10 h-10"},{isIconOnly:!0,size:"lg",class:"min-w-12 w-12 h-12"},{variant:["solid","faded","flat","bordered","shadow"],class:"data-[hover=true]:opacity-hover"}]});(0,g.tv)({base:"inline-flex items-center justify-center h-auto",variants:{fullWidth:{true:"w-full"}},defaultVariants:{fullWidth:!1}});var b=r(88629),y=r(9906),h=r(35925),w=r(28920),x=r(9539),E=r(56973),T=r(95155),k=(0,E.Rf)((e,t)=>{let{Component:r,domRef:n,children:p,styles:g,spinnerSize:m,spinner:E=(0,T.jsx)(w.o,{color:"current",size:m}),spinnerPlacement:k,startContent:P,endContent:C,isLoading:S,disableRipple:M,getButtonProps:j,getRippleProps:L,isIconOnly:O}=function(e){var t,r,n,p,g,m,w,x,E;let T=o(),k=(0,a.o)(),P=!!T,{ref:C,as:S,children:M,startContent:j,endContent:L,autoFocus:O,className:A,spinner:I,isLoading:R=!1,disableRipple:z=!1,fullWidth:N=null!=(t=null==T?void 0:T.fullWidth)&&t,radius:_=null==T?void 0:T.radius,size:D=null!=(r=null==T?void 0:T.size)?r:"md",color:$=null!=(n=null==T?void 0:T.color)?n:"default",variant:K=null!=(p=null==T?void 0:T.variant)?p:"solid",disableAnimation:W=null!=(m=null!=(g=null==T?void 0:T.disableAnimation)?g:null==k?void 0:k.disableAnimation)&&m,isDisabled:F=null!=(w=null==T?void 0:T.isDisabled)&&w,isIconOnly:H=null!=(x=null==T?void 0:T.isIconOnly)&&x,spinnerPlacement:V="start",onPress:G,onClick:B,...U}=e,X=S||"button",Y="string"==typeof X,q=(0,c.zD)(C),Q=null!=(E=z||(null==k?void 0:k.disableRipple))?E:W,{isFocusVisible:J,isFocused:Z,focusProps:ee}=(0,l.o)({autoFocus:O}),et=F||R,er=(0,s.useMemo)(()=>v({size:D,color:$,variant:K,radius:_,fullWidth:N,isDisabled:et,isInGroup:P,disableAnimation:W,isIconOnly:H,className:A}),[D,$,K,_,N,et,P,H,W,A]),{onPress:en,onClear:eo,ripples:ea}=(0,h.k)(),ei=(0,s.useCallback)(e=>{Q||et||W||!q.current||en(e)},[Q,et,W,q,en]),{buttonProps:es,isPressed:el}=(0,b.l)({elementType:S,isDisabled:et,onPress:(0,u.c)(G,ei),onClick:B,...U},q),{isHovered:eu,hoverProps:ed}=(0,y.M)({isDisabled:et}),ec=(0,s.useCallback)(function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return{"data-disabled":(0,i.sE)(et),"data-focus":(0,i.sE)(Z),"data-pressed":(0,i.sE)(el),"data-focus-visible":(0,i.sE)(J),"data-hover":(0,i.sE)(eu),"data-loading":(0,i.sE)(R),...(0,d.v)(es,ee,ed,(0,f.$)(U,{enabled:Y}),(0,f.$)(e))}},[R,et,Z,el,Y,J,eu,es,ee,ed,U]),ef=e=>(0,s.isValidElement)(e)?(0,s.cloneElement)(e,{"aria-hidden":!0,focusable:!1,tabIndex:-1}):null,ep=ef(j),eg=ef(L);return{Component:X,children:M,domRef:q,spinner:I,styles:er,startContent:ep,endContent:eg,isLoading:R,spinnerPlacement:V,spinnerSize:(0,s.useMemo)(()=>({sm:"sm",md:"sm",lg:"md"})[D],[D]),disableRipple:Q,getButtonProps:ec,getRippleProps:(0,s.useCallback)(()=>({ripples:ea,onClear:eo}),[ea,eo]),isIconOnly:H}}({...e,ref:t});return(0,T.jsxs)(r,{ref:n,className:g,...j(),children:[P,S&&"start"===k&&E,S&&O?null:p,S&&"end"===k&&E,C,!M&&(0,T.jsx)(x.j,{...L()})]})});k.displayName="NextUI.Button";var P=k},66232:(e,t,r)=>{r.d(t,{oT:()=>a,wA:()=>o,zb:()=>n});var n=["outline-none","data-[focus-visible=true]:z-10","data-[focus-visible=true]:outline-2","data-[focus-visible=true]:outline-focus","data-[focus-visible=true]:outline-offset-2"],o=["outline-none","group-data-[focus-visible=true]:z-10","group-data-[focus-visible=true]:ring-2","group-data-[focus-visible=true]:ring-focus","group-data-[focus-visible=true]:ring-offset-2","group-data-[focus-visible=true]:ring-offset-background"],a={default:["[&+.border-medium.border-default]:ms-[calc(theme(borderWidth.medium)*-1)]"],primary:["[&+.border-medium.border-primary]:ms-[calc(theme(borderWidth.medium)*-1)]"],secondary:["[&+.border-medium.border-secondary]:ms-[calc(theme(borderWidth.medium)*-1)]"],success:["[&+.border-medium.border-success]:ms-[calc(theme(borderWidth.medium)*-1)]"],warning:["[&+.border-medium.border-warning]:ms-[calc(theme(borderWidth.medium)*-1)]"],danger:["[&+.border-medium.border-danger]:ms-[calc(theme(borderWidth.medium)*-1)]"]}},66680:(e,t,r)=>{function n(e){var t;return"undefined"!=typeof window&&null!=window.navigator&&((null===(t=window.navigator.userAgentData)||void 0===t?void 0:t.brands.some(t=>e.test(t.brand)))||e.test(window.navigator.userAgent))}function o(e){var t;return"undefined"!=typeof window&&null!=window.navigator&&e.test((null===(t=window.navigator.userAgentData)||void 0===t?void 0:t.platform)||window.navigator.platform)}function a(e){let t=null;return()=>(null==t&&(t=e()),t)}r.d(t,{Tc:()=>c,bh:()=>l,cX:()=>i,gm:()=>g,lg:()=>d,m0:()=>p,un:()=>u});let i=a(function(){return o(/^Mac/i)}),s=a(function(){return o(/^iPhone/i)}),l=a(function(){return o(/^iPad/i)||i()&&navigator.maxTouchPoints>1}),u=a(function(){return s()||l()}),d=a(function(){return i()||u()}),c=a(function(){return n(/AppleWebKit/i)&&!f()}),f=a(function(){return n(/Chrome/i)}),p=a(function(){return n(/Android/i)}),g=a(function(){return n(/Firefox/i)})},68972:(e,t,r)=>{r.d(t,{B:()=>n});let n="undefined"!=typeof window},69478:(e,t,r)=>{r.d(t,{tv:()=>et});var n=r(47701),o=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,a=e=>!e||"object"!=typeof e||0===Object.keys(e).length,i=(e,t)=>JSON.stringify(e)===JSON.stringify(t);function s(e){let t=[];return function e(t,r){t.forEach(function(t){Array.isArray(t)?e(t,r):r.push(t)})}(e,t),t}var l=(...e)=>s(e).filter(Boolean),u=(e,t)=>{let r={},n=Object.keys(e),o=Object.keys(t);for(let a of n)if(o.includes(a)){let n=e[a],o=t[a];"object"==typeof n&&"object"==typeof o?r[a]=u(n,o):Array.isArray(n)||Array.isArray(o)?r[a]=l(o,n):r[a]=o+" "+n}else r[a]=e[a];for(let e of o)n.includes(e)||(r[e]=t[e]);return r},d=e=>e&&"string"==typeof e?e.replace(/\s+/g," ").trim():e,c=/^\[(.+)\]$/;function f(e,t){var r=e;return t.split("-").forEach(function(e){r.nextPart.has(e)||r.nextPart.set(e,{nextPart:new Map,validators:[]}),r=r.nextPart.get(e)}),r}var p=/\s+/;function g(){for(var e,t,r=0,n="";r<arguments.length;)(e=arguments[r++])&&(t=function e(t){if("string"==typeof t)return t;for(var r,n="",o=0;o<t.length;o++)t[o]&&(r=e(t[o]))&&(n&&(n+=" "),n+=r);return n}(e))&&(n&&(n+=" "),n+=t);return n}function m(){for(var e,t,r,n=arguments.length,o=Array(n),a=0;a<n;a++)o[a]=arguments[a];var i=function(n){var a=o[0];return t=(e=function(e){var t,r,n,o,a,i,s,l,u,d,p,g,m;return{cache:function(e){if(e<1)return{get:function(){},set:function(){}};var t=0,r=new Map,n=new Map;function o(o,a){r.set(o,a),++t>e&&(t=0,n=r,r=new Map)}return{get:function(e){var t=r.get(e);return void 0!==t?t:void 0!==(t=n.get(e))?(o(e,t),t):void 0},set:function(e,t){r.has(e)?r.set(e,t):o(e,t)}}}(e.cacheSize),splitModifiers:(r=1===(t=e.separator||":").length,n=t[0],o=t.length,function(e){for(var a,i=[],s=0,l=0,u=0;u<e.length;u++){var d=e[u];if(0===s){if(d===n&&(r||e.slice(u,u+o)===t)){i.push(e.slice(l,u)),l=u+o;continue}if("/"===d){a=u;continue}}"["===d?s++:"]"===d&&s--}var c=0===i.length?e:e.substring(l),f=c.startsWith("!"),p=f?c.substring(1):c;return{modifiers:i,hasImportantModifier:f,baseClassName:p,maybePostfixModifierPosition:a&&a>l?a-l:void 0}}),...(u=(l=e).theme,d=l.prefix,p={nextPart:new Map,validators:[]},(g=Object.entries(l.classGroups),(m=d)?g.map(function(e){return[e[0],e[1].map(function(e){return"string"==typeof e?m+e:"object"==typeof e?Object.fromEntries(Object.entries(e).map(function(e){return[m+e[0],e[1]]})):e})]}):g).forEach(function(e){var t=e[0];(function e(t,r,n,o){t.forEach(function(t){if("string"==typeof t){(""===t?r:f(r,t)).classGroupId=n;return}if("function"==typeof t){if(t.isThemeGetter){e(t(o),r,n,o);return}r.validators.push({validator:t,classGroupId:n});return}Object.entries(t).forEach(function(t){var a=t[0];e(t[1],f(r,a),n,o)})})})(e[1],p,t,u)}),a=e.conflictingClassGroups,s=void 0===(i=e.conflictingClassGroupModifiers)?{}:i,{getClassGroupId:function(e){var t=e.split("-");return""===t[0]&&1!==t.length&&t.shift(),function e(t,r){if(0===t.length)return r.classGroupId;var n=t[0],o=r.nextPart.get(n),a=o?e(t.slice(1),o):void 0;if(a)return a;if(0!==r.validators.length){var i=t.join("-");return r.validators.find(function(e){return(0,e.validator)(i)})?.classGroupId}}(t,p)||function(e){if(c.test(e)){var t=c.exec(e)[1],r=t?.substring(0,t.indexOf(":"));if(r)return"arbitrary.."+r}}(e)},getConflictingClassGroupIds:function(e,t){var r=a[e]||[];return t&&s[e]?[].concat(r,s[e]):r}})}}(o.slice(1).reduce(function(e,t){return t(e)},a()))).cache.get,r=e.cache.set,i=s,s(n)};function s(n){var o,a,i,s,l,u=t(n);if(u)return u;var d=(a=(o=e).splitModifiers,i=o.getClassGroupId,s=o.getConflictingClassGroupIds,l=new Set,n.trim().split(p).map(function(e){var t=a(e),r=t.modifiers,n=t.hasImportantModifier,o=t.baseClassName,s=t.maybePostfixModifierPosition,l=i(s?o.substring(0,s):o),u=!!s;if(!l){if(!s||!(l=i(o)))return{isTailwindClass:!1,originalClassName:e};u=!1}var d=(function(e){if(e.length<=1)return e;var t=[],r=[];return e.forEach(function(e){"["===e[0]?(t.push.apply(t,r.sort().concat([e])),r=[]):r.push(e)}),t.push.apply(t,r.sort()),t})(r).join(":");return{isTailwindClass:!0,modifierId:n?d+"!":d,classGroupId:l,originalClassName:e,hasPostfixModifier:u}}).reverse().filter(function(e){if(!e.isTailwindClass)return!0;var t=e.modifierId,r=e.classGroupId,n=e.hasPostfixModifier,o=t+r;return!l.has(o)&&(l.add(o),s(r,n).forEach(function(e){return l.add(t+e)}),!0)}).reverse().map(function(e){return e.originalClassName}).join(" "));return r(n,d),d}return function(){return i(g.apply(null,arguments))}}function v(e){var t=function(t){return t[e]||[]};return t.isThemeGetter=!0,t}var b=/^\[(?:([a-z-]+):)?(.+)\]$/i,y=/^\d+\/\d+$/,h=new Set(["px","full","screen"]),w=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,x=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,E=/^-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/;function T(e){return j(e)||h.has(e)||y.test(e)||k(e)}function k(e){return N(e,"length",_)}function P(e){return N(e,"size",D)}function C(e){return N(e,"position",D)}function S(e){return N(e,"url",$)}function M(e){return N(e,"number",j)}function j(e){return!Number.isNaN(Number(e))}function L(e){return e.endsWith("%")&&j(e.slice(0,-1))}function O(e){return K(e)||N(e,"number",K)}function A(e){return b.test(e)}function I(){return!0}function R(e){return w.test(e)}function z(e){return N(e,"",W)}function N(e,t,r){var n=b.exec(e);return!!n&&(n[1]?n[1]===t:r(n[2]))}function _(e){return x.test(e)}function D(){return!1}function $(e){return e.startsWith("url(")}function K(e){return Number.isInteger(Number(e))}function W(e){return E.test(e)}function F(){var e=v("colors"),t=v("spacing"),r=v("blur"),n=v("brightness"),o=v("borderColor"),a=v("borderRadius"),i=v("borderSpacing"),s=v("borderWidth"),l=v("contrast"),u=v("grayscale"),d=v("hueRotate"),c=v("invert"),f=v("gap"),p=v("gradientColorStops"),g=v("gradientColorStopPositions"),m=v("inset"),b=v("margin"),y=v("opacity"),h=v("padding"),w=v("saturate"),x=v("scale"),E=v("sepia"),N=v("skew"),_=v("space"),D=v("translate"),$=function(){return["auto","contain","none"]},K=function(){return["auto","hidden","clip","visible","scroll"]},W=function(){return["auto",A,t]},F=function(){return[A,t]},H=function(){return["",T]},V=function(){return["auto",j,A]},G=function(){return["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"]},B=function(){return["solid","dashed","dotted","double","none"]},U=function(){return["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity","plus-lighter"]},X=function(){return["start","end","center","between","around","evenly","stretch"]},Y=function(){return["","0",A]},q=function(){return["auto","avoid","all","avoid-page","page","left","right","column"]},Q=function(){return[j,M]},J=function(){return[j,A]};return{cacheSize:500,theme:{colors:[I],spacing:[T],blur:["none","",R,A],brightness:Q(),borderColor:[e],borderRadius:["none","","full",R,A],borderSpacing:F(),borderWidth:H(),contrast:Q(),grayscale:Y(),hueRotate:J(),invert:Y(),gap:F(),gradientColorStops:[e],gradientColorStopPositions:[L,k],inset:W(),margin:W(),opacity:Q(),padding:F(),saturate:Q(),scale:Q(),sepia:Y(),skew:J(),space:F(),translate:F()},classGroups:{aspect:[{aspect:["auto","square","video",A]}],container:["container"],columns:[{columns:[R]}],"break-after":[{"break-after":q()}],"break-before":[{"break-before":q()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none"]}],clear:[{clear:["left","right","both","none"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[].concat(G(),[A])}],overflow:[{overflow:K()}],"overflow-x":[{"overflow-x":K()}],"overflow-y":[{"overflow-y":K()}],overscroll:[{overscroll:$()}],"overscroll-x":[{"overscroll-x":$()}],"overscroll-y":[{"overscroll-y":$()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[m]}],"inset-x":[{"inset-x":[m]}],"inset-y":[{"inset-y":[m]}],start:[{start:[m]}],end:[{end:[m]}],top:[{top:[m]}],right:[{right:[m]}],bottom:[{bottom:[m]}],left:[{left:[m]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",O]}],basis:[{basis:W()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",A]}],grow:[{grow:Y()}],shrink:[{shrink:Y()}],order:[{order:["first","last","none",O]}],"grid-cols":[{"grid-cols":[I]}],"col-start-end":[{col:["auto",{span:["full",O]},A]}],"col-start":[{"col-start":V()}],"col-end":[{"col-end":V()}],"grid-rows":[{"grid-rows":[I]}],"row-start-end":[{row:["auto",{span:[O]},A]}],"row-start":[{"row-start":V()}],"row-end":[{"row-end":V()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",A]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",A]}],gap:[{gap:[f]}],"gap-x":[{"gap-x":[f]}],"gap-y":[{"gap-y":[f]}],"justify-content":[{justify:["normal"].concat(X())}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal"].concat(X(),["baseline"])}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[].concat(X(),["baseline"])}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[h]}],px:[{px:[h]}],py:[{py:[h]}],ps:[{ps:[h]}],pe:[{pe:[h]}],pt:[{pt:[h]}],pr:[{pr:[h]}],pb:[{pb:[h]}],pl:[{pl:[h]}],m:[{m:[b]}],mx:[{mx:[b]}],my:[{my:[b]}],ms:[{ms:[b]}],me:[{me:[b]}],mt:[{mt:[b]}],mr:[{mr:[b]}],mb:[{mb:[b]}],ml:[{ml:[b]}],"space-x":[{"space-x":[_]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[_]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit",A,t]}],"min-w":[{"min-w":["min","max","fit",A,T]}],"max-w":[{"max-w":["0","none","full","min","max","fit","prose",{screen:[R]},R,A]}],h:[{h:[A,t,"auto","min","max","fit"]}],"min-h":[{"min-h":["min","max","fit",A,T]}],"max-h":[{"max-h":[A,t,"min","max","fit"]}],"font-size":[{text:["base",R,k]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",M]}],"font-family":[{font:[I]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractons"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",A]}],"line-clamp":[{"line-clamp":["none",j,M]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",A,T]}],"list-image":[{"list-image":["none",A]}],"list-style-type":[{list:["none","disc","decimal",A]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[y]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[y]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[].concat(B(),["wavy"])}],"text-decoration-thickness":[{decoration:["auto","from-font",T]}],"underline-offset":[{"underline-offset":["auto",A,T]}],"text-decoration-color":[{decoration:[e]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],indent:[{indent:F()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",A]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",A]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[y]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[].concat(G(),[C])}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",P]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},S]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[g]}],"gradient-via-pos":[{via:[g]}],"gradient-to-pos":[{to:[g]}],"gradient-from":[{from:[p]}],"gradient-via":[{via:[p]}],"gradient-to":[{to:[p]}],rounded:[{rounded:[a]}],"rounded-s":[{"rounded-s":[a]}],"rounded-e":[{"rounded-e":[a]}],"rounded-t":[{"rounded-t":[a]}],"rounded-r":[{"rounded-r":[a]}],"rounded-b":[{"rounded-b":[a]}],"rounded-l":[{"rounded-l":[a]}],"rounded-ss":[{"rounded-ss":[a]}],"rounded-se":[{"rounded-se":[a]}],"rounded-ee":[{"rounded-ee":[a]}],"rounded-es":[{"rounded-es":[a]}],"rounded-tl":[{"rounded-tl":[a]}],"rounded-tr":[{"rounded-tr":[a]}],"rounded-br":[{"rounded-br":[a]}],"rounded-bl":[{"rounded-bl":[a]}],"border-w":[{border:[s]}],"border-w-x":[{"border-x":[s]}],"border-w-y":[{"border-y":[s]}],"border-w-s":[{"border-s":[s]}],"border-w-e":[{"border-e":[s]}],"border-w-t":[{"border-t":[s]}],"border-w-r":[{"border-r":[s]}],"border-w-b":[{"border-b":[s]}],"border-w-l":[{"border-l":[s]}],"border-opacity":[{"border-opacity":[y]}],"border-style":[{border:[].concat(B(),["hidden"])}],"divide-x":[{"divide-x":[s]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[s]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[y]}],"divide-style":[{divide:B()}],"border-color":[{border:[o]}],"border-color-x":[{"border-x":[o]}],"border-color-y":[{"border-y":[o]}],"border-color-t":[{"border-t":[o]}],"border-color-r":[{"border-r":[o]}],"border-color-b":[{"border-b":[o]}],"border-color-l":[{"border-l":[o]}],"divide-color":[{divide:[o]}],"outline-style":[{outline:[""].concat(B())}],"outline-offset":[{"outline-offset":[A,T]}],"outline-w":[{outline:[T]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:H()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[y]}],"ring-offset-w":[{"ring-offset":[T]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:["","inner","none",R,z]}],"shadow-color":[{shadow:[I]}],opacity:[{opacity:[y]}],"mix-blend":[{"mix-blend":U()}],"bg-blend":[{"bg-blend":U()}],filter:[{filter:["","none"]}],blur:[{blur:[r]}],brightness:[{brightness:[n]}],contrast:[{contrast:[l]}],"drop-shadow":[{"drop-shadow":["","none",R,A]}],grayscale:[{grayscale:[u]}],"hue-rotate":[{"hue-rotate":[d]}],invert:[{invert:[c]}],saturate:[{saturate:[w]}],sepia:[{sepia:[E]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[r]}],"backdrop-brightness":[{"backdrop-brightness":[n]}],"backdrop-contrast":[{"backdrop-contrast":[l]}],"backdrop-grayscale":[{"backdrop-grayscale":[u]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[d]}],"backdrop-invert":[{"backdrop-invert":[c]}],"backdrop-opacity":[{"backdrop-opacity":[y]}],"backdrop-saturate":[{"backdrop-saturate":[w]}],"backdrop-sepia":[{"backdrop-sepia":[E]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[i]}],"border-spacing-x":[{"border-spacing-x":[i]}],"border-spacing-y":[{"border-spacing-y":[i]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",A]}],duration:[{duration:J()}],ease:[{ease:["linear","in","out","in-out",A]}],delay:[{delay:J()}],animate:[{animate:["none","spin","ping","pulse","bounce",A]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[x]}],"scale-x":[{"scale-x":[x]}],"scale-y":[{"scale-y":[x]}],rotate:[{rotate:[O,A]}],"translate-x":[{"translate-x":[D]}],"translate-y":[{"translate-y":[D]}],"skew-x":[{"skew-x":[N]}],"skew-y":[{"skew-y":[N]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",A]}],accent:[{accent:["auto",e]}],appearance:["appearance-none"],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",A]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":F()}],"scroll-mx":[{"scroll-mx":F()}],"scroll-my":[{"scroll-my":F()}],"scroll-ms":[{"scroll-ms":F()}],"scroll-me":[{"scroll-me":F()}],"scroll-mt":[{"scroll-mt":F()}],"scroll-mr":[{"scroll-mr":F()}],"scroll-mb":[{"scroll-mb":F()}],"scroll-ml":[{"scroll-ml":F()}],"scroll-p":[{"scroll-p":F()}],"scroll-px":[{"scroll-px":F()}],"scroll-py":[{"scroll-py":F()}],"scroll-ps":[{"scroll-ps":F()}],"scroll-pe":[{"scroll-pe":F()}],"scroll-pt":[{"scroll-pt":F()}],"scroll-pr":[{"scroll-pr":F()}],"scroll-pb":[{"scroll-pb":F()}],"scroll-pl":[{"scroll-pl":F()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","pinch-zoom","manipulation",{pan:["x","left","right","y","up","down"]}]}],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",A]}],fill:[{fill:[e,"none"]}],"stroke-w":[{stroke:[T,M]}],stroke:[{stroke:[e,"none"]}],sr:["sr-only","not-sr-only"]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}}var H=m(F),V=Object.prototype.hasOwnProperty,G=new Set(["string","number","boolean"]),B={twMerge:!0,twMergeConfig:{},responsiveVariants:!1},U=e=>e||void 0,X=(...e)=>U(s(e).filter(Boolean).join(" ")),Y=null,q={},Q=!1,J=(...e)=>t=>t.twMerge?((!Y||Q)&&(Q=!1,Y=a(q)?H:function(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];return"function"==typeof e?m.apply(void 0,[F,e].concat(r)):m.apply(void 0,[function(){return function(e,t){for(var r in t)(function e(t,r,n){if(!V.call(t,r)||G.has(typeof n)||null===n){t[r]=n;return}if(Array.isArray(n)&&Array.isArray(t[r])){t[r]=t[r].concat(n);return}if("object"==typeof n&&"object"==typeof t[r]){if(null===t[r]){t[r]=n;return}for(var o in n)e(t[r],o,n[o])}})(e,r,t[r]);return e}(F(),e)}].concat(r))}(q)),U(Y(X(e)))):X(e),Z=(e,t)=>{for(let r in t)e.hasOwnProperty(r)?e[r]=X(e[r],t[r]):e[r]=t[r];return e},ee=(e,t)=>{let{extend:r=null,slots:n={},variants:s={},compoundVariants:c=[],compoundSlots:f=[],defaultVariants:p={}}=e,g={...B,...t},m=null!=r&&r.base?X(r.base,null==e?void 0:e.base):null==e?void 0:e.base,v=null!=r&&r.variants&&!a(r.variants)?u(s,r.variants):s,b=null!=r&&r.defaultVariants&&!a(r.defaultVariants)?{...r.defaultVariants,...p}:p;a(g.twMergeConfig)||i(g.twMergeConfig,q)||(Q=!0,q=g.twMergeConfig);let y=a(null==r?void 0:r.slots),h=a(n)?{}:{base:X(null==e?void 0:e.base,y&&(null==r?void 0:r.base)),...n},w=y?h:Z({...null==r?void 0:r.slots},a(h)?{base:null==e?void 0:e.base}:h),x=e=>{if(a(v)&&a(n)&&y)return J(m,null==e?void 0:e.class,null==e?void 0:e.className)(g);if(c&&!Array.isArray(c))throw TypeError(`The "compoundVariants" prop must be an array. Received: ${typeof c}`);if(f&&!Array.isArray(f))throw TypeError(`The "compoundSlots" prop must be an array. Received: ${typeof f}`);let t=(e,t,r=[],n)=>{let o=r;if("string"==typeof t)o=o.concat(d(t).split(" ").map(t=>`${e}:${t}`));else if(Array.isArray(t))o=o.concat(t.reduce((t,r)=>t.concat(`${e}:${r}`),[]));else if("object"==typeof t&&"string"==typeof n){for(let r in t)if(t.hasOwnProperty(r)&&r===n){let a=t[r];if(a&&"string"==typeof a){let t=d(a);o[n]?o[n]=o[n].concat(t.split(" ").map(t=>`${e}:${t}`)):o[n]=t.split(" ").map(t=>`${e}:${t}`)}else Array.isArray(a)&&a.length>0&&(o[n]=a.reduce((t,r)=>t.concat(`${e}:${r}`),[]))}}return o},i=(r,n=v,i=null,s=null)=>{var l;let u=n[r];if(!u||a(u))return null;let d=null!=(l=null==s?void 0:s[r])?l:null==e?void 0:e[r];if(null===d)return null;let c=o(d),f=Array.isArray(g.responsiveVariants)&&g.responsiveVariants.length>0||!0===g.responsiveVariants,p=null==b?void 0:b[r],m=[];if("object"==typeof c&&f)for(let[e,r]of Object.entries(c)){let n=u[r];if("initial"===e){p=r;continue}Array.isArray(g.responsiveVariants)&&!g.responsiveVariants.includes(e)||(m=t(e,n,m,i))}let y=u[c]||u[o(p)];return"object"==typeof m&&"string"==typeof i&&m[i]?Z(m,y):m.length>0?(m.push(y),m):y},s=(e,t)=>{if(!v||"object"!=typeof v)return null;let r=[];for(let n in v){let o=i(n,v,e,t),a="base"===e&&"string"==typeof o?o:o&&o[e];a&&(r[r.length]=a)}return r},u={};for(let t in e)void 0!==e[t]&&(u[t]=e[t]);let p=(t,r)=>{var n;let o="object"==typeof(null==e?void 0:e[t])?{[t]:null==(n=e[t])?void 0:n.initial}:{};return{...b,...u,...o,...r}},h=(e=[],t)=>{let r=[];for(let{class:n,className:o,...a}of e){let e=!0;for(let[r,n]of Object.entries(a)){let o=p(r,t);if(Array.isArray(n)){if(!n.includes(o[r])){e=!1;break}}else if(o[r]!==n){e=!1;break}}e&&(n&&r.push(n),o&&r.push(o))}return r},x=e=>{let t=h(c,e);return l(h(null==r?void 0:r.compoundVariants,e),t)},E=e=>{let t=x(e);if(!Array.isArray(t))return t;let r={};for(let e of t)if("string"==typeof e&&(r.base=J(r.base,e)(g)),"object"==typeof e)for(let[t,n]of Object.entries(e))r[t]=J(r[t],n)(g);return r},T=e=>{if(f.length<1)return null;let t={};for(let{slots:r=[],class:n,className:o,...i}of f){if(!a(i)){let t=!0;for(let r of Object.keys(i)){let n=p(r,e)[r];if(void 0===n||(Array.isArray(i[r])?!i[r].includes(n):i[r]!==n)){t=!1;break}}if(!t)continue}for(let e of r)t[e]=t[e]||[],t[e].push([n,o])}return t};if(!a(n)||!y){let e={};if("object"==typeof w&&!a(w))for(let t of Object.keys(w))e[t]=e=>{var r,n;return J(w[t],s(t,e),(null!=(r=E(e))?r:[])[t],(null!=(n=T(e))?n:[])[t],null==e?void 0:e.class,null==e?void 0:e.className)(g)};return e}return J(m,v?Object.keys(v).map(e=>i(e,v)):null,x(),null==e?void 0:e.class,null==e?void 0:e.className)(g)};return x.variantKeys=(()=>{if(!(!v||"object"!=typeof v))return Object.keys(v)})(),x.extend=r,x.base=m,x.slots=w,x.variants=v,x.defaultVariants=b,x.compoundSlots=f,x.compoundVariants=c,x},et=(e,t)=>{var r,o,a;return ee(e,{...t,twMerge:null==(r=null==t?void 0:t.twMerge)||r,twMergeConfig:{...null==t?void 0:t.twMergeConfig,theme:{...null==(o=null==t?void 0:t.twMergeConfig)?void 0:o.theme,...n.w.theme},classGroups:{...null==(a=null==t?void 0:t.twMergeConfig)?void 0:a.classGroups,...n.w.classGroups}}})}},70418:(e,t,r)=>{r.d(t,{k:()=>n});var n={solid:{default:"bg-default text-default-foreground",primary:"bg-primary text-primary-foreground",secondary:"bg-secondary text-secondary-foreground",success:"bg-success text-success-foreground",warning:"bg-warning text-warning-foreground",danger:"bg-danger text-danger-foreground",foreground:"bg-foreground text-background"},shadow:{default:"shadow-lg shadow-default/50 bg-default text-default-foreground",primary:"shadow-lg shadow-primary/40 bg-primary text-primary-foreground",secondary:"shadow-lg shadow-secondary/40 bg-secondary text-secondary-foreground",success:"shadow-lg shadow-success/40 bg-success text-success-foreground",warning:"shadow-lg shadow-warning/40 bg-warning text-warning-foreground",danger:"shadow-lg shadow-danger/40 bg-danger text-danger-foreground",foreground:"shadow-lg shadow-foreground/40 bg-foreground text-background"},bordered:{default:"bg-transparent border-default text-foreground",primary:"bg-transparent border-primary text-primary",secondary:"bg-transparent border-secondary text-secondary",success:"bg-transparent border-success text-success",warning:"bg-transparent border-warning text-warning",danger:"bg-transparent border-danger text-danger",foreground:"bg-transparent border-foreground text-foreground"},flat:{default:"bg-default/40 text-default-700",primary:"bg-primary/20 text-primary-600",secondary:"bg-secondary/20 text-secondary-600",success:"bg-success/20 text-success-700 dark:text-success",warning:"bg-warning/20 text-warning-700 dark:text-warning",danger:"bg-danger/20 text-danger-600 dark:text-danger-500",foreground:"bg-foreground/10 text-foreground"},faded:{default:"border-default bg-default-100 text-default-foreground",primary:"border-default bg-default-100 text-primary",secondary:"border-default bg-default-100 text-secondary",success:"border-default bg-default-100 text-success",warning:"border-default bg-default-100 text-warning",danger:"border-default bg-default-100 text-danger",foreground:"border-default bg-default-100 text-foreground"},light:{default:"bg-transparent text-default-foreground",primary:"bg-transparent text-primary",secondary:"bg-transparent text-secondary",success:"bg-transparent text-success",warning:"bg-transparent text-warning",danger:"bg-transparent text-danger",foreground:"bg-transparent text-foreground"},ghost:{default:"border-default text-default-foreground",primary:"border-primary text-primary",secondary:"border-secondary text-secondary",success:"border-success text-success",warning:"border-warning text-warning",danger:"border-danger text-danger",foreground:"border-foreground text-foreground hover:!bg-foreground"}}},70797:(e,t,r)=>{r.d(t,{N:()=>n});let n=(0,r(12115).createContext)({})},71071:(e,t,r)=>{r.d(t,{W:()=>d});var n=r(30502),o=r(87826),a=r(81627),i=r(12115),s=r(45756);function l(e){if(!e)return;let t=!0;return r=>{e({...r,preventDefault(){r.preventDefault()},isDefaultPrevented:()=>r.isDefaultPrevented(),stopPropagation(){console.error("stopPropagation is now the default behavior for events in React Spectrum. You can use continuePropagation() to revert this behavior.")},continuePropagation(){t=!1}}),t&&r.stopPropagation()}}let u=i.createContext(null);function d(e,t){let{focusProps:r}=(0,s.i)(e),{keyboardProps:d}={keyboardProps:e.isDisabled?{}:{onKeyDown:l(e.onKeyDown),onKeyUp:l(e.onKeyUp)}},c=(0,a.v)(r,d),f=function(e){let t=(0,i.useContext)(u)||{};(0,o.w)(t,e);let{ref:r,...n}=t;return n}(t),p=e.isDisabled?{}:f,g=(0,i.useRef)(e.autoFocus);return(0,i.useEffect)(()=>{g.current&&t.current&&(0,n.l)(t.current),g.current=!1},[t]),{focusableProps:(0,a.v)({...c,tabIndex:e.excludeFromTabOrder&&!e.isDisabled?-1:void 0},p)}}},72403:(e,t,r)=>{r.d(t,{W:()=>s});var n=r(57887);let o={...n.ai,transform:Math.round};var a=r(34158);let i={rotate:a.uj,rotateX:a.uj,rotateY:a.uj,rotateZ:a.uj,scale:n.hs,scaleX:n.hs,scaleY:n.hs,scaleZ:n.hs,skew:a.uj,skewX:a.uj,skewY:a.uj,distance:a.px,translateX:a.px,translateY:a.px,translateZ:a.px,x:a.px,y:a.px,z:a.px,perspective:a.px,transformPerspective:a.px,opacity:n.X4,originX:a.gQ,originY:a.gQ,originZ:a.px},s={borderWidth:a.px,borderTopWidth:a.px,borderRightWidth:a.px,borderBottomWidth:a.px,borderLeftWidth:a.px,borderRadius:a.px,radius:a.px,borderTopLeftRadius:a.px,borderTopRightRadius:a.px,borderBottomRightRadius:a.px,borderBottomLeftRadius:a.px,width:a.px,maxWidth:a.px,height:a.px,maxHeight:a.px,top:a.px,right:a.px,bottom:a.px,left:a.px,padding:a.px,paddingTop:a.px,paddingRight:a.px,paddingBottom:a.px,paddingLeft:a.px,margin:a.px,marginTop:a.px,marginRight:a.px,marginBottom:a.px,marginLeft:a.px,backgroundPositionX:a.px,backgroundPositionY:a.px,...i,zIndex:o,fillOpacity:n.X4,strokeOpacity:n.X4,numOctaves:o}},73750:(e,t,r)=>{r.d(t,{c:()=>n});function n(...e){return(...t)=>{for(let r of e)"function"==typeof r&&r(...t)}}},75894:(e,t,r)=>{r.d(t,{n:()=>n,o:()=>o});var[n,o]=(0,r(42810).q)({name:"ProviderContext",strict:!1})},77151:(e,t,r)=>{r.d(t,{o:()=>s});var n=r(28944),o=r(45756),a=r(62293),i=r(12115);function s(e={}){let{autoFocus:t=!1,isTextInput:r,within:l}=e,u=(0,i.useRef)({isFocused:!1,isFocusVisible:t||(0,n.pP)()}),[d,c]=(0,i.useState)(!1),[f,p]=(0,i.useState)(()=>u.current.isFocused&&u.current.isFocusVisible),g=(0,i.useCallback)(()=>p(u.current.isFocused&&u.current.isFocusVisible),[]),m=(0,i.useCallback)(e=>{u.current.isFocused=e,c(e),g()},[g]);(0,n.K7)(e=>{u.current.isFocusVisible=e,g()},[],{isTextInput:r});let{focusProps:v}=(0,o.i)({isDisabled:l,onFocusChange:m}),{focusWithinProps:b}=(0,a.R)({isDisabled:!l,onFocusWithinChange:m});return{isFocused:d,isFocusVisible:f,focusProps:l?b:v}}},78257:(e,t,r)=>{r.d(t,{$:()=>s});let n=new Set(["id"]),o=new Set(["aria-label","aria-labelledby","aria-describedby","aria-details"]),a=new Set(["href","hrefLang","target","rel","download","ping","referrerPolicy"]),i=/^(data-.*)$/;function s(e,t={}){let{labelable:r,isLink:l,propNames:u}=t,d={};for(let t in e)Object.prototype.hasOwnProperty.call(e,t)&&(n.has(t)||r&&o.has(t)||l&&a.has(t)||(null==u?void 0:u.has(t))||i.test(t))&&(d[t]=e[t]);return d}},78450:(e,t,r)=>{r.d(t,{I:()=>n});let n=e=>e.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase()},78606:(e,t,r)=>{r.d(t,{j:()=>o,p:()=>i});let n=e=>t=>"string"==typeof t&&t.startsWith(e),o=n("--"),a=n("var(--"),i=e=>!!a(e)&&s.test(e.split("/*")[0].trim()),s=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu},80845:(e,t,r)=>{r.d(t,{t:()=>n});let n=(0,r(12115).createContext)(null)},81467:(e,t,r)=>{r.d(t,{ZH:()=>p,QA:()=>y,Lz:()=>g,t6:()=>v,GU:()=>m});var n=Object.create,o=Object.defineProperty,a=Object.getOwnPropertyDescriptor,i=Object.getOwnPropertyNames,s=Object.getPrototypeOf,l=Object.prototype.hasOwnProperty,u=(e,t)=>function(){return t||(0,e[i(e)[0]])((t={exports:{}}).exports,t),t.exports},d=(e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let s of i(t))l.call(e,s)||s===r||o(e,s,{get:()=>t[s],enumerable:!(n=a(t,s))||n.enumerable});return e},c=u({"../../../node_modules/.pnpm/react@18.2.0/node_modules/react/cjs/react.production.min.js"(e){var t=Symbol.for("react.element"),r=Symbol.for("react.portal"),n=Symbol.for("react.fragment"),o=Symbol.for("react.strict_mode"),a=Symbol.for("react.profiler"),i=Symbol.for("react.provider"),s=Symbol.for("react.context"),l=Symbol.for("react.forward_ref"),u=Symbol.for("react.suspense"),d=Symbol.for("react.memo"),c=Symbol.for("react.lazy"),f=Symbol.iterator,p={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},g=Object.assign,m={};function v(e,t,r){this.props=e,this.context=t,this.refs=m,this.updater=r||p}function b(){}function y(e,t,r){this.props=e,this.context=t,this.refs=m,this.updater=r||p}v.prototype.isReactComponent={},v.prototype.setState=function(e,t){if("object"!=typeof e&&"function"!=typeof e&&null!=e)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")},v.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},b.prototype=v.prototype;var h=y.prototype=new b;h.constructor=y,g(h,v.prototype),h.isPureReactComponent=!0;var w=Array.isArray,x=Object.prototype.hasOwnProperty,E={current:null},T={key:!0,ref:!0,__self:!0,__source:!0};function k(e,r,n){var o,a={},i=null,s=null;if(null!=r)for(o in void 0!==r.ref&&(s=r.ref),void 0!==r.key&&(i=""+r.key),r)x.call(r,o)&&!T.hasOwnProperty(o)&&(a[o]=r[o]);var l=arguments.length-2;if(1===l)a.children=n;else if(1<l){for(var u=Array(l),d=0;d<l;d++)u[d]=arguments[d+2];a.children=u}if(e&&e.defaultProps)for(o in l=e.defaultProps)void 0===a[o]&&(a[o]=l[o]);return{$$typeof:t,type:e,key:i,ref:s,props:a,_owner:E.current}}function P(e){return"object"==typeof e&&null!==e&&e.$$typeof===t}var C=/\/+/g;function S(e,t){var r,n;return"object"==typeof e&&null!==e&&null!=e.key?(r=""+e.key,n={"=":"=0",":":"=2"},"$"+r.replace(/[=:]/g,function(e){return n[e]})):t.toString(36)}function M(e,n,o){if(null==e)return e;var a=[],i=0;return!function e(n,o,a,i,s){var l,u,d,c=typeof n;("undefined"===c||"boolean"===c)&&(n=null);var p=!1;if(null===n)p=!0;else switch(c){case"string":case"number":p=!0;break;case"object":switch(n.$$typeof){case t:case r:p=!0}}if(p)return s=s(p=n),n=""===i?"."+S(p,0):i,w(s)?(a="",null!=n&&(a=n.replace(C,"$&/")+"/"),e(s,o,a,"",function(e){return e})):null!=s&&(P(s)&&(l=s,u=a+(!s.key||p&&p.key===s.key?"":(""+s.key).replace(C,"$&/")+"/")+n,s={$$typeof:t,type:l.type,key:u,ref:l.ref,props:l.props,_owner:l._owner}),o.push(s)),1;if(p=0,i=""===i?".":i+":",w(n))for(var g=0;g<n.length;g++){var m=i+S(c=n[g],g);p+=e(c,o,a,m,s)}else if("function"==typeof(m=null===(d=n)||"object"!=typeof d?null:"function"==typeof(d=f&&d[f]||d["@@iterator"])?d:null))for(n=m.call(n),g=0;!(c=n.next()).done;)m=i+S(c=c.value,g++),p+=e(c,o,a,m,s);else if("object"===c)throw Error("Objects are not valid as a React child (found: "+("[object Object]"===(o=String(n))?"object with keys {"+Object.keys(n).join(", ")+"}":o)+"). If you meant to render a collection of children, use an array instead.");return p}(e,a,"","",function(e){return n.call(o,e,i++)}),a}function j(e){if(-1===e._status){var t=e._result;(t=t()).then(function(t){(0===e._status||-1===e._status)&&(e._status=1,e._result=t)},function(t){(0===e._status||-1===e._status)&&(e._status=2,e._result=t)}),-1===e._status&&(e._status=0,e._result=t)}if(1===e._status)return e._result.default;throw e._result}var L={current:null},O={transition:null};e.Children={map:M,forEach:function(e,t,r){M(e,function(){t.apply(this,arguments)},r)},count:function(e){var t=0;return M(e,function(){t++}),t},toArray:function(e){return M(e,function(e){return e})||[]},only:function(e){if(!P(e))throw Error("React.Children.only expected to receive a single React element child.");return e}},e.Component=v,e.Fragment=n,e.Profiler=a,e.PureComponent=y,e.StrictMode=o,e.Suspense=u,e.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED={ReactCurrentDispatcher:L,ReactCurrentBatchConfig:O,ReactCurrentOwner:E},e.cloneElement=function(e,r,n){if(null==e)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var o=g({},e.props),a=e.key,i=e.ref,s=e._owner;if(null!=r){if(void 0!==r.ref&&(i=r.ref,s=E.current),void 0!==r.key&&(a=""+r.key),e.type&&e.type.defaultProps)var l=e.type.defaultProps;for(u in r)x.call(r,u)&&!T.hasOwnProperty(u)&&(o[u]=void 0===r[u]&&void 0!==l?l[u]:r[u])}var u=arguments.length-2;if(1===u)o.children=n;else if(1<u){l=Array(u);for(var d=0;d<u;d++)l[d]=arguments[d+2];o.children=l}return{$$typeof:t,type:e.type,key:a,ref:i,props:o,_owner:s}},e.createContext=function(e){return(e={$$typeof:s,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null}).Provider={$$typeof:i,_context:e},e.Consumer=e},e.createElement=k,e.createFactory=function(e){var t=k.bind(null,e);return t.type=e,t},e.createRef=function(){return{current:null}},e.forwardRef=function(e){return{$$typeof:l,render:e}},e.isValidElement=P,e.lazy=function(e){return{$$typeof:c,_payload:{_status:-1,_result:e},_init:j}},e.memo=function(e,t){return{$$typeof:d,type:e,compare:void 0===t?null:t}},e.startTransition=function(e){var t=O.transition;O.transition={};try{e()}finally{O.transition=t}},e.unstable_act=function(){throw Error("act(...) is not supported in production builds of React.")},e.useCallback=function(e,t){return L.current.useCallback(e,t)},e.useContext=function(e){return L.current.useContext(e)},e.useDebugValue=function(){},e.useDeferredValue=function(e){return L.current.useDeferredValue(e)},e.useEffect=function(e,t){return L.current.useEffect(e,t)},e.useId=function(){return L.current.useId()},e.useImperativeHandle=function(e,t,r){return L.current.useImperativeHandle(e,t,r)},e.useInsertionEffect=function(e,t){return L.current.useInsertionEffect(e,t)},e.useLayoutEffect=function(e,t){return L.current.useLayoutEffect(e,t)},e.useMemo=function(e,t){return L.current.useMemo(e,t)},e.useReducer=function(e,t,r){return L.current.useReducer(e,t,r)},e.useRef=function(e){return L.current.useRef(e)},e.useState=function(e){return L.current.useState(e)},e.useSyncExternalStore=function(e,t,r){return L.current.useSyncExternalStore(e,t,r)},e.useTransition=function(){return L.current.useTransition()},e.version="18.2.0"}});u({"../../../node_modules/.pnpm/react@18.2.0/node_modules/react/cjs/react.development.js"(e,t){}});var f=((e,t,r)=>(r=null!=e?n(s(e)):{},d(!t&&e&&e.__esModule?r:o(r,"default",{value:e,enumerable:!0}),e)))(u({"../../../node_modules/.pnpm/react@18.2.0/node_modules/react/index.js"(e,t){t.exports=c()}})()),p=e=>e?e.charAt(0).toUpperCase()+e.slice(1).toLowerCase():"";function g(e){return`${e}-${Math.floor(1e6*Math.random())}`}function m(e){for(let t in e)t.startsWith("on")&&delete e[t];return e}function v(e){if(!e||"object"!=typeof e)return"";try{return JSON.stringify(e)}catch(e){return""}}var b=()=>"19"===f.default.version.split(".")[0],y=e=>b()?e:e?"":void 0},81627:(e,t,r)=>{r.d(t,{v:()=>i});var n=r(73750),o=r(35421),a=r(52596);function i(...e){let t={...e[0]};for(let r=1;r<e.length;r++){let i=e[r];for(let e in i){let r=t[e],s=i[e];"function"==typeof r&&"function"==typeof s&&"o"===e[0]&&"n"===e[1]&&e.charCodeAt(2)>=65&&90>=e.charCodeAt(2)?t[e]=(0,n.c)(r,s):("className"===e||"UNSAFE_className"===e)&&"string"==typeof r&&"string"==typeof s?t[e]=(0,a.A)(r,s):"id"===e&&r&&s?t.id=(0,o.Tw)(r,s):t[e]=void 0!==s?s:r}}return t}},82076:(e,t,r)=>{r.d(t,{B:()=>s});var n=r(7684),o=r(34158);let a={offset:"stroke-dashoffset",array:"stroke-dasharray"},i={offset:"strokeDashoffset",array:"strokeDasharray"};function s(e,{attrX:t,attrY:r,attrScale:s,pathLength:l,pathSpacing:u=1,pathOffset:d=0,...c},f,p){if((0,n.O)(e,c,p),f){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};let{attrs:g,style:m}=e;g.transform&&(m.transform=g.transform,delete g.transform),(m.transform||g.transformOrigin)&&(m.transformOrigin=g.transformOrigin??"50% 50%",delete g.transformOrigin),m.transform&&(m.transformBox="fill-box",delete g.transformBox),void 0!==t&&(g.x=t),void 0!==r&&(g.y=r),void 0!==s&&(g.scale=s),void 0!==l&&function(e,t,r=1,n=0,s=!0){e.pathLength=1;let l=s?a:i;e[l.offset]=o.px.transform(-n);let u=o.px.transform(t),d=o.px.transform(r);e[l.array]=`${u} ${d}`}(g,l,u,d,!1)}},82885:(e,t,r)=>{r.d(t,{M:()=>o});var n=r(12115);function o(e){let t=(0,n.useRef)(null);return null===t.current&&(t.current=e()),t.current}},86176:(e,t,r)=>{r.d(t,{R:()=>o});var n={};function o(e,t,...r){let a=t?` [${t}]`:" ",i=`[Next UI]${a}: ${e}`;"undefined"!=typeof console&&(n[i]||(n[i]=!0))}},87123:(e,t,r)=>{r.d(t,{k:()=>n});let{schedule:n}=(0,r(58437).I)(queueMicrotask,!1)},87418:(e,t,r)=>{r.d(t,{T:()=>n,m:()=>o});let n=e=>{var t;return null!==(t=null==e?void 0:e.ownerDocument)&&void 0!==t?t:document},o=e=>e&&"window"in e&&e.window===e?e:n(e).defaultView||window},87826:(e,t,r)=>{r.d(t,{w:()=>o});var n=r(33205);function o(e,t){(0,n.N)(()=>{if(e&&e.ref&&t)return e.ref.current=t.current,()=>{e.ref&&(e.ref.current=null)}})}},88629:(e,t,r)=>{r.d(t,{l:()=>u});var n=r(86176),o=r(66680),a=r(81627),i=r(78257),s=r(71071),l=r(19914);function u(e,t){let r,{elementType:u="button",isDisabled:d,onPress:c,onPressStart:f,onPressEnd:p,onPressChange:g,preventFocusOnPress:m,allowFocusWhenDisabled:v,onClick:b,href:y,target:h,rel:w,type:x="button",allowTextSelectionOnPress:E}=e;r="button"===u?{type:x,disabled:d}:{role:"button",tabIndex:d?void 0:0,href:"a"!==u||d?void 0:y,target:"a"===u?h:void 0,type:"input"===u?x:void 0,disabled:"input"===u?d:void 0,"aria-disabled":d&&"input"!==u?d:void 0,rel:"a"===u?w:void 0};let T=(0,o.un)()||(0,o.m0)();b&&"function"==typeof b&&(0,n.R)("onClick is deprecated, please use onPress instead. See: https://github.com/nextui-org/nextui/issues/4292","useButton");let{pressProps:k,isPressed:P}=(0,l.d)({onPressStart:f,onPressEnd:p,onPressChange:g,onPress:e=>{T&&(null==b||b(e)),null==c||c(e)},isDisabled:d,preventFocusOnPress:m,allowTextSelectionOnPress:E,ref:t}),{focusableProps:C}=(0,s.W)(e,t);v&&(C.tabIndex=d?-1:C.tabIndex);let S=(0,a.v)(C,k,(0,i.$)(e,{labelable:!0}));return{isPressed:P,buttonProps:(0,a.v)(r,S,{"aria-haspopup":e["aria-haspopup"],"aria-expanded":e["aria-expanded"],"aria-controls":e["aria-controls"],"aria-pressed":e["aria-pressed"],onClick:e=>{("button"!==x||!T)&&(null==b||b(e))}})}}},90869:(e,t,r)=>{r.d(t,{L:()=>n});let n=(0,r(12115).createContext)({})},93095:(e,t,r)=>{r.d(t,{n:()=>n});let n=e=>"string"==typeof e&&"svg"===e.toLowerCase()},95500:(e,t,r)=>{r.d(t,{J:()=>s,D:()=>i});let n=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function o(e){return e.startsWith("while")||e.startsWith("drag")&&"draggable"!==e||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||n.has(e)}let a=e=>!o(e);function i(e){e&&(a=t=>t.startsWith("on")?!o(t):e(t))}try{i(require("@emotion/is-prop-valid").default)}catch{}function s(e,t,r){let n={};for(let i in e)("values"!==i||"object"!=typeof e.values)&&(a(i)||!0===r&&o(i)||!t&&!o(i)||e.draggable&&i.startsWith("onDrag"))&&(n[i]=e[i]);return n}},95902:(e,t,r)=>{r.d(t,{u:()=>o});var n=r(14570);function o(e){return(0,n.S)(e)?e.get():e}},97494:(e,t,r)=>{r.d(t,{E:()=>o});var n=r(12115);let o=r(68972).B?n.useLayoutEffect:n.useEffect},98312:(e,t,r)=>{r.d(t,{U:()=>n,_:()=>o});let n=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],o=["initial",...n]}}]);