{"version": 3, "sources": ["../../../../src/server/lib/incremental-cache/index.ts"], "sourcesContent": ["import type { CacheFs } from '../../../shared/lib/utils'\nimport type { PrerenderManifest } from '../../../build'\nimport {\n  type IncrementalCacheValue,\n  type IncrementalCacheEntry,\n  type IncrementalCache as IncrementalCacheType,\n  IncrementalCacheKind,\n  CachedRouteKind,\n  type IncrementalResponseCacheEntry,\n  type IncrementalFetchCacheEntry,\n  type GetIncrementalFetchCacheContext,\n  type GetIncrementalResponseCacheContext,\n  type CachedFetchValue,\n  type SetIncrementalFetchCacheContext,\n  type SetIncrementalResponseCacheContext,\n} from '../../response-cache'\nimport type { DeepReadonly } from '../../../shared/lib/deep-readonly'\n\nimport FileSystemCache from './file-system-cache'\nimport { normalizePagePath } from '../../../shared/lib/page-path/normalize-page-path'\n\nimport {\n  CACHE_ONE_YEAR,\n  NEXT_CACHE_REVALIDATED_TAGS_HEADER,\n  NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER,\n  PRERENDER_REVALIDATE_HEADER,\n} from '../../../lib/constants'\nimport { toRoute } from '../to-route'\nimport { SharedCacheControls } from './shared-cache-controls'\nimport { workUnitAsyncStorageInstance } from '../../app-render/work-unit-async-storage-instance'\nimport {\n  getPrerenderResumeDataCache,\n  getRenderResumeDataCache,\n} from '../../app-render/work-unit-async-storage.external'\nimport { getCacheHandlers } from '../../use-cache/handlers'\nimport { InvariantError } from '../../../shared/lib/invariant-error'\nimport type { Revalidate } from '../cache-control'\n\nexport interface CacheHandlerContext {\n  fs?: CacheFs\n  dev?: boolean\n  flushToDisk?: boolean\n  serverDistDir?: string\n  maxMemoryCacheSize?: number\n  fetchCacheKeyPrefix?: string\n  prerenderManifest?: PrerenderManifest\n  revalidatedTags: string[]\n  _requestHeaders: IncrementalCache['requestHeaders']\n}\n\nexport interface CacheHandlerValue {\n  lastModified?: number\n  age?: number\n  cacheState?: string\n  value: IncrementalCacheValue | null\n}\n\nexport class CacheHandler {\n  // eslint-disable-next-line\n  constructor(_ctx: CacheHandlerContext) {}\n\n  public async get(\n    _cacheKey: string,\n    _ctx: GetIncrementalFetchCacheContext | GetIncrementalResponseCacheContext\n  ): Promise<CacheHandlerValue | null> {\n    return {} as any\n  }\n\n  public async set(\n    _cacheKey: string,\n    _data: IncrementalCacheValue | null,\n    _ctx: SetIncrementalFetchCacheContext | SetIncrementalResponseCacheContext\n  ): Promise<void> {}\n\n  public async revalidateTag(\n    ..._args: Parameters<IncrementalCache['revalidateTag']>\n  ): Promise<void> {}\n\n  public resetRequestCache(): void {}\n}\n\nexport class IncrementalCache implements IncrementalCacheType {\n  readonly dev?: boolean\n  readonly disableForTestmode?: boolean\n  readonly cacheHandler?: CacheHandler\n  readonly hasCustomCacheHandler: boolean\n  readonly prerenderManifest: DeepReadonly<PrerenderManifest>\n  readonly requestHeaders: Record<string, undefined | string | string[]>\n  readonly requestProtocol?: 'http' | 'https'\n  readonly allowedRevalidateHeaderKeys?: string[]\n  readonly minimalMode?: boolean\n  readonly fetchCacheKeyPrefix?: string\n  readonly revalidatedTags?: string[]\n  readonly isOnDemandRevalidate?: boolean\n\n  private readonly locks = new Map<string, Promise<void>>()\n\n  /**\n   * The cache controls for routes. This will source the values from the\n   * prerender manifest until the in-memory cache is updated with new values.\n   */\n  private readonly cacheControls: SharedCacheControls\n\n  constructor({\n    fs,\n    dev,\n    flushToDisk,\n    minimalMode,\n    serverDistDir,\n    requestHeaders,\n    requestProtocol,\n    maxMemoryCacheSize,\n    getPrerenderManifest,\n    fetchCacheKeyPrefix,\n    CurCacheHandler,\n    allowedRevalidateHeaderKeys,\n  }: {\n    fs?: CacheFs\n    dev: boolean\n    minimalMode?: boolean\n    serverDistDir?: string\n    flushToDisk?: boolean\n    requestProtocol?: 'http' | 'https'\n    allowedRevalidateHeaderKeys?: string[]\n    requestHeaders: IncrementalCache['requestHeaders']\n    maxMemoryCacheSize?: number\n    getPrerenderManifest: () => DeepReadonly<PrerenderManifest>\n    fetchCacheKeyPrefix?: string\n    CurCacheHandler?: typeof CacheHandler\n  }) {\n    const debug = !!process.env.NEXT_PRIVATE_DEBUG_CACHE\n    this.hasCustomCacheHandler = Boolean(CurCacheHandler)\n\n    const cacheHandlersSymbol = Symbol.for('@next/cache-handlers')\n    const _globalThis: typeof globalThis & {\n      [cacheHandlersSymbol]?: {\n        FetchCache?: typeof CacheHandler\n      }\n    } = globalThis\n\n    if (!CurCacheHandler) {\n      // if we have a global cache handler available leverage it\n      const globalCacheHandler = _globalThis[cacheHandlersSymbol]\n\n      if (globalCacheHandler?.FetchCache) {\n        CurCacheHandler = globalCacheHandler.FetchCache\n      } else {\n        if (fs && serverDistDir) {\n          if (debug) {\n            console.log('using filesystem cache handler')\n          }\n          CurCacheHandler = FileSystemCache\n        }\n      }\n    } else if (debug) {\n      console.log('using custom cache handler', CurCacheHandler.name)\n    }\n\n    if (process.env.__NEXT_TEST_MAX_ISR_CACHE) {\n      // Allow cache size to be overridden for testing purposes\n      maxMemoryCacheSize = parseInt(process.env.__NEXT_TEST_MAX_ISR_CACHE, 10)\n    }\n    this.dev = dev\n    this.disableForTestmode = process.env.NEXT_PRIVATE_TEST_PROXY === 'true'\n    // this is a hack to avoid Webpack knowing this is equal to this.minimalMode\n    // because we replace this.minimalMode to true in production bundles.\n    const minimalModeKey = 'minimalMode'\n    this[minimalModeKey] = minimalMode\n    this.requestHeaders = requestHeaders\n    this.requestProtocol = requestProtocol\n    this.allowedRevalidateHeaderKeys = allowedRevalidateHeaderKeys\n    this.prerenderManifest = getPrerenderManifest()\n    this.cacheControls = new SharedCacheControls(this.prerenderManifest)\n    this.fetchCacheKeyPrefix = fetchCacheKeyPrefix\n    let revalidatedTags: string[] = []\n\n    if (\n      requestHeaders[PRERENDER_REVALIDATE_HEADER] ===\n      this.prerenderManifest?.preview?.previewModeId\n    ) {\n      this.isOnDemandRevalidate = true\n    }\n\n    if (\n      minimalMode &&\n      typeof requestHeaders[NEXT_CACHE_REVALIDATED_TAGS_HEADER] === 'string' &&\n      requestHeaders[NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER] ===\n        this.prerenderManifest?.preview?.previewModeId\n    ) {\n      revalidatedTags =\n        requestHeaders[NEXT_CACHE_REVALIDATED_TAGS_HEADER].split(',')\n    }\n\n    if (CurCacheHandler) {\n      this.cacheHandler = new CurCacheHandler({\n        dev,\n        fs,\n        flushToDisk,\n        serverDistDir,\n        revalidatedTags,\n        maxMemoryCacheSize,\n        _requestHeaders: requestHeaders,\n        fetchCacheKeyPrefix,\n      })\n    }\n  }\n\n  private calculateRevalidate(\n    pathname: string,\n    fromTime: number,\n    dev: boolean,\n    isFallback: boolean | undefined\n  ): Revalidate {\n    // in development we don't have a prerender-manifest\n    // and default to always revalidating to allow easier debugging\n    if (dev)\n      return Math.floor(performance.timeOrigin + performance.now() - 1000)\n\n    const cacheControl = this.cacheControls.get(toRoute(pathname))\n\n    // if an entry isn't present in routes we fallback to a default\n    // of revalidating after 1 second unless it's a fallback request.\n    const initialRevalidateSeconds = cacheControl\n      ? cacheControl.revalidate\n      : isFallback\n        ? false\n        : 1\n\n    const revalidateAfter =\n      typeof initialRevalidateSeconds === 'number'\n        ? initialRevalidateSeconds * 1000 + fromTime\n        : initialRevalidateSeconds\n\n    return revalidateAfter\n  }\n\n  _getPathname(pathname: string, fetchCache?: boolean) {\n    return fetchCache ? pathname : normalizePagePath(pathname)\n  }\n\n  resetRequestCache() {\n    this.cacheHandler?.resetRequestCache?.()\n  }\n\n  async lock(cacheKey: string) {\n    let unlockNext: () => Promise<void> = () => Promise.resolve()\n    const existingLock = this.locks.get(cacheKey)\n\n    if (existingLock) {\n      await existingLock\n    }\n\n    const newLock = new Promise<void>((resolve) => {\n      unlockNext = async () => {\n        resolve()\n        this.locks.delete(cacheKey) // Remove the lock upon release\n      }\n    })\n\n    this.locks.set(cacheKey, newLock)\n    return unlockNext\n  }\n\n  async revalidateTag(tags: string | string[]): Promise<void> {\n    const promises: Promise<void>[] = []\n\n    if (this.cacheHandler?.revalidateTag) {\n      promises.push(this.cacheHandler.revalidateTag(tags))\n    }\n\n    const handlers = getCacheHandlers()\n    if (handlers) {\n      tags = Array.isArray(tags) ? tags : [tags]\n      for (const handler of handlers) {\n        promises.push(handler.expireTags(...tags))\n      }\n    }\n\n    await Promise.all(promises)\n  }\n\n  // x-ref: https://github.com/facebook/react/blob/2655c9354d8e1c54ba888444220f63e836925caa/packages/react/src/ReactFetch.js#L23\n  async generateCacheKey(\n    url: string,\n    init: RequestInit | Request = {}\n  ): Promise<string> {\n    // this should be bumped anytime a fix is made to cache entries\n    // that should bust the cache\n    const MAIN_KEY_PREFIX = 'v3'\n\n    const bodyChunks: string[] = []\n\n    const encoder = new TextEncoder()\n    const decoder = new TextDecoder()\n\n    if (init.body) {\n      // handle ReadableStream body\n      if (typeof (init.body as any).getReader === 'function') {\n        const readableBody = init.body as ReadableStream<Uint8Array | string>\n\n        const chunks: Uint8Array[] = []\n\n        try {\n          await readableBody.pipeTo(\n            new WritableStream({\n              write(chunk) {\n                if (typeof chunk === 'string') {\n                  chunks.push(encoder.encode(chunk))\n                  bodyChunks.push(chunk)\n                } else {\n                  chunks.push(chunk)\n                  bodyChunks.push(decoder.decode(chunk, { stream: true }))\n                }\n              },\n            })\n          )\n\n          // Flush the decoder.\n          bodyChunks.push(decoder.decode())\n\n          // Create a new buffer with all the chunks.\n          const length = chunks.reduce((total, arr) => total + arr.length, 0)\n          const arrayBuffer = new Uint8Array(length)\n\n          // Push each of the chunks into the new array buffer.\n          let offset = 0\n          for (const chunk of chunks) {\n            arrayBuffer.set(chunk, offset)\n            offset += chunk.length\n          }\n\n          ;(init as any)._ogBody = arrayBuffer\n        } catch (err) {\n          console.error('Problem reading body', err)\n        }\n      } // handle FormData or URLSearchParams bodies\n      else if (typeof (init.body as any).keys === 'function') {\n        const formData = init.body as FormData\n        ;(init as any)._ogBody = init.body\n        for (const key of new Set([...formData.keys()])) {\n          const values = formData.getAll(key)\n          bodyChunks.push(\n            `${key}=${(\n              await Promise.all(\n                values.map(async (val) => {\n                  if (typeof val === 'string') {\n                    return val\n                  } else {\n                    return await val.text()\n                  }\n                })\n              )\n            ).join(',')}`\n          )\n        }\n        // handle blob body\n      } else if (typeof (init.body as any).arrayBuffer === 'function') {\n        const blob = init.body as Blob\n        const arrayBuffer = await blob.arrayBuffer()\n        bodyChunks.push(await blob.text())\n        ;(init as any)._ogBody = new Blob([arrayBuffer], { type: blob.type })\n      } else if (typeof init.body === 'string') {\n        bodyChunks.push(init.body)\n        ;(init as any)._ogBody = init.body\n      }\n    }\n\n    const headers =\n      typeof (init.headers || {}).keys === 'function'\n        ? Object.fromEntries(init.headers as Headers)\n        : Object.assign({}, init.headers)\n\n    // w3c trace context headers can break request caching and deduplication\n    // so we remove them from the cache key\n    if ('traceparent' in headers) delete headers['traceparent']\n    if ('tracestate' in headers) delete headers['tracestate']\n\n    const cacheString = JSON.stringify([\n      MAIN_KEY_PREFIX,\n      this.fetchCacheKeyPrefix || '',\n      url,\n      init.method,\n      headers,\n      init.mode,\n      init.redirect,\n      init.credentials,\n      init.referrer,\n      init.referrerPolicy,\n      init.integrity,\n      init.cache,\n      bodyChunks,\n    ])\n\n    if (process.env.NEXT_RUNTIME === 'edge') {\n      function bufferToHex(buffer: ArrayBuffer): string {\n        return Array.prototype.map\n          .call(new Uint8Array(buffer), (b) => b.toString(16).padStart(2, '0'))\n          .join('')\n      }\n      const buffer = encoder.encode(cacheString)\n      return bufferToHex(await crypto.subtle.digest('SHA-256', buffer))\n    } else {\n      const crypto = require('crypto') as typeof import('crypto')\n      return crypto.createHash('sha256').update(cacheString).digest('hex')\n    }\n  }\n\n  async get(\n    cacheKey: string,\n    ctx: GetIncrementalFetchCacheContext\n  ): Promise<IncrementalFetchCacheEntry | null>\n  async get(\n    cacheKey: string,\n    ctx: GetIncrementalResponseCacheContext\n  ): Promise<IncrementalResponseCacheEntry | null>\n  async get(\n    cacheKey: string,\n    ctx: GetIncrementalFetchCacheContext | GetIncrementalResponseCacheContext\n  ): Promise<IncrementalCacheEntry | null> {\n    // Unlike other caches if we have a resume data cache, we use it even if\n    // testmode would normally disable it or if requestHeaders say 'no-cache'.\n    if (ctx.kind === IncrementalCacheKind.FETCH) {\n      const workUnitStore = workUnitAsyncStorageInstance.getStore()\n      const resumeDataCache = workUnitStore\n        ? getRenderResumeDataCache(workUnitStore)\n        : null\n      if (resumeDataCache) {\n        const memoryCacheData = resumeDataCache.fetch.get(cacheKey)\n        if (memoryCacheData?.kind === CachedRouteKind.FETCH) {\n          return { isStale: false, value: memoryCacheData }\n        }\n      }\n    }\n\n    // we don't leverage the prerender cache in dev mode\n    // so that getStaticProps is always called for easier debugging\n    if (\n      this.disableForTestmode ||\n      (this.dev &&\n        (ctx.kind !== IncrementalCacheKind.FETCH ||\n          this.requestHeaders['cache-control'] === 'no-cache'))\n    ) {\n      return null\n    }\n\n    cacheKey = this._getPathname(\n      cacheKey,\n      ctx.kind === IncrementalCacheKind.FETCH\n    )\n\n    const cacheData = await this.cacheHandler?.get(cacheKey, ctx)\n\n    if (ctx.kind === IncrementalCacheKind.FETCH) {\n      if (!cacheData) {\n        return null\n      }\n\n      if (cacheData.value?.kind !== CachedRouteKind.FETCH) {\n        throw new InvariantError(\n          `Expected cached value for cache key ${JSON.stringify(cacheKey)} to be a \"FETCH\" kind, got ${JSON.stringify(cacheData.value?.kind)} instead.`\n        )\n      }\n\n      const combinedTags = [...(ctx.tags || []), ...(ctx.softTags || [])]\n      // if a tag was revalidated we don't return stale data\n      if (\n        combinedTags.some((tag) => {\n          return this.revalidatedTags?.includes(tag)\n        })\n      ) {\n        return null\n      }\n\n      const revalidate = ctx.revalidate || cacheData.value.revalidate\n      const age =\n        (performance.timeOrigin +\n          performance.now() -\n          (cacheData.lastModified || 0)) /\n        1000\n\n      const isStale = age > revalidate\n      const data = cacheData.value.data\n\n      return {\n        isStale,\n        value: { kind: CachedRouteKind.FETCH, data, revalidate },\n      }\n    } else if (cacheData?.value?.kind === CachedRouteKind.FETCH) {\n      throw new InvariantError(\n        `Expected cached value for cache key ${JSON.stringify(cacheKey)} not to be a ${JSON.stringify(ctx.kind)} kind, got \"FETCH\" instead.`\n      )\n    }\n\n    let entry: IncrementalResponseCacheEntry | null = null\n    const { isFallback } = ctx\n    const cacheControl = this.cacheControls.get(toRoute(cacheKey))\n\n    let isStale: boolean | -1 | undefined\n    let revalidateAfter: Revalidate\n\n    if (cacheData?.lastModified === -1) {\n      isStale = -1\n      revalidateAfter = -1 * CACHE_ONE_YEAR\n    } else {\n      revalidateAfter = this.calculateRevalidate(\n        cacheKey,\n        cacheData?.lastModified || performance.timeOrigin + performance.now(),\n        this.dev ?? false,\n        ctx.isFallback\n      )\n      isStale =\n        revalidateAfter !== false &&\n        revalidateAfter < performance.timeOrigin + performance.now()\n          ? true\n          : undefined\n    }\n\n    if (cacheData) {\n      entry = {\n        isStale,\n        cacheControl,\n        revalidateAfter,\n        value: cacheData.value,\n        isFallback,\n      }\n    }\n\n    if (\n      !cacheData &&\n      this.prerenderManifest.notFoundRoutes.includes(cacheKey)\n    ) {\n      // for the first hit after starting the server the cache\n      // may not have a way to save notFound: true so if\n      // the prerender-manifest marks this as notFound then we\n      // return that entry and trigger a cache set to give it a\n      // chance to update in-memory entries\n      entry = {\n        isStale,\n        value: null,\n        cacheControl,\n        revalidateAfter,\n        isFallback,\n      }\n      this.set(cacheKey, entry.value, { ...ctx, cacheControl })\n    }\n    return entry\n  }\n\n  async set(\n    pathname: string,\n    data: CachedFetchValue | null,\n    ctx: SetIncrementalFetchCacheContext\n  ): Promise<void>\n  async set(\n    pathname: string,\n    data: Exclude<IncrementalCacheValue, CachedFetchValue> | null,\n    ctx: SetIncrementalResponseCacheContext\n  ): Promise<void>\n  async set(\n    pathname: string,\n    data: IncrementalCacheValue | null,\n    ctx: SetIncrementalFetchCacheContext | SetIncrementalResponseCacheContext\n  ): Promise<void> {\n    // Even if we otherwise disable caching for testMode or if no fetchCache is\n    // configured we still always stash results in the resume data cache if one\n    // exists. This is because this is a transient in memory cache that\n    // populates caches ahead of a dynamic render in dev mode to allow the RSC\n    // debug info to have the right environment associated to it.\n    if (data?.kind === CachedRouteKind.FETCH) {\n      const workUnitStore = workUnitAsyncStorageInstance.getStore()\n      const prerenderResumeDataCache = workUnitStore\n        ? getPrerenderResumeDataCache(workUnitStore)\n        : null\n      if (prerenderResumeDataCache) {\n        prerenderResumeDataCache.fetch.set(pathname, data)\n      }\n    }\n\n    if (this.disableForTestmode || (this.dev && !ctx.fetchCache)) return\n\n    pathname = this._getPathname(pathname, ctx.fetchCache)\n\n    // FetchCache has upper limit of 2MB per-entry currently\n    const itemSize = JSON.stringify(data).length\n    if (\n      ctx.fetchCache &&\n      // we don't show this error/warning when a custom cache handler is being used\n      // as it might not have this limit\n      !this.hasCustomCacheHandler &&\n      itemSize > 2 * 1024 * 1024\n    ) {\n      if (this.dev) {\n        throw new Error(\n          `Failed to set Next.js data cache, items over 2MB can not be cached (${itemSize} bytes)`\n        )\n      }\n      return\n    }\n\n    try {\n      if (!ctx.fetchCache && ctx.cacheControl) {\n        this.cacheControls.set(toRoute(pathname), ctx.cacheControl)\n      }\n\n      await this.cacheHandler?.set(pathname, data, ctx)\n    } catch (error) {\n      console.warn('Failed to update prerender cache for', pathname, error)\n    }\n  }\n}\n"], "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "IncrementalCache", "constructor", "_ctx", "get", "_cacheKey", "set", "_data", "revalidateTag", "_args", "resetRequestCache", "fs", "dev", "flushToDisk", "minimalMode", "serverDistDir", "requestHeaders", "requestProtocol", "maxMemoryCacheSize", "getPrerenderManifest", "fetchCacheKeyPrefix", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "allowedRevalidateHeaderKeys", "locks", "Map", "debug", "process", "env", "NEXT_PRIVATE_DEBUG_CACHE", "hasCustomCacheHandler", "Boolean", "cacheHandlersSymbol", "Symbol", "for", "_globalThis", "globalThis", "globalCacheHandler", "<PERSON><PERSON><PERSON><PERSON>", "console", "log", "FileSystemCache", "name", "__NEXT_TEST_MAX_ISR_CACHE", "parseInt", "disableForTestmode", "NEXT_PRIVATE_TEST_PROXY", "minimalModeKey", "prerenderManifest", "cacheControls", "SharedCacheControls", "revalidatedTags", "PRERENDER_REVALIDATE_HEADER", "preview", "previewModeId", "isOnDemandRevalidate", "NEXT_CACHE_REVALIDATED_TAGS_HEADER", "NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER", "split", "cache<PERSON><PERSON><PERSON>", "_requestHeaders", "calculateRevalidate", "pathname", "fromTime", "<PERSON><PERSON><PERSON><PERSON>", "Math", "floor", "performance", "<PERSON><PERSON><PERSON><PERSON>", "now", "cacheControl", "toRoute", "initialRevalidateSeconds", "revalidate", "revalidateAfter", "_getPathname", "fetchCache", "normalizePagePath", "lock", "cache<PERSON>ey", "unlockNext", "Promise", "resolve", "existingLock", "newLock", "delete", "tags", "promises", "push", "handlers", "getCacheHandlers", "Array", "isArray", "handler", "expireTags", "all", "generate<PERSON>ache<PERSON>ey", "url", "init", "MAIN_KEY_PREFIX", "bodyChunks", "encoder", "TextEncoder", "decoder", "TextDecoder", "body", "<PERSON><PERSON><PERSON><PERSON>", "readableBody", "chunks", "pipeTo", "WritableStream", "write", "chunk", "encode", "decode", "stream", "length", "reduce", "total", "arr", "arrayBuffer", "Uint8Array", "offset", "_ogBody", "err", "error", "keys", "formData", "key", "Set", "values", "getAll", "map", "val", "text", "join", "blob", "Blob", "type", "headers", "Object", "fromEntries", "assign", "cacheString", "JSON", "stringify", "method", "mode", "redirect", "credentials", "referrer", "referrerPolicy", "integrity", "cache", "NEXT_RUNTIME", "bufferToHex", "buffer", "prototype", "call", "b", "toString", "padStart", "crypto", "subtle", "digest", "require", "createHash", "update", "ctx", "cacheData", "kind", "IncrementalCacheKind", "FETCH", "workUnitStore", "workUnitAsyncStorageInstance", "getStore", "resumeDataCache", "getRenderResumeDataCache", "memoryCacheData", "fetch", "CachedRouteKind", "isStale", "value", "InvariantError", "combinedTags", "softTags", "some", "tag", "includes", "age", "lastModified", "data", "entry", "CACHE_ONE_YEAR", "undefined", "notFoundRoutes", "prerenderResumeDataCache", "getPrerenderResumeDataCache", "itemSize", "Error", "warn"], "mappings": ";;;;;;;;;;;;;;;IAyDaA,YAAY;eAAZA;;IAwBAC,gBAAgB;eAAhBA;;;+BAlEN;wEAGqB;mCACM;2BAO3B;yBACiB;qCACY;8CACS;8CAItC;0BAC0B;gCACF;;;;;;AAsBxB,MAAMD;IACX,2BAA2B;IAC3BE,YAAYC,IAAyB,CAAE,CAAC;IAExC,MAAaC,IACXC,SAAiB,EACjBF,IAA0E,EACvC;QACnC,OAAO,CAAC;IACV;IAEA,MAAaG,IACXD,SAAiB,EACjBE,KAAmC,EACnCJ,IAA0E,EAC3D,CAAC;IAElB,MAAaK,cACX,GAAGC,KAAoD,EACxC,CAAC;IAEXC,oBAA0B,CAAC;AACpC;AAEO,MAAMT;IAsBXC,YAAY,EACVS,EAAE,EACFC,GAAG,EACHC,WAAW,EACXC,WAAW,EACXC,aAAa,EACbC,cAAc,EACdC,eAAe,EACfC,kBAAkB,EAClBC,oBAAoB,EACpBC,mBAAmB,EACnBC,eAAe,EACfC,2BAA2B,EAc5B,CAAE;YAiDC,iCAAA,yBASE,kCAAA;aA5FWC,QAAQ,IAAIC;QAmC3B,MAAMC,QAAQ,CAAC,CAACC,QAAQC,GAAG,CAACC,wBAAwB;QACpD,IAAI,CAACC,qBAAqB,GAAGC,QAAQT;QAErC,MAAMU,sBAAsBC,OAAOC,GAAG,CAAC;QACvC,MAAMC,cAIFC;QAEJ,IAAI,CAACd,iBAAiB;YACpB,0DAA0D;YAC1D,MAAMe,qBAAqBF,WAAW,CAACH,oBAAoB;YAE3D,IAAIK,sCAAAA,mBAAoBC,UAAU,EAAE;gBAClChB,kBAAkBe,mBAAmBC,UAAU;YACjD,OAAO;gBACL,IAAI1B,MAAMI,eAAe;oBACvB,IAAIU,OAAO;wBACTa,QAAQC,GAAG,CAAC;oBACd;oBACAlB,kBAAkBmB,wBAAe;gBACnC;YACF;QACF,OAAO,IAAIf,OAAO;YAChBa,QAAQC,GAAG,CAAC,8BAA8BlB,gBAAgBoB,IAAI;QAChE;QAEA,IAAIf,QAAQC,GAAG,CAACe,yBAAyB,EAAE;YACzC,yDAAyD;YACzDxB,qBAAqByB,SAASjB,QAAQC,GAAG,CAACe,yBAAyB,EAAE;QACvE;QACA,IAAI,CAAC9B,GAAG,GAAGA;QACX,IAAI,CAACgC,kBAAkB,GAAGlB,QAAQC,GAAG,CAACkB,uBAAuB,KAAK;QAClE,4EAA4E;QAC5E,qEAAqE;QACrE,MAAMC,iBAAiB;QACvB,IAAI,CAACA,eAAe,GAAGhC;QACvB,IAAI,CAACE,cAAc,GAAGA;QACtB,IAAI,CAACC,eAAe,GAAGA;QACvB,IAAI,CAACK,2BAA2B,GAAGA;QACnC,IAAI,CAACyB,iBAAiB,GAAG5B;QACzB,IAAI,CAAC6B,aAAa,GAAG,IAAIC,wCAAmB,CAAC,IAAI,CAACF,iBAAiB;QACnE,IAAI,CAAC3B,mBAAmB,GAAGA;QAC3B,IAAI8B,kBAA4B,EAAE;QAElC,IACElC,cAAc,CAACmC,sCAA2B,CAAC,OAC3C,0BAAA,IAAI,CAACJ,iBAAiB,sBAAtB,kCAAA,wBAAwBK,OAAO,qBAA/B,gCAAiCC,aAAa,GAC9C;YACA,IAAI,CAACC,oBAAoB,GAAG;QAC9B;QAEA,IACExC,eACA,OAAOE,cAAc,CAACuC,6CAAkC,CAAC,KAAK,YAC9DvC,cAAc,CAACwC,iDAAsC,CAAC,OACpD,2BAAA,IAAI,CAACT,iBAAiB,sBAAtB,mCAAA,yBAAwBK,OAAO,qBAA/B,iCAAiCC,aAAa,GAChD;YACAH,kBACElC,cAAc,CAACuC,6CAAkC,CAAC,CAACE,KAAK,CAAC;QAC7D;QAEA,IAAIpC,iBAAiB;YACnB,IAAI,CAACqC,YAAY,GAAG,IAAIrC,gBAAgB;gBACtCT;gBACAD;gBACAE;gBACAE;gBACAmC;gBACAhC;gBACAyC,iBAAiB3C;gBACjBI;YACF;QACF;IACF;IAEQwC,oBACNC,QAAgB,EAChBC,QAAgB,EAChBlD,GAAY,EACZmD,UAA+B,EACnB;QACZ,oDAAoD;QACpD,+DAA+D;QAC/D,IAAInD,KACF,OAAOoD,KAAKC,KAAK,CAACC,YAAYC,UAAU,GAAGD,YAAYE,GAAG,KAAK;QAEjE,MAAMC,eAAe,IAAI,CAACrB,aAAa,CAAC5C,GAAG,CAACkE,IAAAA,gBAAO,EAACT;QAEpD,+DAA+D;QAC/D,iEAAiE;QACjE,MAAMU,2BAA2BF,eAC7BA,aAAaG,UAAU,GACvBT,aACE,QACA;QAEN,MAAMU,kBACJ,OAAOF,6BAA6B,WAChCA,2BAA2B,OAAOT,WAClCS;QAEN,OAAOE;IACT;IAEAC,aAAab,QAAgB,EAAEc,UAAoB,EAAE;QACnD,OAAOA,aAAad,WAAWe,IAAAA,oCAAiB,EAACf;IACnD;IAEAnD,oBAAoB;YAClB,sCAAA;SAAA,qBAAA,IAAI,CAACgD,YAAY,sBAAjB,uCAAA,mBAAmBhD,iBAAiB,qBAApC,0CAAA;IACF;IAEA,MAAMmE,KAAKC,QAAgB,EAAE;QAC3B,IAAIC,aAAkC,IAAMC,QAAQC,OAAO;QAC3D,MAAMC,eAAe,IAAI,CAAC3D,KAAK,CAACnB,GAAG,CAAC0E;QAEpC,IAAII,cAAc;YAChB,MAAMA;QACR;QAEA,MAAMC,UAAU,IAAIH,QAAc,CAACC;YACjCF,aAAa;gBACXE;gBACA,IAAI,CAAC1D,KAAK,CAAC6D,MAAM,CAACN,UAAU,+BAA+B;;YAC7D;QACF;QAEA,IAAI,CAACvD,KAAK,CAACjB,GAAG,CAACwE,UAAUK;QACzB,OAAOJ;IACT;IAEA,MAAMvE,cAAc6E,IAAuB,EAAiB;YAGtD;QAFJ,MAAMC,WAA4B,EAAE;QAEpC,KAAI,qBAAA,IAAI,CAAC5B,YAAY,qBAAjB,mBAAmBlD,aAAa,EAAE;YACpC8E,SAASC,IAAI,CAAC,IAAI,CAAC7B,YAAY,CAAClD,aAAa,CAAC6E;QAChD;QAEA,MAAMG,WAAWC,IAAAA,0BAAgB;QACjC,IAAID,UAAU;YACZH,OAAOK,MAAMC,OAAO,CAACN,QAAQA,OAAO;gBAACA;aAAK;YAC1C,KAAK,MAAMO,WAAWJ,SAAU;gBAC9BF,SAASC,IAAI,CAACK,QAAQC,UAAU,IAAIR;YACtC;QACF;QAEA,MAAML,QAAQc,GAAG,CAACR;IACpB;IAEA,8HAA8H;IAC9H,MAAMS,iBACJC,GAAW,EACXC,OAA8B,CAAC,CAAC,EACf;QACjB,+DAA+D;QAC/D,6BAA6B;QAC7B,MAAMC,kBAAkB;QAExB,MAAMC,aAAuB,EAAE;QAE/B,MAAMC,UAAU,IAAIC;QACpB,MAAMC,UAAU,IAAIC;QAEpB,IAAIN,KAAKO,IAAI,EAAE;YACb,6BAA6B;YAC7B,IAAI,OAAO,AAACP,KAAKO,IAAI,CAASC,SAAS,KAAK,YAAY;gBACtD,MAAMC,eAAeT,KAAKO,IAAI;gBAE9B,MAAMG,SAAuB,EAAE;gBAE/B,IAAI;oBACF,MAAMD,aAAaE,MAAM,CACvB,IAAIC,eAAe;wBACjBC,OAAMC,KAAK;4BACT,IAAI,OAAOA,UAAU,UAAU;gCAC7BJ,OAAOpB,IAAI,CAACa,QAAQY,MAAM,CAACD;gCAC3BZ,WAAWZ,IAAI,CAACwB;4BAClB,OAAO;gCACLJ,OAAOpB,IAAI,CAACwB;gCACZZ,WAAWZ,IAAI,CAACe,QAAQW,MAAM,CAACF,OAAO;oCAAEG,QAAQ;gCAAK;4BACvD;wBACF;oBACF;oBAGF,qBAAqB;oBACrBf,WAAWZ,IAAI,CAACe,QAAQW,MAAM;oBAE9B,2CAA2C;oBAC3C,MAAME,SAASR,OAAOS,MAAM,CAAC,CAACC,OAAOC,MAAQD,QAAQC,IAAIH,MAAM,EAAE;oBACjE,MAAMI,cAAc,IAAIC,WAAWL;oBAEnC,qDAAqD;oBACrD,IAAIM,SAAS;oBACb,KAAK,MAAMV,SAASJ,OAAQ;wBAC1BY,YAAYjH,GAAG,CAACyG,OAAOU;wBACvBA,UAAUV,MAAMI,MAAM;oBACxB;;oBAEElB,KAAayB,OAAO,GAAGH;gBAC3B,EAAE,OAAOI,KAAK;oBACZrF,QAAQsF,KAAK,CAAC,wBAAwBD;gBACxC;YACF,OACK,IAAI,OAAO,AAAC1B,KAAKO,IAAI,CAASqB,IAAI,KAAK,YAAY;gBACtD,MAAMC,WAAW7B,KAAKO,IAAI;gBACxBP,KAAayB,OAAO,GAAGzB,KAAKO,IAAI;gBAClC,KAAK,MAAMuB,OAAO,IAAIC,IAAI;uBAAIF,SAASD,IAAI;iBAAG,EAAG;oBAC/C,MAAMI,SAASH,SAASI,MAAM,CAACH;oBAC/B5B,WAAWZ,IAAI,CACb,GAAGwC,IAAI,CAAC,EAAE,AACR,CAAA,MAAM/C,QAAQc,GAAG,CACfmC,OAAOE,GAAG,CAAC,OAAOC;wBAChB,IAAI,OAAOA,QAAQ,UAAU;4BAC3B,OAAOA;wBACT,OAAO;4BACL,OAAO,MAAMA,IAAIC,IAAI;wBACvB;oBACF,GACF,EACAC,IAAI,CAAC,MAAM;gBAEjB;YACA,mBAAmB;YACrB,OAAO,IAAI,OAAO,AAACrC,KAAKO,IAAI,CAASe,WAAW,KAAK,YAAY;gBAC/D,MAAMgB,OAAOtC,KAAKO,IAAI;gBACtB,MAAMe,cAAc,MAAMgB,KAAKhB,WAAW;gBAC1CpB,WAAWZ,IAAI,CAAC,MAAMgD,KAAKF,IAAI;gBAC7BpC,KAAayB,OAAO,GAAG,IAAIc,KAAK;oBAACjB;iBAAY,EAAE;oBAAEkB,MAAMF,KAAKE,IAAI;gBAAC;YACrE,OAAO,IAAI,OAAOxC,KAAKO,IAAI,KAAK,UAAU;gBACxCL,WAAWZ,IAAI,CAACU,KAAKO,IAAI;gBACvBP,KAAayB,OAAO,GAAGzB,KAAKO,IAAI;YACpC;QACF;QAEA,MAAMkC,UACJ,OAAO,AAACzC,CAAAA,KAAKyC,OAAO,IAAI,CAAC,CAAA,EAAGb,IAAI,KAAK,aACjCc,OAAOC,WAAW,CAAC3C,KAAKyC,OAAO,IAC/BC,OAAOE,MAAM,CAAC,CAAC,GAAG5C,KAAKyC,OAAO;QAEpC,wEAAwE;QACxE,uCAAuC;QACvC,IAAI,iBAAiBA,SAAS,OAAOA,OAAO,CAAC,cAAc;QAC3D,IAAI,gBAAgBA,SAAS,OAAOA,OAAO,CAAC,aAAa;QAEzD,MAAMI,cAAcC,KAAKC,SAAS,CAAC;YACjC9C;YACA,IAAI,CAAC9E,mBAAmB,IAAI;YAC5B4E;YACAC,KAAKgD,MAAM;YACXP;YACAzC,KAAKiD,IAAI;YACTjD,KAAKkD,QAAQ;YACblD,KAAKmD,WAAW;YAChBnD,KAAKoD,QAAQ;YACbpD,KAAKqD,cAAc;YACnBrD,KAAKsD,SAAS;YACdtD,KAAKuD,KAAK;YACVrD;SACD;QAED,IAAIzE,QAAQC,GAAG,CAAC8H,YAAY,KAAK,QAAQ;YACvC,SAASC,YAAYC,MAAmB;gBACtC,OAAOjE,MAAMkE,SAAS,CAACzB,GAAG,CACvB0B,IAAI,CAAC,IAAIrC,WAAWmC,SAAS,CAACG,IAAMA,EAAEC,QAAQ,CAAC,IAAIC,QAAQ,CAAC,GAAG,MAC/D1B,IAAI,CAAC;YACV;YACA,MAAMqB,SAASvD,QAAQY,MAAM,CAAC8B;YAC9B,OAAOY,YAAY,MAAMO,OAAOC,MAAM,CAACC,MAAM,CAAC,WAAWR;QAC3D,OAAO;YACL,MAAMM,UAASG,QAAQ;YACvB,OAAOH,QAAOI,UAAU,CAAC,UAAUC,MAAM,CAACxB,aAAaqB,MAAM,CAAC;QAChE;IACF;IAUA,MAAM/J,IACJ0E,QAAgB,EAChByF,GAAyE,EAClC;YAgCf,oBAqCbC;QApEX,wEAAwE;QACxE,0EAA0E;QAC1E,IAAID,IAAIE,IAAI,KAAKC,mCAAoB,CAACC,KAAK,EAAE;YAC3C,MAAMC,gBAAgBC,0DAA4B,CAACC,QAAQ;YAC3D,MAAMC,kBAAkBH,gBACpBI,IAAAA,sDAAwB,EAACJ,iBACzB;YACJ,IAAIG,iBAAiB;gBACnB,MAAME,kBAAkBF,gBAAgBG,KAAK,CAAC9K,GAAG,CAAC0E;gBAClD,IAAImG,CAAAA,mCAAAA,gBAAiBR,IAAI,MAAKU,8BAAe,CAACR,KAAK,EAAE;oBACnD,OAAO;wBAAES,SAAS;wBAAOC,OAAOJ;oBAAgB;gBAClD;YACF;QACF;QAEA,oDAAoD;QACpD,+DAA+D;QAC/D,IACE,IAAI,CAACrI,kBAAkB,IACtB,IAAI,CAAChC,GAAG,IACN2J,CAAAA,IAAIE,IAAI,KAAKC,mCAAoB,CAACC,KAAK,IACtC,IAAI,CAAC3J,cAAc,CAAC,gBAAgB,KAAK,UAAS,GACtD;YACA,OAAO;QACT;QAEA8D,WAAW,IAAI,CAACJ,YAAY,CAC1BI,UACAyF,IAAIE,IAAI,KAAKC,mCAAoB,CAACC,KAAK;QAGzC,MAAMH,YAAY,QAAM,qBAAA,IAAI,CAAC9G,YAAY,qBAAjB,mBAAmBtD,GAAG,CAAC0E,UAAUyF;QAEzD,IAAIA,IAAIE,IAAI,KAAKC,mCAAoB,CAACC,KAAK,EAAE;gBAKvCH;YAJJ,IAAI,CAACA,WAAW;gBACd,OAAO;YACT;YAEA,IAAIA,EAAAA,oBAAAA,UAAUa,KAAK,qBAAfb,kBAAiBC,IAAI,MAAKU,8BAAe,CAACR,KAAK,EAAE;oBAE2DH;gBAD9G,MAAM,qBAEL,CAFK,IAAIc,8BAAc,CACtB,CAAC,oCAAoC,EAAEvC,KAAKC,SAAS,CAAClE,UAAU,2BAA2B,EAAEiE,KAAKC,SAAS,EAACwB,oBAAAA,UAAUa,KAAK,qBAAfb,kBAAiBC,IAAI,EAAE,SAAS,CAAC,GADzI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YAEA,MAAMc,eAAe;mBAAKhB,IAAIlF,IAAI,IAAI,EAAE;mBAAOkF,IAAIiB,QAAQ,IAAI,EAAE;aAAE;YACnE,sDAAsD;YACtD,IACED,aAAaE,IAAI,CAAC,CAACC;oBACV;gBAAP,QAAO,wBAAA,IAAI,CAACxI,eAAe,qBAApB,sBAAsByI,QAAQ,CAACD;YACxC,IACA;gBACA,OAAO;YACT;YAEA,MAAMlH,aAAa+F,IAAI/F,UAAU,IAAIgG,UAAUa,KAAK,CAAC7G,UAAU;YAC/D,MAAMoH,MACJ,AAAC1H,CAAAA,YAAYC,UAAU,GACrBD,YAAYE,GAAG,KACdoG,CAAAA,UAAUqB,YAAY,IAAI,CAAA,CAAC,IAC9B;YAEF,MAAMT,UAAUQ,MAAMpH;YACtB,MAAMsH,OAAOtB,UAAUa,KAAK,CAACS,IAAI;YAEjC,OAAO;gBACLV;gBACAC,OAAO;oBAAEZ,MAAMU,8BAAe,CAACR,KAAK;oBAAEmB;oBAAMtH;gBAAW;YACzD;QACF,OAAO,IAAIgG,CAAAA,8BAAAA,mBAAAA,UAAWa,KAAK,qBAAhBb,iBAAkBC,IAAI,MAAKU,8BAAe,CAACR,KAAK,EAAE;YAC3D,MAAM,qBAEL,CAFK,IAAIW,8BAAc,CACtB,CAAC,oCAAoC,EAAEvC,KAAKC,SAAS,CAAClE,UAAU,aAAa,EAAEiE,KAAKC,SAAS,CAACuB,IAAIE,IAAI,EAAE,2BAA2B,CAAC,GADhI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IAAIsB,QAA8C;QAClD,MAAM,EAAEhI,UAAU,EAAE,GAAGwG;QACvB,MAAMlG,eAAe,IAAI,CAACrB,aAAa,CAAC5C,GAAG,CAACkE,IAAAA,gBAAO,EAACQ;QAEpD,IAAIsG;QACJ,IAAI3G;QAEJ,IAAI+F,CAAAA,6BAAAA,UAAWqB,YAAY,MAAK,CAAC,GAAG;YAClCT,UAAU,CAAC;YACX3G,kBAAkB,CAAC,IAAIuH,yBAAc;QACvC,OAAO;YACLvH,kBAAkB,IAAI,CAACb,mBAAmB,CACxCkB,UACA0F,CAAAA,6BAAAA,UAAWqB,YAAY,KAAI3H,YAAYC,UAAU,GAAGD,YAAYE,GAAG,IACnE,IAAI,CAACxD,GAAG,IAAI,OACZ2J,IAAIxG,UAAU;YAEhBqH,UACE3G,oBAAoB,SACpBA,kBAAkBP,YAAYC,UAAU,GAAGD,YAAYE,GAAG,KACtD,OACA6H;QACR;QAEA,IAAIzB,WAAW;YACbuB,QAAQ;gBACNX;gBACA/G;gBACAI;gBACA4G,OAAOb,UAAUa,KAAK;gBACtBtH;YACF;QACF;QAEA,IACE,CAACyG,aACD,IAAI,CAACzH,iBAAiB,CAACmJ,cAAc,CAACP,QAAQ,CAAC7G,WAC/C;YACA,wDAAwD;YACxD,kDAAkD;YAClD,wDAAwD;YACxD,yDAAyD;YACzD,qCAAqC;YACrCiH,QAAQ;gBACNX;gBACAC,OAAO;gBACPhH;gBACAI;gBACAV;YACF;YACA,IAAI,CAACzD,GAAG,CAACwE,UAAUiH,MAAMV,KAAK,EAAE;gBAAE,GAAGd,GAAG;gBAAElG;YAAa;QACzD;QACA,OAAO0H;IACT;IAYA,MAAMzL,IACJuD,QAAgB,EAChBiI,IAAkC,EAClCvB,GAAyE,EAC1D;QACf,2EAA2E;QAC3E,2EAA2E;QAC3E,mEAAmE;QACnE,0EAA0E;QAC1E,6DAA6D;QAC7D,IAAIuB,CAAAA,wBAAAA,KAAMrB,IAAI,MAAKU,8BAAe,CAACR,KAAK,EAAE;YACxC,MAAMC,gBAAgBC,0DAA4B,CAACC,QAAQ;YAC3D,MAAMqB,2BAA2BvB,gBAC7BwB,IAAAA,yDAA2B,EAACxB,iBAC5B;YACJ,IAAIuB,0BAA0B;gBAC5BA,yBAAyBjB,KAAK,CAAC5K,GAAG,CAACuD,UAAUiI;YAC/C;QACF;QAEA,IAAI,IAAI,CAAClJ,kBAAkB,IAAK,IAAI,CAAChC,GAAG,IAAI,CAAC2J,IAAI5F,UAAU,EAAG;QAE9Dd,WAAW,IAAI,CAACa,YAAY,CAACb,UAAU0G,IAAI5F,UAAU;QAErD,wDAAwD;QACxD,MAAM0H,WAAWtD,KAAKC,SAAS,CAAC8C,MAAM3E,MAAM;QAC5C,IACEoD,IAAI5F,UAAU,IACd,6EAA6E;QAC7E,kCAAkC;QAClC,CAAC,IAAI,CAAC9C,qBAAqB,IAC3BwK,WAAW,IAAI,OAAO,MACtB;YACA,IAAI,IAAI,CAACzL,GAAG,EAAE;gBACZ,MAAM,qBAEL,CAFK,IAAI0L,MACR,CAAC,oEAAoE,EAAED,SAAS,OAAO,CAAC,GADpF,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YACA;QACF;QAEA,IAAI;gBAKI;YAJN,IAAI,CAAC9B,IAAI5F,UAAU,IAAI4F,IAAIlG,YAAY,EAAE;gBACvC,IAAI,CAACrB,aAAa,CAAC1C,GAAG,CAACgE,IAAAA,gBAAO,EAACT,WAAW0G,IAAIlG,YAAY;YAC5D;YAEA,QAAM,qBAAA,IAAI,CAACX,YAAY,qBAAjB,mBAAmBpD,GAAG,CAACuD,UAAUiI,MAAMvB;QAC/C,EAAE,OAAO3C,OAAO;YACdtF,QAAQiK,IAAI,CAAC,wCAAwC1I,UAAU+D;QACjE;IACF;AACF"}