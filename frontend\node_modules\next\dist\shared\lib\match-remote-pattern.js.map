{"version": 3, "sources": ["../../../src/shared/lib/match-remote-pattern.ts"], "sourcesContent": ["import type { RemotePattern } from './image-config'\nimport { makeRe } from 'next/dist/compiled/picomatch'\n\n// Modifying this function should also modify writeImagesManifest()\nexport function matchRemotePattern(pattern: RemotePattern, url: URL): boolean {\n  if (pattern.protocol !== undefined) {\n    const actualProto = url.protocol.slice(0, -1)\n    if (pattern.protocol !== actualProto) {\n      return false\n    }\n  }\n  if (pattern.port !== undefined) {\n    if (pattern.port !== url.port) {\n      return false\n    }\n  }\n\n  if (pattern.hostname === undefined) {\n    throw new Error(\n      `Pattern should define hostname but found\\n${JSON.stringify(pattern)}`\n    )\n  } else {\n    if (!makeRe(pattern.hostname).test(url.hostname)) {\n      return false\n    }\n  }\n\n  if (pattern.search !== undefined) {\n    if (pattern.search !== url.search) {\n      return false\n    }\n  }\n\n  // Should be the same as writeImagesManifest()\n  if (!makeRe(pattern.pathname ?? '**', { dot: true }).test(url.pathname)) {\n    return false\n  }\n\n  return true\n}\n\nexport function hasRemoteMatch(\n  domains: string[],\n  remotePatterns: RemotePattern[],\n  url: URL\n): boolean {\n  return (\n    domains.some((domain) => url.hostname === domain) ||\n    remotePatterns.some((p) => matchRemotePattern(p, url))\n  )\n}\n"], "names": ["hasRemoteMatch", "matchRemotePattern", "pattern", "url", "protocol", "undefined", "actualProto", "slice", "port", "hostname", "Error", "JSON", "stringify", "makeRe", "test", "search", "pathname", "dot", "domains", "remotePatterns", "some", "domain", "p"], "mappings": ";;;;;;;;;;;;;;;IAyCgBA,cAAc;eAAdA;;IArCAC,kBAAkB;eAAlBA;;;2BAHO;AAGhB,SAASA,mBAAmBC,OAAsB,EAAEC,GAAQ;IACjE,IAAID,QAAQE,QAAQ,KAAKC,WAAW;QAClC,MAAMC,cAAcH,IAAIC,QAAQ,CAACG,KAAK,CAAC,GAAG,CAAC;QAC3C,IAAIL,QAAQE,QAAQ,KAAKE,aAAa;YACpC,OAAO;QACT;IACF;IACA,IAAIJ,QAAQM,IAAI,KAAKH,WAAW;QAC9B,IAAIH,QAAQM,IAAI,KAAKL,IAAIK,IAAI,EAAE;YAC7B,OAAO;QACT;IACF;IAEA,IAAIN,QAAQO,QAAQ,KAAKJ,WAAW;QAClC,MAAM,qBAEL,CAFK,IAAIK,MACR,AAAC,+CAA4CC,KAAKC,SAAS,CAACV,WADxD,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF,OAAO;QACL,IAAI,CAACW,IAAAA,iBAAM,EAACX,QAAQO,QAAQ,EAAEK,IAAI,CAACX,IAAIM,QAAQ,GAAG;YAChD,OAAO;QACT;IACF;IAEA,IAAIP,QAAQa,MAAM,KAAKV,WAAW;QAChC,IAAIH,QAAQa,MAAM,KAAKZ,IAAIY,MAAM,EAAE;YACjC,OAAO;QACT;IACF;QAGYb;IADZ,8CAA8C;IAC9C,IAAI,CAACW,IAAAA,iBAAM,EAACX,CAAAA,oBAAAA,QAAQc,QAAQ,YAAhBd,oBAAoB,MAAM;QAAEe,KAAK;IAAK,GAAGH,IAAI,CAACX,IAAIa,QAAQ,GAAG;QACvE,OAAO;IACT;IAEA,OAAO;AACT;AAEO,SAAShB,eACdkB,OAAiB,EACjBC,cAA+B,EAC/BhB,GAAQ;IAER,OACEe,QAAQE,IAAI,CAAC,CAACC,SAAWlB,IAAIM,QAAQ,KAAKY,WAC1CF,eAAeC,IAAI,CAAC,CAACE,IAAMrB,mBAAmBqB,GAAGnB;AAErD"}