{"version": 3, "sources": ["../../../src/server/async-storage/request-store.ts"], "sourcesContent": ["import type { BaseNextRequest, BaseNextResponse } from '../base-http'\nimport type { IncomingHttpHeaders } from 'http'\nimport type { RequestStore } from '../app-render/work-unit-async-storage.external'\nimport type { RenderOpts } from '../app-render/types'\nimport type { NextRequest } from '../web/spec-extension/request'\nimport type { __ApiPreviewProps } from '../api-utils'\n\nimport { FLIGHT_HEADERS } from '../../client/components/app-router-headers'\nimport {\n  HeadersAdapter,\n  type ReadonlyHeaders,\n} from '../web/spec-extension/adapters/headers'\nimport {\n  MutableRequestCookiesAdapter,\n  RequestCookiesAdapter,\n  responseCookiesToRequestCookies,\n  wrapWithMutableAccessCheck,\n  type ReadonlyRequestCookies,\n} from '../web/spec-extension/adapters/request-cookies'\nimport { ResponseCookies, RequestCookies } from '../web/spec-extension/cookies'\nimport { DraftModeProvider } from './draft-mode-provider'\nimport { splitCookiesString } from '../web/utils'\nimport type { ServerComponentsHmrCache } from '../response-cache'\nimport type { RenderResumeDataCache } from '../resume-data-cache/resume-data-cache'\nimport type { Params } from '../request/params'\n\nfunction getHeaders(headers: Headers | IncomingHttpHeaders): ReadonlyHeaders {\n  const cleaned = HeadersAdapter.from(headers)\n  for (const header of FLIGHT_HEADERS) {\n    cleaned.delete(header.toLowerCase())\n  }\n\n  return HeadersAdapter.seal(cleaned)\n}\n\nfunction getMutableCookies(\n  headers: Headers | IncomingHttpHeaders,\n  onUpdateCookies?: (cookies: string[]) => void\n): ResponseCookies {\n  const cookies = new RequestCookies(HeadersAdapter.from(headers))\n  return MutableRequestCookiesAdapter.wrap(cookies, onUpdateCookies)\n}\n\nexport type WrapperRenderOpts = Partial<Pick<RenderOpts, 'onUpdateCookies'>> & {\n  previewProps?: __ApiPreviewProps\n}\n\ntype RequestContext = RequestResponsePair & {\n  /**\n   * The URL of the request. This only specifies the pathname and the search\n   * part of the URL. This is only undefined when generating static paths (ie,\n   * there is no request in progress, nor do we know one).\n   */\n  url: {\n    /**\n     * The pathname of the requested URL.\n     */\n    pathname: string\n\n    /**\n     * The search part of the requested URL. If the request did not provide a\n     * search part, this will be an empty string.\n     */\n    search?: string\n  }\n  phase: RequestStore['phase']\n  renderOpts?: WrapperRenderOpts\n  isHmrRefresh?: boolean\n  serverComponentsHmrCache?: ServerComponentsHmrCache\n  implicitTags?: string[] | undefined\n}\n\ntype RequestResponsePair =\n  | { req: BaseNextRequest; res: BaseNextResponse } // for an app page\n  | { req: NextRequest; res: undefined } // in an api route or middleware\n\n/**\n * If middleware set cookies in this request (indicated by `x-middleware-set-cookie`),\n * then merge those into the existing cookie object, so that when `cookies()` is accessed\n * it's able to read the newly set cookies.\n */\nfunction mergeMiddlewareCookies(\n  req: RequestContext['req'],\n  existingCookies: RequestCookies | ResponseCookies\n) {\n  if (\n    'x-middleware-set-cookie' in req.headers &&\n    typeof req.headers['x-middleware-set-cookie'] === 'string'\n  ) {\n    const setCookieValue = req.headers['x-middleware-set-cookie']\n    const responseHeaders = new Headers()\n\n    for (const cookie of splitCookiesString(setCookieValue)) {\n      responseHeaders.append('set-cookie', cookie)\n    }\n\n    const responseCookies = new ResponseCookies(responseHeaders)\n\n    // Transfer cookies from ResponseCookies to RequestCookies\n    for (const cookie of responseCookies.getAll()) {\n      existingCookies.set(cookie)\n    }\n  }\n}\n\nexport function createRequestStoreForRender(\n  req: RequestContext['req'],\n  res: RequestContext['res'],\n  url: RequestContext['url'],\n  rootParams: Params,\n  implicitTags: RequestContext['implicitTags'],\n  onUpdateCookies: RenderOpts['onUpdateCookies'],\n  previewProps: WrapperRenderOpts['previewProps'],\n  isHmrRefresh: RequestContext['isHmrRefresh'],\n  serverComponentsHmrCache: RequestContext['serverComponentsHmrCache'],\n  renderResumeDataCache: RenderResumeDataCache | undefined\n): RequestStore {\n  return createRequestStoreImpl(\n    // Pages start in render phase by default\n    'render',\n    req,\n    res,\n    url,\n    rootParams,\n    implicitTags,\n    onUpdateCookies,\n    renderResumeDataCache,\n    previewProps,\n    isHmrRefresh,\n    serverComponentsHmrCache\n  )\n}\n\nexport function createRequestStoreForAPI(\n  req: RequestContext['req'],\n  url: RequestContext['url'],\n  implicitTags: RequestContext['implicitTags'],\n  onUpdateCookies: RenderOpts['onUpdateCookies'],\n  previewProps: WrapperRenderOpts['previewProps']\n): RequestStore {\n  return createRequestStoreImpl(\n    // API routes start in action phase by default\n    'action',\n    req,\n    undefined,\n    url,\n    {},\n    implicitTags,\n    onUpdateCookies,\n    undefined,\n    previewProps,\n    false,\n    undefined\n  )\n}\n\nfunction createRequestStoreImpl(\n  phase: RequestStore['phase'],\n  req: RequestContext['req'],\n  res: RequestContext['res'],\n  url: RequestContext['url'],\n  rootParams: Params,\n  implicitTags: RequestContext['implicitTags'],\n  onUpdateCookies: RenderOpts['onUpdateCookies'],\n  renderResumeDataCache: RenderResumeDataCache | undefined,\n  previewProps: WrapperRenderOpts['previewProps'],\n  isHmrRefresh: RequestContext['isHmrRefresh'],\n  serverComponentsHmrCache: RequestContext['serverComponentsHmrCache']\n): RequestStore {\n  function defaultOnUpdateCookies(cookies: string[]) {\n    if (res) {\n      res.setHeader('Set-Cookie', cookies)\n    }\n  }\n\n  const cache: {\n    headers?: ReadonlyHeaders\n    cookies?: ReadonlyRequestCookies\n    mutableCookies?: ResponseCookies\n    userspaceMutableCookies?: ResponseCookies\n    draftMode?: DraftModeProvider\n  } = {}\n\n  return {\n    type: 'request',\n    phase,\n    implicitTags: implicitTags ?? [],\n    // Rather than just using the whole `url` here, we pull the parts we want\n    // to ensure we don't use parts of the URL that we shouldn't. This also\n    // lets us avoid requiring an empty string for `search` in the type.\n    url: { pathname: url.pathname, search: url.search ?? '' },\n    rootParams,\n    get headers() {\n      if (!cache.headers) {\n        // Seal the headers object that'll freeze out any methods that could\n        // mutate the underlying data.\n        cache.headers = getHeaders(req.headers)\n      }\n\n      return cache.headers\n    },\n    get cookies() {\n      if (!cache.cookies) {\n        // if middleware is setting cookie(s), then include those in\n        // the initial cached cookies so they can be read in render\n        const requestCookies = new RequestCookies(\n          HeadersAdapter.from(req.headers)\n        )\n\n        mergeMiddlewareCookies(req, requestCookies)\n\n        // Seal the cookies object that'll freeze out any methods that could\n        // mutate the underlying data.\n        cache.cookies = RequestCookiesAdapter.seal(requestCookies)\n      }\n\n      return cache.cookies\n    },\n    set cookies(value: ReadonlyRequestCookies) {\n      cache.cookies = value\n    },\n    get mutableCookies() {\n      if (!cache.mutableCookies) {\n        const mutableCookies = getMutableCookies(\n          req.headers,\n          onUpdateCookies || (res ? defaultOnUpdateCookies : undefined)\n        )\n\n        mergeMiddlewareCookies(req, mutableCookies)\n\n        cache.mutableCookies = mutableCookies\n      }\n      return cache.mutableCookies\n    },\n    get userspaceMutableCookies() {\n      if (!cache.userspaceMutableCookies) {\n        const userspaceMutableCookies = wrapWithMutableAccessCheck(\n          this.mutableCookies\n        )\n        cache.userspaceMutableCookies = userspaceMutableCookies\n      }\n      return cache.userspaceMutableCookies\n    },\n    get draftMode() {\n      if (!cache.draftMode) {\n        cache.draftMode = new DraftModeProvider(\n          previewProps,\n          req,\n          this.cookies,\n          this.mutableCookies\n        )\n      }\n\n      return cache.draftMode\n    },\n    renderResumeDataCache: renderResumeDataCache ?? null,\n    isHmrRefresh,\n    serverComponentsHmrCache:\n      serverComponentsHmrCache ||\n      (globalThis as any).__serverComponentsHmrCache,\n  }\n}\n\nexport function synchronizeMutableCookies(store: RequestStore) {\n  // TODO: does this need to update headers as well?\n  store.cookies = RequestCookiesAdapter.seal(\n    responseCookiesToRequestCookies(store.mutableCookies)\n  )\n}\n"], "names": ["FLIGHT_HEADERS", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "MutableRequestCookiesAdapter", "RequestCookiesAdapter", "responseCookiesToRequestCookies", "wrapWithMutableAccessCheck", "ResponseCookies", "RequestCookies", "DraftModeProvider", "splitCookiesString", "getHeaders", "headers", "cleaned", "from", "header", "delete", "toLowerCase", "seal", "getMutableCookies", "onUpdateCookies", "cookies", "wrap", "mergeMiddlewareCookies", "req", "existingCookies", "setCookieValue", "responseHeaders", "Headers", "cookie", "append", "responseCookies", "getAll", "set", "createRequestStoreForRender", "res", "url", "rootParams", "implicitTags", "previewProps", "isHmrRefresh", "serverComponentsHmrCache", "renderResumeDataCache", "createRequestStoreImpl", "createRequestStoreForAPI", "undefined", "phase", "defaultOnUpdateCookies", "<PERSON><PERSON><PERSON><PERSON>", "cache", "type", "pathname", "search", "requestCookies", "value", "mutableCookies", "userspaceMutableCookies", "draftMode", "globalThis", "__serverComponentsHmrCache", "synchronizeMutableCookies", "store"], "mappings": "AAOA,SAASA,cAAc,QAAQ,6CAA4C;AAC3E,SACEC,cAAc,QAET,yCAAwC;AAC/C,SACEC,4BAA4B,EAC5BC,qBAAqB,EACrBC,+BAA+B,EAC/BC,0BAA0B,QAErB,iDAAgD;AACvD,SAASC,eAAe,EAAEC,cAAc,QAAQ,gCAA+B;AAC/E,SAASC,iBAAiB,QAAQ,wBAAuB;AACzD,SAASC,kBAAkB,QAAQ,eAAc;AAKjD,SAASC,WAAWC,OAAsC;IACxD,MAAMC,UAAUX,eAAeY,IAAI,CAACF;IACpC,KAAK,MAAMG,UAAUd,eAAgB;QACnCY,QAAQG,MAAM,CAACD,OAAOE,WAAW;IACnC;IAEA,OAAOf,eAAegB,IAAI,CAACL;AAC7B;AAEA,SAASM,kBACPP,OAAsC,EACtCQ,eAA6C;IAE7C,MAAMC,UAAU,IAAIb,eAAeN,eAAeY,IAAI,CAACF;IACvD,OAAOT,6BAA6BmB,IAAI,CAACD,SAASD;AACpD;AAmCA;;;;CAIC,GACD,SAASG,uBACPC,GAA0B,EAC1BC,eAAiD;IAEjD,IACE,6BAA6BD,IAAIZ,OAAO,IACxC,OAAOY,IAAIZ,OAAO,CAAC,0BAA0B,KAAK,UAClD;QACA,MAAMc,iBAAiBF,IAAIZ,OAAO,CAAC,0BAA0B;QAC7D,MAAMe,kBAAkB,IAAIC;QAE5B,KAAK,MAAMC,UAAUnB,mBAAmBgB,gBAAiB;YACvDC,gBAAgBG,MAAM,CAAC,cAAcD;QACvC;QAEA,MAAME,kBAAkB,IAAIxB,gBAAgBoB;QAE5C,0DAA0D;QAC1D,KAAK,MAAME,UAAUE,gBAAgBC,MAAM,GAAI;YAC7CP,gBAAgBQ,GAAG,CAACJ;QACtB;IACF;AACF;AAEA,OAAO,SAASK,4BACdV,GAA0B,EAC1BW,GAA0B,EAC1BC,GAA0B,EAC1BC,UAAkB,EAClBC,YAA4C,EAC5ClB,eAA8C,EAC9CmB,YAA+C,EAC/CC,YAA4C,EAC5CC,wBAAoE,EACpEC,qBAAwD;IAExD,OAAOC,uBACL,yCAAyC;IACzC,UACAnB,KACAW,KACAC,KACAC,YACAC,cACAlB,iBACAsB,uBACAH,cACAC,cACAC;AAEJ;AAEA,OAAO,SAASG,yBACdpB,GAA0B,EAC1BY,GAA0B,EAC1BE,YAA4C,EAC5ClB,eAA8C,EAC9CmB,YAA+C;IAE/C,OAAOI,uBACL,8CAA8C;IAC9C,UACAnB,KACAqB,WACAT,KACA,CAAC,GACDE,cACAlB,iBACAyB,WACAN,cACA,OACAM;AAEJ;AAEA,SAASF,uBACPG,KAA4B,EAC5BtB,GAA0B,EAC1BW,GAA0B,EAC1BC,GAA0B,EAC1BC,UAAkB,EAClBC,YAA4C,EAC5ClB,eAA8C,EAC9CsB,qBAAwD,EACxDH,YAA+C,EAC/CC,YAA4C,EAC5CC,wBAAoE;IAEpE,SAASM,uBAAuB1B,OAAiB;QAC/C,IAAIc,KAAK;YACPA,IAAIa,SAAS,CAAC,cAAc3B;QAC9B;IACF;IAEA,MAAM4B,QAMF,CAAC;IAEL,OAAO;QACLC,MAAM;QACNJ;QACAR,cAAcA,gBAAgB,EAAE;QAChC,yEAAyE;QACzE,uEAAuE;QACvE,oEAAoE;QACpEF,KAAK;YAAEe,UAAUf,IAAIe,QAAQ;YAAEC,QAAQhB,IAAIgB,MAAM,IAAI;QAAG;QACxDf;QACA,IAAIzB,WAAU;YACZ,IAAI,CAACqC,MAAMrC,OAAO,EAAE;gBAClB,oEAAoE;gBACpE,8BAA8B;gBAC9BqC,MAAMrC,OAAO,GAAGD,WAAWa,IAAIZ,OAAO;YACxC;YAEA,OAAOqC,MAAMrC,OAAO;QACtB;QACA,IAAIS,WAAU;YACZ,IAAI,CAAC4B,MAAM5B,OAAO,EAAE;gBAClB,4DAA4D;gBAC5D,2DAA2D;gBAC3D,MAAMgC,iBAAiB,IAAI7C,eACzBN,eAAeY,IAAI,CAACU,IAAIZ,OAAO;gBAGjCW,uBAAuBC,KAAK6B;gBAE5B,oEAAoE;gBACpE,8BAA8B;gBAC9BJ,MAAM5B,OAAO,GAAGjB,sBAAsBc,IAAI,CAACmC;YAC7C;YAEA,OAAOJ,MAAM5B,OAAO;QACtB;QACA,IAAIA,SAAQiC,MAA+B;YACzCL,MAAM5B,OAAO,GAAGiC;QAClB;QACA,IAAIC,kBAAiB;YACnB,IAAI,CAACN,MAAMM,cAAc,EAAE;gBACzB,MAAMA,iBAAiBpC,kBACrBK,IAAIZ,OAAO,EACXQ,mBAAoBe,CAAAA,MAAMY,yBAAyBF,SAAQ;gBAG7DtB,uBAAuBC,KAAK+B;gBAE5BN,MAAMM,cAAc,GAAGA;YACzB;YACA,OAAON,MAAMM,cAAc;QAC7B;QACA,IAAIC,2BAA0B;YAC5B,IAAI,CAACP,MAAMO,uBAAuB,EAAE;gBAClC,MAAMA,0BAA0BlD,2BAC9B,IAAI,CAACiD,cAAc;gBAErBN,MAAMO,uBAAuB,GAAGA;YAClC;YACA,OAAOP,MAAMO,uBAAuB;QACtC;QACA,IAAIC,aAAY;YACd,IAAI,CAACR,MAAMQ,SAAS,EAAE;gBACpBR,MAAMQ,SAAS,GAAG,IAAIhD,kBACpB8B,cACAf,KACA,IAAI,CAACH,OAAO,EACZ,IAAI,CAACkC,cAAc;YAEvB;YAEA,OAAON,MAAMQ,SAAS;QACxB;QACAf,uBAAuBA,yBAAyB;QAChDF;QACAC,0BACEA,4BACA,AAACiB,WAAmBC,0BAA0B;IAClD;AACF;AAEA,OAAO,SAASC,0BAA0BC,KAAmB;IAC3D,kDAAkD;IAClDA,MAAMxC,OAAO,GAAGjB,sBAAsBc,IAAI,CACxCb,gCAAgCwD,MAAMN,cAAc;AAExD"}