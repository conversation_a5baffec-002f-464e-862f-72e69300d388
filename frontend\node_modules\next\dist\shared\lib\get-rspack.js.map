{"version": 3, "sources": ["../../../src/shared/lib/get-rspack.ts"], "sourcesContent": ["import { CanaryOnlyError, isStableBuild } from './canary-only'\n\nexport function getRspackCore() {\n  gateCanary()\n  try {\n    // eslint-disable-next-line import/no-extraneous-dependencies\n    return require('@rspack/core')\n  } catch (e) {\n    if (e instanceof Error && 'code' in e && e.code === 'MODULE_NOT_FOUND') {\n      throw new Error(\n        '@rspack/core is not available. Please make sure `@next/plugin-rspack` is correctly installed.'\n      )\n    }\n\n    throw e\n  }\n}\n\nexport function getRspackReactRefresh() {\n  gateCanary()\n  try {\n    // eslint-disable-next-line import/no-extraneous-dependencies\n    return require('@rspack/plugin-react-refresh')\n  } catch (e) {\n    if (e instanceof Error && 'code' in e && e.code === 'MODULE_NOT_FOUND') {\n      throw new Error(\n        '@rspack/plugin-react-refresh is not available. Please make sure `@next/plugin-rspack` is correctly installed.'\n      )\n    }\n\n    throw e\n  }\n}\n\nfunction gateCanary() {\n  if (isStableBuild()) {\n    throw new CanaryOnlyError(\n      'Rspack support is only available in Next.js canary.'\n    )\n  }\n}\n"], "names": ["getRspackCore", "getRspackReactRefresh", "gateCanary", "require", "e", "Error", "code", "isStableBuild", "CanaryOnlyError"], "mappings": ";;;;;;;;;;;;;;;IAEgBA,aAAa;eAAbA;;IAgBAC,qBAAqB;eAArBA;;;4BAlB+B;AAExC,SAASD;IACdE;IACA,IAAI;QACF,6DAA6D;QAC7D,OAAOC,QAAQ;IACjB,EAAE,OAAOC,GAAG;QACV,IAAIA,aAAaC,SAAS,UAAUD,KAAKA,EAAEE,IAAI,KAAK,oBAAoB;YACtE,MAAM,qBAEL,CAFK,IAAID,MACR,kGADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,MAAMD;IACR;AACF;AAEO,SAASH;IACdC;IACA,IAAI;QACF,6DAA6D;QAC7D,OAAOC,QAAQ;IACjB,EAAE,OAAOC,GAAG;QACV,IAAIA,aAAaC,SAAS,UAAUD,KAAKA,EAAEE,IAAI,KAAK,oBAAoB;YACtE,MAAM,qBAEL,CAFK,IAAID,MACR,kHADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,MAAMD;IACR;AACF;AAEA,SAASF;IACP,IAAIK,IAAAA,yBAAa,KAAI;QACnB,MAAM,qBAEL,CAFK,IAAIC,2BAAe,CACvB,wDADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;AACF"}