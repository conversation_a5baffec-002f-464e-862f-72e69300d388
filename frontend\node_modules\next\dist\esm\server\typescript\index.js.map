{"version": 3, "sources": ["../../../src/server/typescript/index.ts"], "sourcesContent": ["/**\n * This is a TypeScript language service plugin for Next.js app directory,\n * it provides the following features:\n *\n * - Warns about disallowed React APIs in server components.\n * - Warns about disallowed layout and page exports.\n * - Autocompletion for entry configurations.\n * - Hover hint and docs for entry configurations.\n */\n\nimport {\n  init,\n  getEntryInfo,\n  isAppEntryFile,\n  isDefaultFunctionExport,\n  isPositionInsideNode,\n  getSource,\n  isInsideApp,\n} from './utils'\nimport { NEXT_TS_ERRORS } from './constant'\n\nimport entryConfig from './rules/config'\nimport serverLayer from './rules/server'\nimport entryDefault from './rules/entry'\nimport clientBoundary from './rules/client-boundary'\nimport serverBoundary from './rules/server-boundary'\nimport metadata from './rules/metadata'\nimport errorEntry from './rules/error'\nimport type tsModule from 'typescript/lib/tsserverlibrary'\n\nexport const createTSPlugin: tsModule.server.PluginModuleFactory = ({\n  typescript: ts,\n}) => {\n  function create(info: tsModule.server.PluginCreateInfo) {\n    init({\n      ts,\n      info,\n    })\n\n    // Set up decorator object\n    const proxy = Object.create(null)\n    for (let k of Object.keys(info.languageService)) {\n      const x = (info.languageService as any)[k]\n      proxy[k] = (...args: Array<{}>) => x.apply(info.languageService, args)\n    }\n\n    // Get plugin options\n    // config is the plugin options from the user's tsconfig.json\n    // e.g. { \"plugins\": [{ \"name\": \"next\", \"enabled\": true }] }\n    // config will be { \"name\": \"next\", \"enabled\": true }\n    // The default user config is { \"name\": \"next\" }\n    const isPluginEnabled = info.config.enabled ?? true\n\n    if (!isPluginEnabled) {\n      return proxy\n    }\n\n    // Auto completion\n    proxy.getCompletionsAtPosition = (\n      fileName: string,\n      position: number,\n      options: any\n    ) => {\n      let prior = info.languageService.getCompletionsAtPosition(\n        fileName,\n        position,\n        options\n      ) || {\n        isGlobalCompletion: false,\n        isMemberCompletion: false,\n        isNewIdentifierLocation: false,\n        entries: [],\n      }\n      if (!isAppEntryFile(fileName)) return prior\n\n      // If it's a server entry.\n      const entryInfo = getEntryInfo(fileName)\n      if (!entryInfo.client) {\n        // Remove specified entries from completion list\n        prior.entries = serverLayer.filterCompletionsAtPosition(prior.entries)\n\n        // Provide autocompletion for metadata fields\n        prior = metadata.filterCompletionsAtPosition(\n          fileName,\n          position,\n          options,\n          prior\n        )\n      }\n\n      // Add auto completions for export configs.\n      entryConfig.addCompletionsAtPosition(fileName, position, prior)\n\n      const source = getSource(fileName)\n      if (!source) return prior\n\n      ts.forEachChild(source!, (node) => {\n        // Auto completion for default export function's props.\n        if (\n          isPositionInsideNode(position, node) &&\n          isDefaultFunctionExport(node)\n        ) {\n          prior.entries.push(\n            ...entryDefault.getCompletionsAtPosition(\n              fileName,\n              node as tsModule.FunctionDeclaration,\n              position\n            )\n          )\n        }\n      })\n\n      return prior\n    }\n\n    // Show auto completion details\n    proxy.getCompletionEntryDetails = (\n      fileName: string,\n      position: number,\n      entryName: string,\n      formatOptions: tsModule.FormatCodeOptions,\n      source: string,\n      preferences: tsModule.UserPreferences,\n      data: tsModule.CompletionEntryData\n    ) => {\n      const entryCompletionEntryDetails = entryConfig.getCompletionEntryDetails(\n        entryName,\n        data\n      )\n      if (entryCompletionEntryDetails) return entryCompletionEntryDetails\n\n      const metadataCompletionEntryDetails = metadata.getCompletionEntryDetails(\n        fileName,\n        position,\n        entryName,\n        formatOptions,\n        source,\n        preferences,\n        data\n      )\n      if (metadataCompletionEntryDetails) return metadataCompletionEntryDetails\n\n      return info.languageService.getCompletionEntryDetails(\n        fileName,\n        position,\n        entryName,\n        formatOptions,\n        source,\n        preferences,\n        data\n      )\n    }\n\n    // Quick info\n    proxy.getQuickInfoAtPosition = (fileName: string, position: number) => {\n      const prior = info.languageService.getQuickInfoAtPosition(\n        fileName,\n        position\n      )\n      if (!isAppEntryFile(fileName)) return prior\n\n      // Remove type suggestions for disallowed APIs in server components.\n      const entryInfo = getEntryInfo(fileName)\n      if (!entryInfo.client) {\n        const definitions = info.languageService.getDefinitionAtPosition(\n          fileName,\n          position\n        )\n        if (\n          definitions &&\n          serverLayer.hasDisallowedReactAPIDefinition(definitions)\n        ) {\n          return\n        }\n\n        const metadataInfo = metadata.getQuickInfoAtPosition(fileName, position)\n        if (metadataInfo) return metadataInfo\n      }\n\n      const overridden = entryConfig.getQuickInfoAtPosition(fileName, position)\n      if (overridden) return overridden\n\n      return prior\n    }\n\n    // Show errors for disallowed imports\n    proxy.getSemanticDiagnostics = (fileName: string) => {\n      const prior = info.languageService.getSemanticDiagnostics(fileName)\n      const source = getSource(fileName)\n      if (!source) return prior\n\n      let isClientEntry = false\n      let isServerEntry = false\n      const isAppEntry = isAppEntryFile(fileName)\n\n      try {\n        const entryInfo = getEntryInfo(fileName, true)\n        isClientEntry = entryInfo.client\n        isServerEntry = entryInfo.server\n      } catch (e: any) {\n        prior.push({\n          file: source,\n          category: ts.DiagnosticCategory.Error,\n          code: NEXT_TS_ERRORS.MISPLACED_ENTRY_DIRECTIVE,\n          ...e,\n        })\n        isClientEntry = false\n        isServerEntry = false\n      }\n\n      if (isInsideApp(fileName)) {\n        const errorDiagnostic = errorEntry.getSemanticDiagnostics(\n          source!,\n          isClientEntry\n        )\n        prior.push(...errorDiagnostic)\n      }\n\n      ts.forEachChild(source!, (node) => {\n        if (ts.isImportDeclaration(node)) {\n          // import ...\n          if (isAppEntry) {\n            if (!isClientEntry || isServerEntry) {\n              // Check if it has valid imports in the server layer\n              const diagnostics =\n                serverLayer.getSemanticDiagnosticsForImportDeclaration(\n                  source,\n                  node\n                )\n              prior.push(...diagnostics)\n            }\n          }\n        } else if (\n          ts.isVariableStatement(node) &&\n          node.modifiers?.some((m) => m.kind === ts.SyntaxKind.ExportKeyword)\n        ) {\n          // export const ...\n          if (isAppEntry) {\n            // Check if it has correct option exports\n            const diagnostics =\n              entryConfig.getSemanticDiagnosticsForExportVariableStatement(\n                source,\n                node\n              )\n            const metadataDiagnostics = isClientEntry\n              ? metadata.getSemanticDiagnosticsForExportVariableStatementInClientEntry(\n                  fileName,\n                  node\n                )\n              : metadata.getSemanticDiagnosticsForExportVariableStatement(\n                  fileName,\n                  node\n                )\n            prior.push(...diagnostics, ...metadataDiagnostics)\n          }\n\n          if (isClientEntry) {\n            prior.push(\n              ...clientBoundary.getSemanticDiagnosticsForExportVariableStatement(\n                source,\n                node\n              )\n            )\n          }\n\n          if (isServerEntry) {\n            prior.push(\n              ...serverBoundary.getSemanticDiagnosticsForExportVariableStatement(\n                source,\n                node\n              )\n            )\n          }\n        } else if (isDefaultFunctionExport(node)) {\n          // export default function ...\n          if (isAppEntry) {\n            const diagnostics = entryDefault.getSemanticDiagnostics(\n              fileName,\n              source,\n              node\n            )\n            prior.push(...diagnostics)\n          }\n\n          if (isClientEntry) {\n            prior.push(\n              ...clientBoundary.getSemanticDiagnosticsForFunctionExport(\n                source,\n                node\n              )\n            )\n          }\n\n          if (isServerEntry) {\n            prior.push(\n              ...serverBoundary.getSemanticDiagnosticsForFunctionExport(\n                source,\n                node\n              )\n            )\n          }\n        } else if (\n          ts.isFunctionDeclaration(node) &&\n          node.modifiers?.some((m) => m.kind === ts.SyntaxKind.ExportKeyword)\n        ) {\n          // export function ...\n          if (isAppEntry) {\n            const metadataDiagnostics = isClientEntry\n              ? metadata.getSemanticDiagnosticsForExportVariableStatementInClientEntry(\n                  fileName,\n                  node\n                )\n              : metadata.getSemanticDiagnosticsForExportVariableStatement(\n                  fileName,\n                  node\n                )\n            prior.push(...metadataDiagnostics)\n          }\n\n          if (isClientEntry) {\n            prior.push(\n              ...clientBoundary.getSemanticDiagnosticsForFunctionExport(\n                source,\n                node\n              )\n            )\n          }\n\n          if (isServerEntry) {\n            prior.push(\n              ...serverBoundary.getSemanticDiagnosticsForFunctionExport(\n                source,\n                node\n              )\n            )\n          }\n        } else if (ts.isExportDeclaration(node)) {\n          // export { ... }\n          if (isAppEntry) {\n            const metadataDiagnostics = isClientEntry\n              ? metadata.getSemanticDiagnosticsForExportDeclarationInClientEntry(\n                  fileName,\n                  node\n                )\n              : metadata.getSemanticDiagnosticsForExportDeclaration(\n                  fileName,\n                  node\n                )\n            prior.push(...metadataDiagnostics)\n          }\n\n          if (isServerEntry) {\n            prior.push(\n              ...serverBoundary.getSemanticDiagnosticsForExportDeclaration(\n                source,\n                node\n              )\n            )\n          }\n        }\n      })\n\n      return prior\n    }\n\n    // Get definition and link for specific node\n    proxy.getDefinitionAndBoundSpan = (fileName: string, position: number) => {\n      const entryInfo = getEntryInfo(fileName)\n      if (isAppEntryFile(fileName) && !entryInfo.client) {\n        const metadataDefinition = metadata.getDefinitionAndBoundSpan(\n          fileName,\n          position\n        )\n        if (metadataDefinition) return metadataDefinition\n      }\n\n      return info.languageService.getDefinitionAndBoundSpan(fileName, position)\n    }\n\n    return proxy\n  }\n\n  return { create }\n}\n"], "names": ["init", "getEntryInfo", "isAppEntryFile", "isDefaultFunctionExport", "isPositionInsideNode", "getSource", "isInsideApp", "NEXT_TS_ERRORS", "entryConfig", "serverLayer", "<PERSON><PERSON><PERSON><PERSON>", "clientBoundary", "serverBoundary", "metadata", "errorEntry", "createTSPlugin", "typescript", "ts", "create", "info", "proxy", "Object", "k", "keys", "languageService", "x", "args", "apply", "isPluginEnabled", "config", "enabled", "getCompletionsAtPosition", "fileName", "position", "options", "prior", "isGlobalCompletion", "isMemberCompletion", "isNewIdentifierLocation", "entries", "entryInfo", "client", "filterCompletionsAtPosition", "addCompletionsAtPosition", "source", "for<PERSON><PERSON><PERSON><PERSON><PERSON>", "node", "push", "getCompletionEntryDetails", "entryName", "formatOptions", "preferences", "data", "entryCompletionEntryDetails", "metadataCompletionEntryDetails", "getQuickInfoAtPosition", "definitions", "getDefinitionAtPosition", "hasDisallowedReactAPIDefinition", "metadataInfo", "overridden", "getSemanticDiagnostics", "isClientEntry", "isServerEntry", "isAppEntry", "server", "e", "file", "category", "DiagnosticCategory", "Error", "code", "MISPLACED_ENTRY_DIRECTIVE", "errorDiagnostic", "isImportDeclaration", "diagnostics", "getSemanticDiagnosticsForImportDeclaration", "isVariableStatement", "modifiers", "some", "m", "kind", "SyntaxKind", "ExportKeyword", "getSemanticDiagnosticsForExportVariableStatement", "metadataDiagnostics", "getSemanticDiagnosticsForExportVariableStatementInClientEntry", "getSemanticDiagnosticsForFunctionExport", "isFunctionDeclaration", "isExportDeclaration", "getSemanticDiagnosticsForExportDeclarationInClientEntry", "getSemanticDiagnosticsForExportDeclaration", "getDefinitionAndBoundSpan", "metadataDefinition"], "mappings": "AAAA;;;;;;;;CAQC,GAED,SACEA,IAAI,EACJC,YAAY,EACZC,cAAc,EACdC,uBAAuB,EACvBC,oBAAoB,EACpBC,SAAS,EACTC,WAAW,QACN,UAAS;AAChB,SAASC,cAAc,QAAQ,aAAY;AAE3C,OAAOC,iBAAiB,iBAAgB;AACxC,OAAOC,iBAAiB,iBAAgB;AACxC,OAAOC,kBAAkB,gBAAe;AACxC,OAAOC,oBAAoB,0BAAyB;AACpD,OAAOC,oBAAoB,0BAAyB;AACpD,OAAOC,cAAc,mBAAkB;AACvC,OAAOC,gBAAgB,gBAAe;AAGtC,OAAO,MAAMC,iBAAsD,CAAC,EAClEC,YAAYC,EAAE,EACf;IACC,SAASC,OAAOC,IAAsC;QACpDnB,KAAK;YACHiB;YACAE;QACF;QAEA,0BAA0B;QAC1B,MAAMC,QAAQC,OAAOH,MAAM,CAAC;QAC5B,KAAK,IAAII,KAAKD,OAAOE,IAAI,CAACJ,KAAKK,eAAe,EAAG;YAC/C,MAAMC,IAAI,AAACN,KAAKK,eAAe,AAAQ,CAACF,EAAE;YAC1CF,KAAK,CAACE,EAAE,GAAG,CAAC,GAAGI,OAAoBD,EAAEE,KAAK,CAACR,KAAKK,eAAe,EAAEE;QACnE;QAEA,qBAAqB;QACrB,6DAA6D;QAC7D,4DAA4D;QAC5D,qDAAqD;QACrD,gDAAgD;QAChD,MAAME,kBAAkBT,KAAKU,MAAM,CAACC,OAAO,IAAI;QAE/C,IAAI,CAACF,iBAAiB;YACpB,OAAOR;QACT;QAEA,kBAAkB;QAClBA,MAAMW,wBAAwB,GAAG,CAC/BC,UACAC,UACAC;YAEA,IAAIC,QAAQhB,KAAKK,eAAe,CAACO,wBAAwB,CACvDC,UACAC,UACAC,YACG;gBACHE,oBAAoB;gBACpBC,oBAAoB;gBACpBC,yBAAyB;gBACzBC,SAAS,EAAE;YACb;YACA,IAAI,CAACrC,eAAe8B,WAAW,OAAOG;YAEtC,0BAA0B;YAC1B,MAAMK,YAAYvC,aAAa+B;YAC/B,IAAI,CAACQ,UAAUC,MAAM,EAAE;gBACrB,gDAAgD;gBAChDN,MAAMI,OAAO,GAAG9B,YAAYiC,2BAA2B,CAACP,MAAMI,OAAO;gBAErE,6CAA6C;gBAC7CJ,QAAQtB,SAAS6B,2BAA2B,CAC1CV,UACAC,UACAC,SACAC;YAEJ;YAEA,2CAA2C;YAC3C3B,YAAYmC,wBAAwB,CAACX,UAAUC,UAAUE;YAEzD,MAAMS,SAASvC,UAAU2B;YACzB,IAAI,CAACY,QAAQ,OAAOT;YAEpBlB,GAAG4B,YAAY,CAACD,QAAS,CAACE;gBACxB,uDAAuD;gBACvD,IACE1C,qBAAqB6B,UAAUa,SAC/B3C,wBAAwB2C,OACxB;oBACAX,MAAMI,OAAO,CAACQ,IAAI,IACbrC,aAAaqB,wBAAwB,CACtCC,UACAc,MACAb;gBAGN;YACF;YAEA,OAAOE;QACT;QAEA,+BAA+B;QAC/Bf,MAAM4B,yBAAyB,GAAG,CAChChB,UACAC,UACAgB,WACAC,eACAN,QACAO,aACAC;YAEA,MAAMC,8BAA8B7C,YAAYwC,yBAAyB,CACvEC,WACAG;YAEF,IAAIC,6BAA6B,OAAOA;YAExC,MAAMC,iCAAiCzC,SAASmC,yBAAyB,CACvEhB,UACAC,UACAgB,WACAC,eACAN,QACAO,aACAC;YAEF,IAAIE,gCAAgC,OAAOA;YAE3C,OAAOnC,KAAKK,eAAe,CAACwB,yBAAyB,CACnDhB,UACAC,UACAgB,WACAC,eACAN,QACAO,aACAC;QAEJ;QAEA,aAAa;QACbhC,MAAMmC,sBAAsB,GAAG,CAACvB,UAAkBC;YAChD,MAAME,QAAQhB,KAAKK,eAAe,CAAC+B,sBAAsB,CACvDvB,UACAC;YAEF,IAAI,CAAC/B,eAAe8B,WAAW,OAAOG;YAEtC,oEAAoE;YACpE,MAAMK,YAAYvC,aAAa+B;YAC/B,IAAI,CAACQ,UAAUC,MAAM,EAAE;gBACrB,MAAMe,cAAcrC,KAAKK,eAAe,CAACiC,uBAAuB,CAC9DzB,UACAC;gBAEF,IACEuB,eACA/C,YAAYiD,+BAA+B,CAACF,cAC5C;oBACA;gBACF;gBAEA,MAAMG,eAAe9C,SAAS0C,sBAAsB,CAACvB,UAAUC;gBAC/D,IAAI0B,cAAc,OAAOA;YAC3B;YAEA,MAAMC,aAAapD,YAAY+C,sBAAsB,CAACvB,UAAUC;YAChE,IAAI2B,YAAY,OAAOA;YAEvB,OAAOzB;QACT;QAEA,qCAAqC;QACrCf,MAAMyC,sBAAsB,GAAG,CAAC7B;YAC9B,MAAMG,QAAQhB,KAAKK,eAAe,CAACqC,sBAAsB,CAAC7B;YAC1D,MAAMY,SAASvC,UAAU2B;YACzB,IAAI,CAACY,QAAQ,OAAOT;YAEpB,IAAI2B,gBAAgB;YACpB,IAAIC,gBAAgB;YACpB,MAAMC,aAAa9D,eAAe8B;YAElC,IAAI;gBACF,MAAMQ,YAAYvC,aAAa+B,UAAU;gBACzC8B,gBAAgBtB,UAAUC,MAAM;gBAChCsB,gBAAgBvB,UAAUyB,MAAM;YAClC,EAAE,OAAOC,GAAQ;gBACf/B,MAAMY,IAAI,CAAC;oBACToB,MAAMvB;oBACNwB,UAAUnD,GAAGoD,kBAAkB,CAACC,KAAK;oBACrCC,MAAMhE,eAAeiE,yBAAyB;oBAC9C,GAAGN,CAAC;gBACN;gBACAJ,gBAAgB;gBAChBC,gBAAgB;YAClB;YAEA,IAAIzD,YAAY0B,WAAW;gBACzB,MAAMyC,kBAAkB3D,WAAW+C,sBAAsB,CACvDjB,QACAkB;gBAEF3B,MAAMY,IAAI,IAAI0B;YAChB;YAEAxD,GAAG4B,YAAY,CAACD,QAAS,CAACE;oBAgBtBA,iBAqEAA;gBApFF,IAAI7B,GAAGyD,mBAAmB,CAAC5B,OAAO;oBAChC,aAAa;oBACb,IAAIkB,YAAY;wBACd,IAAI,CAACF,iBAAiBC,eAAe;4BACnC,oDAAoD;4BACpD,MAAMY,cACJlE,YAAYmE,0CAA0C,CACpDhC,QACAE;4BAEJX,MAAMY,IAAI,IAAI4B;wBAChB;oBACF;gBACF,OAAO,IACL1D,GAAG4D,mBAAmB,CAAC/B,WACvBA,kBAAAA,KAAKgC,SAAS,qBAAdhC,gBAAgBiC,IAAI,CAAC,CAACC,IAAMA,EAAEC,IAAI,KAAKhE,GAAGiE,UAAU,CAACC,aAAa,IAClE;oBACA,mBAAmB;oBACnB,IAAInB,YAAY;wBACd,yCAAyC;wBACzC,MAAMW,cACJnE,YAAY4E,gDAAgD,CAC1DxC,QACAE;wBAEJ,MAAMuC,sBAAsBvB,gBACxBjD,SAASyE,6DAA6D,CACpEtD,UACAc,QAEFjC,SAASuE,gDAAgD,CACvDpD,UACAc;wBAENX,MAAMY,IAAI,IAAI4B,gBAAgBU;oBAChC;oBAEA,IAAIvB,eAAe;wBACjB3B,MAAMY,IAAI,IACLpC,eAAeyE,gDAAgD,CAChExC,QACAE;oBAGN;oBAEA,IAAIiB,eAAe;wBACjB5B,MAAMY,IAAI,IACLnC,eAAewE,gDAAgD,CAChExC,QACAE;oBAGN;gBACF,OAAO,IAAI3C,wBAAwB2C,OAAO;oBACxC,8BAA8B;oBAC9B,IAAIkB,YAAY;wBACd,MAAMW,cAAcjE,aAAamD,sBAAsB,CACrD7B,UACAY,QACAE;wBAEFX,MAAMY,IAAI,IAAI4B;oBAChB;oBAEA,IAAIb,eAAe;wBACjB3B,MAAMY,IAAI,IACLpC,eAAe4E,uCAAuC,CACvD3C,QACAE;oBAGN;oBAEA,IAAIiB,eAAe;wBACjB5B,MAAMY,IAAI,IACLnC,eAAe2E,uCAAuC,CACvD3C,QACAE;oBAGN;gBACF,OAAO,IACL7B,GAAGuE,qBAAqB,CAAC1C,WACzBA,mBAAAA,KAAKgC,SAAS,qBAAdhC,iBAAgBiC,IAAI,CAAC,CAACC,IAAMA,EAAEC,IAAI,KAAKhE,GAAGiE,UAAU,CAACC,aAAa,IAClE;oBACA,sBAAsB;oBACtB,IAAInB,YAAY;wBACd,MAAMqB,sBAAsBvB,gBACxBjD,SAASyE,6DAA6D,CACpEtD,UACAc,QAEFjC,SAASuE,gDAAgD,CACvDpD,UACAc;wBAENX,MAAMY,IAAI,IAAIsC;oBAChB;oBAEA,IAAIvB,eAAe;wBACjB3B,MAAMY,IAAI,IACLpC,eAAe4E,uCAAuC,CACvD3C,QACAE;oBAGN;oBAEA,IAAIiB,eAAe;wBACjB5B,MAAMY,IAAI,IACLnC,eAAe2E,uCAAuC,CACvD3C,QACAE;oBAGN;gBACF,OAAO,IAAI7B,GAAGwE,mBAAmB,CAAC3C,OAAO;oBACvC,iBAAiB;oBACjB,IAAIkB,YAAY;wBACd,MAAMqB,sBAAsBvB,gBACxBjD,SAAS6E,uDAAuD,CAC9D1D,UACAc,QAEFjC,SAAS8E,0CAA0C,CACjD3D,UACAc;wBAENX,MAAMY,IAAI,IAAIsC;oBAChB;oBAEA,IAAItB,eAAe;wBACjB5B,MAAMY,IAAI,IACLnC,eAAe+E,0CAA0C,CAC1D/C,QACAE;oBAGN;gBACF;YACF;YAEA,OAAOX;QACT;QAEA,4CAA4C;QAC5Cf,MAAMwE,yBAAyB,GAAG,CAAC5D,UAAkBC;YACnD,MAAMO,YAAYvC,aAAa+B;YAC/B,IAAI9B,eAAe8B,aAAa,CAACQ,UAAUC,MAAM,EAAE;gBACjD,MAAMoD,qBAAqBhF,SAAS+E,yBAAyB,CAC3D5D,UACAC;gBAEF,IAAI4D,oBAAoB,OAAOA;YACjC;YAEA,OAAO1E,KAAKK,eAAe,CAACoE,yBAAyB,CAAC5D,UAAUC;QAClE;QAEA,OAAOb;IACT;IAEA,OAAO;QAAEF;IAAO;AAClB,EAAC"}