{"version": 3, "sources": ["../../../../../../src/client/components/react-dev-overlay/utils/dev-indicator/use-sync-dev-render-indicator-internal.tsx"], "sourcesContent": ["import { useEffect, useTransition } from 'react'\nimport { devRenderIndicator } from './dev-render-indicator'\n\nexport const useSyncDevRenderIndicatorInternal = () => {\n  const [isPending, startTransition] = useTransition()\n\n  useEffect(() => {\n    if (isPending) {\n      devRenderIndicator.show()\n    } else {\n      devRenderIndicator.hide()\n    }\n  }, [isPending])\n\n  return startTransition\n}\n"], "names": ["useSyncDevRenderIndicatorInternal", "isPending", "startTransition", "useTransition", "useEffect", "devRenderIndicator", "show", "hide"], "mappings": ";;;;+BAGaA;;;eAAAA;;;uBAH4B;oCACN;AAE5B,MAAMA,oCAAoC;IAC/C,MAAM,CAACC,WAAWC,gBAAgB,GAAGC,IAAAA,oBAAa;IAElDC,IAAAA,gBAAS,EAAC;QACR,IAAIH,WAAW;YACbI,sCAAkB,CAACC,IAAI;QACzB,OAAO;YACLD,sCAAkB,CAACE,IAAI;QACzB;IACF,GAAG;QAACN;KAAU;IAEd,OAAOC;AACT"}