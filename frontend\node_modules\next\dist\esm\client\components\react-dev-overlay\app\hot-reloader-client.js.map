{"version": 3, "sources": ["../../../../../src/client/components/react-dev-overlay/app/hot-reloader-client.tsx"], "sourcesContent": ["import type { ReactNode } from 'react'\nimport {\n  use<PERSON>allback,\n  useEffect,\n  startTransition,\n  useMemo,\n  useRef,\n  useSyncExternalStore,\n} from 'react'\nimport stripAnsi from 'next/dist/compiled/strip-ansi'\nimport formatWebpackMessages from '../utils/format-webpack-messages'\nimport { useRouter } from '../../navigation'\nimport {\n  ACTION_BEFORE_REFRESH,\n  ACTION_BUILD_ERROR,\n  ACTION_BUILD_OK,\n  ACTION_DEBUG_INFO,\n  ACTION_DEV_INDICATOR,\n  ACTION_REFRESH,\n  ACTION_STATIC_INDICATOR,\n  ACTION_UNHANDLED_ERROR,\n  ACTION_UNHANDLED_REJECTION,\n  ACTION_VERSION_INFO,\n  useErrorOverlayReducer,\n} from '../shared'\nimport { parseStack } from '../utils/parse-stack'\nimport { AppDevOverlay } from './app-dev-overlay'\nimport { useErrorHandler } from '../../errors/use-error-handler'\nimport { RuntimeErrorHandler } from '../../errors/runtime-error-handler'\nimport {\n  useSendMessage,\n  useTurbopack,\n  useWebsocket,\n  useWebsocketPing,\n} from '../utils/use-websocket'\nimport { parseComponentStack } from '../utils/parse-component-stack'\nimport type { VersionInfo } from '../../../../server/dev/parse-version-info'\nimport { HMR_ACTIONS_SENT_TO_BROWSER } from '../../../../server/dev/hot-reloader-types'\nimport type {\n  HMR_ACTION_TYPES,\n  TurbopackMsgToBrowser,\n} from '../../../../server/dev/hot-reloader-types'\nimport { extractModulesFromTurbopackMessage } from '../../../../server/dev/extract-modules-from-turbopack-message'\nimport { REACT_REFRESH_FULL_RELOAD_FROM_ERROR } from '../shared'\nimport type { HydrationErrorState } from '../../errors/hydration-error-info'\nimport type { DebugInfo } from '../types'\nimport { useUntrackedPathname } from '../../navigation-untracked'\nimport { getReactStitchedError } from '../../errors/stitched-error'\nimport { shouldRenderRootLevelErrorOverlay } from '../../../lib/is-error-thrown-while-rendering-rsc'\nimport { handleDevBuildIndicatorHmrEvents } from '../../../dev/dev-build-indicator/internal/handle-dev-build-indicator-hmr-events'\nimport type { GlobalErrorComponent } from '../../error-boundary'\nimport type { DevIndicatorServerState } from '../../../../server/dev/dev-indicator-server-state'\n\nexport interface Dispatcher {\n  onBuildOk(): void\n  onBuildError(message: string): void\n  onVersionInfo(versionInfo: VersionInfo): void\n  onDebugInfo(debugInfo: DebugInfo): void\n  onBeforeRefresh(): void\n  onRefresh(): void\n  onStaticIndicator(status: boolean): void\n  onDevIndicator(devIndicator: DevIndicatorServerState): void\n}\n\nlet mostRecentCompilationHash: any = null\nlet __nextDevClientId = Math.round(Math.random() * 100 + Date.now())\nlet reloading = false\nlet startLatency: number | null = null\nlet turbopackLastUpdateLatency: number | null = null\nlet turbopackUpdatedModules: Set<string> = new Set()\n\nlet pendingHotUpdateWebpack = Promise.resolve()\nlet resolvePendingHotUpdateWebpack: () => void = () => {}\nfunction setPendingHotUpdateWebpack() {\n  pendingHotUpdateWebpack = new Promise((resolve) => {\n    resolvePendingHotUpdateWebpack = () => {\n      resolve()\n    }\n  })\n}\n\nexport function waitForWebpackRuntimeHotUpdate() {\n  return pendingHotUpdateWebpack\n}\n\nfunction handleBeforeHotUpdateWebpack(\n  dispatcher: Dispatcher,\n  hasUpdates: boolean\n) {\n  if (hasUpdates) {\n    dispatcher.onBeforeRefresh()\n  }\n}\n\nfunction handleSuccessfulHotUpdateWebpack(\n  dispatcher: Dispatcher,\n  sendMessage: (message: string) => void,\n  updatedModules: ReadonlyArray<string>\n) {\n  resolvePendingHotUpdateWebpack()\n  dispatcher.onBuildOk()\n  reportHmrLatency(sendMessage, updatedModules)\n\n  dispatcher.onRefresh()\n}\n\nfunction reportHmrLatency(\n  sendMessage: (message: string) => void,\n  updatedModules: ReadonlyArray<string>\n) {\n  if (!startLatency) return\n  // turbopack has a debounce for the \"built\" event which we don't want to\n  // incorrectly show in this number, use the last TURBOPACK_MESSAGE time\n  let endLatency = turbopackLastUpdateLatency ?? Date.now()\n  const latency = endLatency - startLatency\n  console.log(`[Fast Refresh] done in ${latency}ms`)\n  sendMessage(\n    JSON.stringify({\n      event: 'client-hmr-latency',\n      id: window.__nextDevClientId,\n      startTime: startLatency,\n      endTime: endLatency,\n      page: window.location.pathname,\n      updatedModules,\n      // Whether the page (tab) was hidden at the time the event occurred.\n      // This can impact the accuracy of the event's timing.\n      isPageHidden: document.visibilityState === 'hidden',\n    })\n  )\n}\n\n// There is a newer version of the code available.\nfunction handleAvailableHash(hash: string) {\n  // Update last known compilation hash.\n  mostRecentCompilationHash = hash\n}\n\n/**\n * Is there a newer version of this code available?\n * For webpack: Check if the hash changed compared to __webpack_hash__\n * For Turbopack: Always true because it doesn't have __webpack_hash__\n */\nfunction isUpdateAvailable() {\n  if (process.env.TURBOPACK) {\n    return true\n  }\n\n  /* globals __webpack_hash__ */\n  // __webpack_hash__ is the hash of the current compilation.\n  // It's a global variable injected by Webpack.\n  return mostRecentCompilationHash !== __webpack_hash__\n}\n\n// Webpack disallows updates in other states.\nfunction canApplyUpdates() {\n  // @ts-expect-error module.hot exists\n  return module.hot.status() === 'idle'\n}\nfunction afterApplyUpdates(fn: any) {\n  if (canApplyUpdates()) {\n    fn()\n  } else {\n    function handler(status: any) {\n      if (status === 'idle') {\n        // @ts-expect-error module.hot exists\n        module.hot.removeStatusHandler(handler)\n        fn()\n      }\n    }\n    // @ts-expect-error module.hot exists\n    module.hot.addStatusHandler(handler)\n  }\n}\n\nfunction performFullReload(err: any, sendMessage: any) {\n  const stackTrace =\n    err &&\n    ((err.stack && err.stack.split('\\n').slice(0, 5).join('\\n')) ||\n      err.message ||\n      err + '')\n\n  sendMessage(\n    JSON.stringify({\n      event: 'client-full-reload',\n      stackTrace,\n      hadRuntimeError: !!RuntimeErrorHandler.hadRuntimeError,\n      dependencyChain: err ? err.dependencyChain : undefined,\n    })\n  )\n\n  if (reloading) return\n  reloading = true\n  window.location.reload()\n}\n\n// Attempt to update code on the fly, fall back to a hard reload.\nfunction tryApplyUpdates(\n  onBeforeUpdate: (hasUpdates: boolean) => void,\n  onHotUpdateSuccess: (updatedModules: string[]) => void,\n  sendMessage: any,\n  dispatcher: Dispatcher\n) {\n  if (!isUpdateAvailable() || !canApplyUpdates()) {\n    resolvePendingHotUpdateWebpack()\n    dispatcher.onBuildOk()\n    reportHmrLatency(sendMessage, [])\n    return\n  }\n\n  function handleApplyUpdates(err: any, updatedModules: string[] | null) {\n    if (err || RuntimeErrorHandler.hadRuntimeError || !updatedModules) {\n      if (err) {\n        console.warn(\n          '[Fast Refresh] performing full reload\\n\\n' +\n            \"Fast Refresh will perform a full reload when you edit a file that's imported by modules outside of the React rendering tree.\\n\" +\n            'You might have a file which exports a React component but also exports a value that is imported by a non-React component file.\\n' +\n            'Consider migrating the non-React component export to a separate file and importing it into both files.\\n\\n' +\n            'It is also possible the parent component of the component you edited is a class component, which disables Fast Refresh.\\n' +\n            'Fast Refresh requires at least one parent function component in your React tree.'\n        )\n      } else if (RuntimeErrorHandler.hadRuntimeError) {\n        console.warn(REACT_REFRESH_FULL_RELOAD_FROM_ERROR)\n      }\n      performFullReload(err, sendMessage)\n      return\n    }\n\n    const hasUpdates = Boolean(updatedModules.length)\n    if (typeof onHotUpdateSuccess === 'function') {\n      // Maybe we want to do something.\n      onHotUpdateSuccess(updatedModules)\n    }\n\n    if (isUpdateAvailable()) {\n      // While we were updating, there was a new update! Do it again.\n      tryApplyUpdates(\n        hasUpdates ? () => {} : onBeforeUpdate,\n        hasUpdates ? () => dispatcher.onBuildOk() : onHotUpdateSuccess,\n        sendMessage,\n        dispatcher\n      )\n    } else {\n      dispatcher.onBuildOk()\n      if (process.env.__NEXT_TEST_MODE) {\n        afterApplyUpdates(() => {\n          if (self.__NEXT_HMR_CB) {\n            self.__NEXT_HMR_CB()\n            self.__NEXT_HMR_CB = null\n          }\n        })\n      }\n    }\n  }\n\n  // https://webpack.js.org/api/hot-module-replacement/#check\n  // @ts-expect-error module.hot exists\n  module.hot\n    .check(/* autoApply */ false)\n    .then((updatedModules: any[] | null) => {\n      if (!updatedModules) {\n        return null\n      }\n\n      if (typeof onBeforeUpdate === 'function') {\n        const hasUpdates = Boolean(updatedModules.length)\n        onBeforeUpdate(hasUpdates)\n      }\n      // https://webpack.js.org/api/hot-module-replacement/#apply\n      // @ts-expect-error module.hot exists\n      return module.hot.apply()\n    })\n    .then(\n      (updatedModules: any[] | null) => {\n        handleApplyUpdates(null, updatedModules)\n      },\n      (err: any) => {\n        handleApplyUpdates(err, null)\n      }\n    )\n}\n\n/** Handles messages from the sevrer for the App Router. */\nfunction processMessage(\n  obj: HMR_ACTION_TYPES,\n  sendMessage: (message: string) => void,\n  processTurbopackMessage: (msg: TurbopackMsgToBrowser) => void,\n  router: ReturnType<typeof useRouter>,\n  dispatcher: Dispatcher,\n  appIsrManifestRef: ReturnType<typeof useRef>,\n  pathnameRef: ReturnType<typeof useRef>\n) {\n  if (!('action' in obj)) {\n    return\n  }\n\n  function handleErrors(errors: ReadonlyArray<unknown>) {\n    // \"Massage\" webpack messages.\n    const formatted = formatWebpackMessages({\n      errors: errors,\n      warnings: [],\n    })\n\n    // Only show the first error.\n    dispatcher.onBuildError(formatted.errors[0])\n\n    // Also log them to the console.\n    for (let i = 0; i < formatted.errors.length; i++) {\n      console.error(stripAnsi(formatted.errors[i]))\n    }\n\n    // Do not attempt to reload now.\n    // We will reload on next success instead.\n    if (process.env.__NEXT_TEST_MODE) {\n      if (self.__NEXT_HMR_CB) {\n        self.__NEXT_HMR_CB(formatted.errors[0])\n        self.__NEXT_HMR_CB = null\n      }\n    }\n  }\n\n  function handleHotUpdate() {\n    if (process.env.TURBOPACK) {\n      dispatcher.onBuildOk()\n      reportHmrLatency(sendMessage, [...turbopackUpdatedModules])\n    } else {\n      tryApplyUpdates(\n        function onBeforeHotUpdate(hasUpdates: boolean) {\n          handleBeforeHotUpdateWebpack(dispatcher, hasUpdates)\n        },\n        function onSuccessfulHotUpdate(webpackUpdatedModules: string[]) {\n          // Only dismiss it when we're sure it's a hot update.\n          // Otherwise it would flicker right before the reload.\n          handleSuccessfulHotUpdateWebpack(\n            dispatcher,\n            sendMessage,\n            webpackUpdatedModules\n          )\n        },\n        sendMessage,\n        dispatcher\n      )\n    }\n  }\n\n  switch (obj.action) {\n    case HMR_ACTIONS_SENT_TO_BROWSER.ISR_MANIFEST: {\n      if (process.env.__NEXT_DEV_INDICATOR) {\n        if (appIsrManifestRef) {\n          appIsrManifestRef.current = obj.data\n\n          // handle initial status on receiving manifest\n          // navigation is handled in useEffect for pathname changes\n          // as we'll receive the updated manifest before usePathname\n          // triggers for new value\n          if ((pathnameRef.current as string) in obj.data) {\n            dispatcher.onStaticIndicator(true)\n          } else {\n            dispatcher.onStaticIndicator(false)\n          }\n        }\n      }\n      break\n    }\n    case HMR_ACTIONS_SENT_TO_BROWSER.BUILDING: {\n      startLatency = Date.now()\n      turbopackLastUpdateLatency = null\n      turbopackUpdatedModules.clear()\n      if (!process.env.TURBOPACK) {\n        setPendingHotUpdateWebpack()\n      }\n      console.log('[Fast Refresh] rebuilding')\n      break\n    }\n    case HMR_ACTIONS_SENT_TO_BROWSER.BUILT:\n    case HMR_ACTIONS_SENT_TO_BROWSER.SYNC: {\n      if (obj.hash) {\n        handleAvailableHash(obj.hash)\n      }\n\n      const { errors, warnings } = obj\n\n      // Is undefined when it's a 'built' event\n      if ('versionInfo' in obj) dispatcher.onVersionInfo(obj.versionInfo)\n      if ('debug' in obj && obj.debug) dispatcher.onDebugInfo(obj.debug)\n      if ('devIndicator' in obj) dispatcher.onDevIndicator(obj.devIndicator)\n\n      const hasErrors = Boolean(errors && errors.length)\n      // Compilation with errors (e.g. syntax error or missing modules).\n      if (hasErrors) {\n        sendMessage(\n          JSON.stringify({\n            event: 'client-error',\n            errorCount: errors.length,\n            clientId: __nextDevClientId,\n          })\n        )\n\n        handleErrors(errors)\n        return\n      }\n\n      const hasWarnings = Boolean(warnings && warnings.length)\n      if (hasWarnings) {\n        sendMessage(\n          JSON.stringify({\n            event: 'client-warning',\n            warningCount: warnings.length,\n            clientId: __nextDevClientId,\n          })\n        )\n\n        // Print warnings to the console.\n        const formattedMessages = formatWebpackMessages({\n          warnings: warnings,\n          errors: [],\n        })\n\n        for (let i = 0; i < formattedMessages.warnings.length; i++) {\n          if (i === 5) {\n            console.warn(\n              'There were more warnings in other files.\\n' +\n                'You can find a complete log in the terminal.'\n            )\n            break\n          }\n          console.warn(stripAnsi(formattedMessages.warnings[i]))\n        }\n\n        // No early return here as we need to apply modules in the same way between warnings only and compiles without warnings\n      }\n\n      sendMessage(\n        JSON.stringify({\n          event: 'client-success',\n          clientId: __nextDevClientId,\n        })\n      )\n\n      if (obj.action === HMR_ACTIONS_SENT_TO_BROWSER.BUILT) {\n        // Handle hot updates\n        handleHotUpdate()\n      }\n      return\n    }\n    case HMR_ACTIONS_SENT_TO_BROWSER.TURBOPACK_CONNECTED: {\n      processTurbopackMessage({\n        type: HMR_ACTIONS_SENT_TO_BROWSER.TURBOPACK_CONNECTED,\n        data: {\n          sessionId: obj.data.sessionId,\n        },\n      })\n      break\n    }\n    case HMR_ACTIONS_SENT_TO_BROWSER.TURBOPACK_MESSAGE: {\n      dispatcher.onBeforeRefresh()\n      processTurbopackMessage({\n        type: HMR_ACTIONS_SENT_TO_BROWSER.TURBOPACK_MESSAGE,\n        data: obj.data,\n      })\n      dispatcher.onRefresh()\n      if (RuntimeErrorHandler.hadRuntimeError) {\n        console.warn(REACT_REFRESH_FULL_RELOAD_FROM_ERROR)\n        performFullReload(null, sendMessage)\n      }\n      for (const module of extractModulesFromTurbopackMessage(obj.data)) {\n        turbopackUpdatedModules.add(module)\n      }\n      turbopackLastUpdateLatency = Date.now()\n      break\n    }\n    // TODO-APP: make server component change more granular\n    case HMR_ACTIONS_SENT_TO_BROWSER.SERVER_COMPONENT_CHANGES: {\n      sendMessage(\n        JSON.stringify({\n          event: 'server-component-reload-page',\n          clientId: __nextDevClientId,\n          hash: obj.hash,\n        })\n      )\n\n      // Store the latest hash in a session cookie so that it's sent back to the\n      // server with any subsequent requests.\n      document.cookie = `__next_hmr_refresh_hash__=${obj.hash}`\n\n      if (RuntimeErrorHandler.hadRuntimeError) {\n        if (reloading) return\n        reloading = true\n        return window.location.reload()\n      }\n\n      startTransition(() => {\n        router.hmrRefresh()\n        dispatcher.onRefresh()\n      })\n\n      if (process.env.__NEXT_TEST_MODE) {\n        if (self.__NEXT_HMR_CB) {\n          self.__NEXT_HMR_CB()\n          self.__NEXT_HMR_CB = null\n        }\n      }\n\n      return\n    }\n    case HMR_ACTIONS_SENT_TO_BROWSER.RELOAD_PAGE: {\n      sendMessage(\n        JSON.stringify({\n          event: 'client-reload-page',\n          clientId: __nextDevClientId,\n        })\n      )\n      if (reloading) return\n      reloading = true\n      return window.location.reload()\n    }\n    case HMR_ACTIONS_SENT_TO_BROWSER.ADDED_PAGE:\n    case HMR_ACTIONS_SENT_TO_BROWSER.REMOVED_PAGE: {\n      // TODO-APP: potentially only refresh if the currently viewed page was added/removed.\n      return router.hmrRefresh()\n    }\n    case HMR_ACTIONS_SENT_TO_BROWSER.SERVER_ERROR: {\n      const { errorJSON } = obj\n      if (errorJSON) {\n        const { message, stack } = JSON.parse(errorJSON)\n        const error = new Error(message)\n        error.stack = stack\n        handleErrors([error])\n      }\n      return\n    }\n    case HMR_ACTIONS_SENT_TO_BROWSER.DEV_PAGES_MANIFEST_UPDATE: {\n      return\n    }\n    default: {\n    }\n  }\n}\n\nexport default function HotReload({\n  assetPrefix,\n  children,\n  globalError,\n}: {\n  assetPrefix: string\n  children: ReactNode\n  globalError: [GlobalErrorComponent, React.ReactNode]\n}) {\n  const [state, dispatch] = useErrorOverlayReducer('app')\n\n  const dispatcher = useMemo<Dispatcher>(() => {\n    return {\n      onBuildOk() {\n        dispatch({ type: ACTION_BUILD_OK })\n      },\n      onBuildError(message) {\n        dispatch({ type: ACTION_BUILD_ERROR, message })\n      },\n      onBeforeRefresh() {\n        dispatch({ type: ACTION_BEFORE_REFRESH })\n      },\n      onRefresh() {\n        dispatch({ type: ACTION_REFRESH })\n      },\n      onVersionInfo(versionInfo) {\n        dispatch({ type: ACTION_VERSION_INFO, versionInfo })\n      },\n      onStaticIndicator(status: boolean) {\n        dispatch({ type: ACTION_STATIC_INDICATOR, staticIndicator: status })\n      },\n      onDebugInfo(debugInfo) {\n        dispatch({ type: ACTION_DEBUG_INFO, debugInfo })\n      },\n      onDevIndicator(devIndicator) {\n        dispatch({\n          type: ACTION_DEV_INDICATOR,\n          devIndicator,\n        })\n      },\n    }\n  }, [dispatch])\n\n  //  We render a separate error overlay at the root when an error is thrown from rendering RSC, so\n  //  we should not render an additional error overlay in the descendent. However, we need to\n  //  keep rendering these hooks to ensure HMR works when the error is addressed.\n  const shouldRenderErrorOverlay = useSyncExternalStore(\n    () => () => {},\n    () => !shouldRenderRootLevelErrorOverlay(),\n    () => true\n  )\n\n  const handleOnUnhandledError = useCallback(\n    (error: Error): void => {\n      const errorDetails = (error as any).details as\n        | HydrationErrorState\n        | undefined\n      // Component stack is added to the error in use-error-handler in case there was a hydration error\n      const componentStackTrace =\n        (error as any)._componentStack || errorDetails?.componentStack\n      const warning = errorDetails?.warning\n\n      dispatch({\n        type: ACTION_UNHANDLED_ERROR,\n        reason: error,\n        frames: parseStack(error.stack || ''),\n        componentStackFrames:\n          typeof componentStackTrace === 'string'\n            ? parseComponentStack(componentStackTrace)\n            : undefined,\n        warning,\n      })\n    },\n    [dispatch]\n  )\n\n  const handleOnUnhandledRejection = useCallback(\n    (reason: Error): void => {\n      const stitchedError = getReactStitchedError(reason)\n      dispatch({\n        type: ACTION_UNHANDLED_REJECTION,\n        reason: stitchedError,\n        frames: parseStack(stitchedError.stack || ''),\n      })\n    },\n    [dispatch]\n  )\n  useErrorHandler(handleOnUnhandledError, handleOnUnhandledRejection)\n\n  const webSocketRef = useWebsocket(assetPrefix)\n  useWebsocketPing(webSocketRef)\n  const sendMessage = useSendMessage(webSocketRef)\n  const processTurbopackMessage = useTurbopack(sendMessage, (err) =>\n    performFullReload(err, sendMessage)\n  )\n\n  const router = useRouter()\n\n  // We don't want access of the pathname for the dev tools to trigger a dynamic\n  // access (as the dev overlay will never be present in production).\n  const pathname = useUntrackedPathname()\n  const appIsrManifestRef = useRef<Record<string, false | number>>({})\n  const pathnameRef = useRef(pathname)\n\n  if (process.env.__NEXT_DEV_INDICATOR) {\n    // this conditional is only for dead-code elimination which\n    // isn't a runtime conditional only build-time so ignore hooks rule\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    useEffect(() => {\n      pathnameRef.current = pathname\n\n      const appIsrManifest = appIsrManifestRef.current\n\n      if (appIsrManifest) {\n        if (pathname && pathname in appIsrManifest) {\n          try {\n            dispatcher.onStaticIndicator(true)\n          } catch (reason) {\n            let message = ''\n\n            if (reason instanceof DOMException) {\n              // Most likely a SecurityError, because of an unavailable localStorage\n              message = reason.stack ?? reason.message\n            } else if (reason instanceof Error) {\n              message = 'Error: ' + reason.message + '\\n' + (reason.stack ?? '')\n            } else {\n              message = 'Unexpected Exception: ' + reason\n            }\n\n            console.warn('[HMR] ' + message)\n          }\n        } else {\n          dispatcher.onStaticIndicator(false)\n        }\n      }\n    }, [pathname, dispatcher])\n  }\n\n  useEffect(() => {\n    const websocket = webSocketRef.current\n    if (!websocket) return\n\n    const handler = (event: MessageEvent<any>) => {\n      try {\n        const obj = JSON.parse(event.data)\n        handleDevBuildIndicatorHmrEvents(obj)\n        processMessage(\n          obj,\n          sendMessage,\n          processTurbopackMessage,\n          router,\n          dispatcher,\n          appIsrManifestRef,\n          pathnameRef\n        )\n      } catch (err: any) {\n        console.warn(\n          '[HMR] Invalid message: ' +\n            JSON.stringify(event.data) +\n            '\\n' +\n            (err?.stack ?? '')\n        )\n      }\n    }\n\n    websocket.addEventListener('message', handler)\n    return () => websocket.removeEventListener('message', handler)\n  }, [\n    sendMessage,\n    router,\n    webSocketRef,\n    dispatcher,\n    processTurbopackMessage,\n    appIsrManifestRef,\n  ])\n\n  if (shouldRenderErrorOverlay) {\n    return (\n      <AppDevOverlay state={state} globalError={globalError}>\n        {children}\n      </AppDevOverlay>\n    )\n  }\n\n  return children\n}\n"], "names": ["useCallback", "useEffect", "startTransition", "useMemo", "useRef", "useSyncExternalStore", "stripAnsi", "formatWebpackMessages", "useRouter", "ACTION_BEFORE_REFRESH", "ACTION_BUILD_ERROR", "ACTION_BUILD_OK", "ACTION_DEBUG_INFO", "ACTION_DEV_INDICATOR", "ACTION_REFRESH", "ACTION_STATIC_INDICATOR", "ACTION_UNHANDLED_ERROR", "ACTION_UNHANDLED_REJECTION", "ACTION_VERSION_INFO", "useErrorOverlayReducer", "parseStack", "AppDevOverlay", "useErrorHandler", "RuntimeError<PERSON>andler", "useSendMessage", "useTurbopack", "useWebsocket", "useWebsocketPing", "parseComponentStack", "HMR_ACTIONS_SENT_TO_BROWSER", "extractModulesFromTurbopackMessage", "REACT_REFRESH_FULL_RELOAD_FROM_ERROR", "useUntrackedPathname", "getReactStitchedError", "shouldRenderRootLevelErrorOverlay", "handleDevBuildIndicatorHmrEvents", "mostRecentCompilationHash", "__nextDevClientId", "Math", "round", "random", "Date", "now", "reloading", "startLatency", "turbopackLastUpdateLatency", "turbopackUpdatedModules", "Set", "pendingHotUpdateWebpack", "Promise", "resolve", "resolvePendingHotUpdateWebpack", "setPendingHotUpdateWebpack", "waitForWebpackRuntimeHotUpdate", "handleBeforeHotUpdateWebpack", "dispatcher", "hasUpdates", "onBeforeRefresh", "handleSuccessfulHotUpdateWebpack", "sendMessage", "updatedModules", "onBuildOk", "reportHmrLatency", "onRefresh", "endLatency", "latency", "console", "log", "JSON", "stringify", "event", "id", "window", "startTime", "endTime", "page", "location", "pathname", "isPageHidden", "document", "visibilityState", "handleAvailableHash", "hash", "isUpdateAvailable", "process", "env", "TURBOPACK", "__webpack_hash__", "canApplyUpdates", "module", "hot", "status", "afterApplyUpdates", "fn", "handler", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "addStatusHandler", "performFullReload", "err", "stackTrace", "stack", "split", "slice", "join", "message", "hadRuntimeError", "dependency<PERSON><PERSON>n", "undefined", "reload", "tryApplyUpdates", "onBeforeUpdate", "onHotUpdateSuccess", "handleApplyUpdates", "warn", "Boolean", "length", "__NEXT_TEST_MODE", "self", "__NEXT_HMR_CB", "check", "then", "apply", "processMessage", "obj", "processTurbopackMessage", "router", "appIsrManifestRef", "pathnameRef", "handleErrors", "errors", "formatted", "warnings", "onBuildError", "i", "error", "handleHotUpdate", "onBeforeHotUpdate", "onSuccessfulHotUpdate", "webpackUpdatedModules", "action", "ISR_MANIFEST", "__NEXT_DEV_INDICATOR", "current", "data", "onStaticIndicator", "BUILDING", "clear", "BUILT", "SYNC", "onVersionInfo", "versionInfo", "debug", "onDebugInfo", "onDevIndicator", "devIndicator", "hasErrors", "errorCount", "clientId", "hasWarnings", "warningCount", "formattedMessages", "TURBOPACK_CONNECTED", "type", "sessionId", "TURBOPACK_MESSAGE", "add", "SERVER_COMPONENT_CHANGES", "cookie", "hmrRefresh", "RELOAD_PAGE", "ADDED_PAGE", "REMOVED_PAGE", "SERVER_ERROR", "errorJSON", "parse", "Error", "DEV_PAGES_MANIFEST_UPDATE", "HotReload", "assetPrefix", "children", "globalError", "state", "dispatch", "staticIndicator", "debugInfo", "shouldRenderErrorOverlay", "handleOnUnhandledError", "errorDetails", "details", "componentStackTrace", "_componentStack", "componentStack", "warning", "reason", "frames", "componentStackFrames", "handleOnUnhandledRejection", "stitchedError", "webSocketRef", "appIsrManifest", "DOMException", "websocket", "addEventListener", "removeEventListener"], "mappings": ";AACA,SACEA,WAAW,EACXC,SAAS,EACTC,eAAe,EACfC,OAAO,EACPC,MAAM,EACNC,oBAAoB,QACf,QAAO;AACd,OAAOC,eAAe,gCAA+B;AACrD,OAAOC,2BAA2B,mCAAkC;AACpE,SAASC,SAAS,QAAQ,mBAAkB;AAC5C,SACEC,qBAAqB,EACrBC,kBAAkB,EAClBC,eAAe,EACfC,iBAAiB,EACjBC,oBAAoB,EACpBC,cAAc,EACdC,uBAAuB,EACvBC,sBAAsB,EACtBC,0BAA0B,EAC1BC,mBAAmB,EACnBC,sBAAsB,QACjB,YAAW;AAClB,SAASC,UAAU,QAAQ,uBAAsB;AACjD,SAASC,aAAa,QAAQ,oBAAmB;AACjD,SAASC,eAAe,QAAQ,iCAAgC;AAChE,SAASC,mBAAmB,QAAQ,qCAAoC;AACxE,SACEC,cAAc,EACdC,YAAY,EACZC,YAAY,EACZC,gBAAgB,QACX,yBAAwB;AAC/B,SAASC,mBAAmB,QAAQ,iCAAgC;AAEpE,SAASC,2BAA2B,QAAQ,4CAA2C;AAKvF,SAASC,kCAAkC,QAAQ,gEAA+D;AAClH,SAASC,oCAAoC,QAAQ,YAAW;AAGhE,SAASC,oBAAoB,QAAQ,6BAA4B;AACjE,SAASC,qBAAqB,QAAQ,8BAA6B;AACnE,SAASC,iCAAiC,QAAQ,mDAAkD;AACpG,SAASC,gCAAgC,QAAQ,kFAAiF;AAelI,IAAIC,4BAAiC;AACrC,IAAIC,oBAAoBC,KAAKC,KAAK,CAACD,KAAKE,MAAM,KAAK,MAAMC,KAAKC,GAAG;AACjE,IAAIC,YAAY;AAChB,IAAIC,eAA8B;AAClC,IAAIC,6BAA4C;AAChD,IAAIC,0BAAuC,IAAIC;AAE/C,IAAIC,0BAA0BC,QAAQC,OAAO;AAC7C,IAAIC,iCAA6C,KAAO;AACxD,SAASC;IACPJ,0BAA0B,IAAIC,QAAQ,CAACC;QACrCC,iCAAiC;YAC/BD;QACF;IACF;AACF;AAEA,OAAO,SAASG;IACd,OAAOL;AACT;AAEA,SAASM,6BACPC,UAAsB,EACtBC,UAAmB;IAEnB,IAAIA,YAAY;QACdD,WAAWE,eAAe;IAC5B;AACF;AAEA,SAASC,iCACPH,UAAsB,EACtBI,WAAsC,EACtCC,cAAqC;IAErCT;IACAI,WAAWM,SAAS;IACpBC,iBAAiBH,aAAaC;IAE9BL,WAAWQ,SAAS;AACtB;AAEA,SAASD,iBACPH,WAAsC,EACtCC,cAAqC;IAErC,IAAI,CAAChB,cAAc;IACnB,wEAAwE;IACxE,uEAAuE;IACvE,IAAIoB,aAAanB,qCAAAA,6BAA8BJ,KAAKC,GAAG;IACvD,MAAMuB,UAAUD,aAAapB;IAC7BsB,QAAQC,GAAG,CAAC,AAAC,4BAAyBF,UAAQ;IAC9CN,YACES,KAAKC,SAAS,CAAC;QACbC,OAAO;QACPC,IAAIC,OAAOnC,iBAAiB;QAC5BoC,WAAW7B;QACX8B,SAASV;QACTW,MAAMH,OAAOI,QAAQ,CAACC,QAAQ;QAC9BjB;QACA,oEAAoE;QACpE,sDAAsD;QACtDkB,cAAcC,SAASC,eAAe,KAAK;IAC7C;AAEJ;AAEA,kDAAkD;AAClD,SAASC,oBAAoBC,IAAY;IACvC,sCAAsC;IACtC9C,4BAA4B8C;AAC9B;AAEA;;;;CAIC,GACD,SAASC;IACP,IAAIC,QAAQC,GAAG,CAACC,SAAS,EAAE;QACzB,OAAO;IACT;IAEA,4BAA4B,GAC5B,2DAA2D;IAC3D,8CAA8C;IAC9C,OAAOlD,8BAA8BmD;AACvC;AAEA,6CAA6C;AAC7C,SAASC;IACP,qCAAqC;IACrC,OAAOC,OAAOC,GAAG,CAACC,MAAM,OAAO;AACjC;AACA,SAASC,kBAAkBC,EAAO;IAChC,IAAIL,mBAAmB;QACrBK;IACF,OAAO;QACL,SAASC,QAAQH,MAAW;YAC1B,IAAIA,WAAW,QAAQ;gBACrB,qCAAqC;gBACrCF,OAAOC,GAAG,CAACK,mBAAmB,CAACD;gBAC/BD;YACF;QACF;QACA,qCAAqC;QACrCJ,OAAOC,GAAG,CAACM,gBAAgB,CAACF;IAC9B;AACF;AAEA,SAASG,kBAAkBC,GAAQ,EAAEvC,WAAgB;IACnD,MAAMwC,aACJD,OACC,CAAA,AAACA,IAAIE,KAAK,IAAIF,IAAIE,KAAK,CAACC,KAAK,CAAC,MAAMC,KAAK,CAAC,GAAG,GAAGC,IAAI,CAAC,SACpDL,IAAIM,OAAO,IACXN,MAAM,EAAC;IAEXvC,YACES,KAAKC,SAAS,CAAC;QACbC,OAAO;QACP6B;QACAM,iBAAiB,CAAC,CAAClF,oBAAoBkF,eAAe;QACtDC,iBAAiBR,MAAMA,IAAIQ,eAAe,GAAGC;IAC/C;IAGF,IAAIhE,WAAW;IACfA,YAAY;IACZ6B,OAAOI,QAAQ,CAACgC,MAAM;AACxB;AAEA,iEAAiE;AACjE,SAASC,gBACPC,cAA6C,EAC7CC,kBAAsD,EACtDpD,WAAgB,EAChBJ,UAAsB;IAEtB,IAAI,CAAC4B,uBAAuB,CAACK,mBAAmB;QAC9CrC;QACAI,WAAWM,SAAS;QACpBC,iBAAiBH,aAAa,EAAE;QAChC;IACF;IAEA,SAASqD,mBAAmBd,GAAQ,EAAEtC,cAA+B;QACnE,IAAIsC,OAAO3E,oBAAoBkF,eAAe,IAAI,CAAC7C,gBAAgB;YACjE,IAAIsC,KAAK;gBACPhC,QAAQ+C,IAAI,CACV,8CACE,mIACA,qIACA,+GACA,8HACA;YAEN,OAAO,IAAI1F,oBAAoBkF,eAAe,EAAE;gBAC9CvC,QAAQ+C,IAAI,CAAClF;YACf;YACAkE,kBAAkBC,KAAKvC;YACvB;QACF;QAEA,MAAMH,aAAa0D,QAAQtD,eAAeuD,MAAM;QAChD,IAAI,OAAOJ,uBAAuB,YAAY;YAC5C,iCAAiC;YACjCA,mBAAmBnD;QACrB;QAEA,IAAIuB,qBAAqB;YACvB,+DAA+D;YAC/D0B,gBACErD,aAAa,KAAO,IAAIsD,gBACxBtD,aAAa,IAAMD,WAAWM,SAAS,KAAKkD,oBAC5CpD,aACAJ;QAEJ,OAAO;YACLA,WAAWM,SAAS;YACpB,IAAIuB,QAAQC,GAAG,CAAC+B,gBAAgB,EAAE;gBAChCxB,kBAAkB;oBAChB,IAAIyB,KAAKC,aAAa,EAAE;wBACtBD,KAAKC,aAAa;wBAClBD,KAAKC,aAAa,GAAG;oBACvB;gBACF;YACF;QACF;IACF;IAEA,2DAA2D;IAC3D,qCAAqC;IACrC7B,OAAOC,GAAG,CACP6B,KAAK,CAAC,aAAa,GAAG,OACtBC,IAAI,CAAC,CAAC5D;QACL,IAAI,CAACA,gBAAgB;YACnB,OAAO;QACT;QAEA,IAAI,OAAOkD,mBAAmB,YAAY;YACxC,MAAMtD,aAAa0D,QAAQtD,eAAeuD,MAAM;YAChDL,eAAetD;QACjB;QACA,2DAA2D;QAC3D,qCAAqC;QACrC,OAAOiC,OAAOC,GAAG,CAAC+B,KAAK;IACzB,GACCD,IAAI,CACH,CAAC5D;QACCoD,mBAAmB,MAAMpD;IAC3B,GACA,CAACsC;QACCc,mBAAmBd,KAAK;IAC1B;AAEN;AAEA,yDAAyD,GACzD,SAASwB,eACPC,GAAqB,EACrBhE,WAAsC,EACtCiE,uBAA6D,EAC7DC,MAAoC,EACpCtE,UAAsB,EACtBuE,iBAA4C,EAC5CC,WAAsC;IAEtC,IAAI,CAAE,CAAA,YAAYJ,GAAE,GAAI;QACtB;IACF;IAEA,SAASK,aAAaC,MAA8B;QAClD,8BAA8B;QAC9B,MAAMC,YAAY3H,sBAAsB;YACtC0H,QAAQA;YACRE,UAAU,EAAE;QACd;QAEA,6BAA6B;QAC7B5E,WAAW6E,YAAY,CAACF,UAAUD,MAAM,CAAC,EAAE;QAE3C,gCAAgC;QAChC,IAAK,IAAII,IAAI,GAAGA,IAAIH,UAAUD,MAAM,CAACd,MAAM,EAAEkB,IAAK;YAChDnE,QAAQoE,KAAK,CAAChI,UAAU4H,UAAUD,MAAM,CAACI,EAAE;QAC7C;QAEA,gCAAgC;QAChC,0CAA0C;QAC1C,IAAIjD,QAAQC,GAAG,CAAC+B,gBAAgB,EAAE;YAChC,IAAIC,KAAKC,aAAa,EAAE;gBACtBD,KAAKC,aAAa,CAACY,UAAUD,MAAM,CAAC,EAAE;gBACtCZ,KAAKC,aAAa,GAAG;YACvB;QACF;IACF;IAEA,SAASiB;QACP,IAAInD,QAAQC,GAAG,CAACC,SAAS,EAAE;YACzB/B,WAAWM,SAAS;YACpBC,iBAAiBH,aAAa;mBAAIb;aAAwB;QAC5D,OAAO;YACL+D,gBACE,SAAS2B,kBAAkBhF,UAAmB;gBAC5CF,6BAA6BC,YAAYC;YAC3C,GACA,SAASiF,sBAAsBC,qBAA+B;gBAC5D,qDAAqD;gBACrD,sDAAsD;gBACtDhF,iCACEH,YACAI,aACA+E;YAEJ,GACA/E,aACAJ;QAEJ;IACF;IAEA,OAAQoE,IAAIgB,MAAM;QAChB,KAAK9G,4BAA4B+G,YAAY;YAAE;gBAC7C,IAAIxD,QAAQC,GAAG,CAACwD,oBAAoB,EAAE;oBACpC,IAAIf,mBAAmB;wBACrBA,kBAAkBgB,OAAO,GAAGnB,IAAIoB,IAAI;wBAEpC,8CAA8C;wBAC9C,0DAA0D;wBAC1D,2DAA2D;wBAC3D,yBAAyB;wBACzB,IAAI,AAAChB,YAAYe,OAAO,IAAenB,IAAIoB,IAAI,EAAE;4BAC/CxF,WAAWyF,iBAAiB,CAAC;wBAC/B,OAAO;4BACLzF,WAAWyF,iBAAiB,CAAC;wBAC/B;oBACF;gBACF;gBACA;YACF;QACA,KAAKnH,4BAA4BoH,QAAQ;YAAE;gBACzCrG,eAAeH,KAAKC,GAAG;gBACvBG,6BAA6B;gBAC7BC,wBAAwBoG,KAAK;gBAC7B,IAAI,CAAC9D,QAAQC,GAAG,CAACC,SAAS,EAAE;oBAC1BlC;gBACF;gBACAc,QAAQC,GAAG,CAAC;gBACZ;YACF;QACA,KAAKtC,4BAA4BsH,KAAK;QACtC,KAAKtH,4BAA4BuH,IAAI;YAAE;gBACrC,IAAIzB,IAAIzC,IAAI,EAAE;oBACZD,oBAAoB0C,IAAIzC,IAAI;gBAC9B;gBAEA,MAAM,EAAE+C,MAAM,EAAEE,QAAQ,EAAE,GAAGR;gBAE7B,yCAAyC;gBACzC,IAAI,iBAAiBA,KAAKpE,WAAW8F,aAAa,CAAC1B,IAAI2B,WAAW;gBAClE,IAAI,WAAW3B,OAAOA,IAAI4B,KAAK,EAAEhG,WAAWiG,WAAW,CAAC7B,IAAI4B,KAAK;gBACjE,IAAI,kBAAkB5B,KAAKpE,WAAWkG,cAAc,CAAC9B,IAAI+B,YAAY;gBAErE,MAAMC,YAAYzC,QAAQe,UAAUA,OAAOd,MAAM;gBACjD,kEAAkE;gBAClE,IAAIwC,WAAW;oBACbhG,YACES,KAAKC,SAAS,CAAC;wBACbC,OAAO;wBACPsF,YAAY3B,OAAOd,MAAM;wBACzB0C,UAAUxH;oBACZ;oBAGF2F,aAAaC;oBACb;gBACF;gBAEA,MAAM6B,cAAc5C,QAAQiB,YAAYA,SAAShB,MAAM;gBACvD,IAAI2C,aAAa;oBACfnG,YACES,KAAKC,SAAS,CAAC;wBACbC,OAAO;wBACPyF,cAAc5B,SAAShB,MAAM;wBAC7B0C,UAAUxH;oBACZ;oBAGF,iCAAiC;oBACjC,MAAM2H,oBAAoBzJ,sBAAsB;wBAC9C4H,UAAUA;wBACVF,QAAQ,EAAE;oBACZ;oBAEA,IAAK,IAAII,IAAI,GAAGA,IAAI2B,kBAAkB7B,QAAQ,CAAChB,MAAM,EAAEkB,IAAK;wBAC1D,IAAIA,MAAM,GAAG;4BACXnE,QAAQ+C,IAAI,CACV,+CACE;4BAEJ;wBACF;wBACA/C,QAAQ+C,IAAI,CAAC3G,UAAU0J,kBAAkB7B,QAAQ,CAACE,EAAE;oBACtD;gBAEA,uHAAuH;gBACzH;gBAEA1E,YACES,KAAKC,SAAS,CAAC;oBACbC,OAAO;oBACPuF,UAAUxH;gBACZ;gBAGF,IAAIsF,IAAIgB,MAAM,KAAK9G,4BAA4BsH,KAAK,EAAE;oBACpD,qBAAqB;oBACrBZ;gBACF;gBACA;YACF;QACA,KAAK1G,4BAA4BoI,mBAAmB;YAAE;gBACpDrC,wBAAwB;oBACtBsC,MAAMrI,4BAA4BoI,mBAAmB;oBACrDlB,MAAM;wBACJoB,WAAWxC,IAAIoB,IAAI,CAACoB,SAAS;oBAC/B;gBACF;gBACA;YACF;QACA,KAAKtI,4BAA4BuI,iBAAiB;YAAE;gBAClD7G,WAAWE,eAAe;gBAC1BmE,wBAAwB;oBACtBsC,MAAMrI,4BAA4BuI,iBAAiB;oBACnDrB,MAAMpB,IAAIoB,IAAI;gBAChB;gBACAxF,WAAWQ,SAAS;gBACpB,IAAIxC,oBAAoBkF,eAAe,EAAE;oBACvCvC,QAAQ+C,IAAI,CAAClF;oBACbkE,kBAAkB,MAAMtC;gBAC1B;gBACA,KAAK,MAAM8B,WAAU3D,mCAAmC6F,IAAIoB,IAAI,EAAG;oBACjEjG,wBAAwBuH,GAAG,CAAC5E;gBAC9B;gBACA5C,6BAA6BJ,KAAKC,GAAG;gBACrC;YACF;QACA,uDAAuD;QACvD,KAAKb,4BAA4ByI,wBAAwB;YAAE;gBACzD3G,YACES,KAAKC,SAAS,CAAC;oBACbC,OAAO;oBACPuF,UAAUxH;oBACV6C,MAAMyC,IAAIzC,IAAI;gBAChB;gBAGF,0EAA0E;gBAC1E,uCAAuC;gBACvCH,SAASwF,MAAM,GAAG,AAAC,+BAA4B5C,IAAIzC,IAAI;gBAEvD,IAAI3D,oBAAoBkF,eAAe,EAAE;oBACvC,IAAI9D,WAAW;oBACfA,YAAY;oBACZ,OAAO6B,OAAOI,QAAQ,CAACgC,MAAM;gBAC/B;gBAEA1G,gBAAgB;oBACd2H,OAAO2C,UAAU;oBACjBjH,WAAWQ,SAAS;gBACtB;gBAEA,IAAIqB,QAAQC,GAAG,CAAC+B,gBAAgB,EAAE;oBAChC,IAAIC,KAAKC,aAAa,EAAE;wBACtBD,KAAKC,aAAa;wBAClBD,KAAKC,aAAa,GAAG;oBACvB;gBACF;gBAEA;YACF;QACA,KAAKzF,4BAA4B4I,WAAW;YAAE;gBAC5C9G,YACES,KAAKC,SAAS,CAAC;oBACbC,OAAO;oBACPuF,UAAUxH;gBACZ;gBAEF,IAAIM,WAAW;gBACfA,YAAY;gBACZ,OAAO6B,OAAOI,QAAQ,CAACgC,MAAM;YAC/B;QACA,KAAK/E,4BAA4B6I,UAAU;QAC3C,KAAK7I,4BAA4B8I,YAAY;YAAE;gBAC7C,qFAAqF;gBACrF,OAAO9C,OAAO2C,UAAU;YAC1B;QACA,KAAK3I,4BAA4B+I,YAAY;YAAE;gBAC7C,MAAM,EAAEC,SAAS,EAAE,GAAGlD;gBACtB,IAAIkD,WAAW;oBACb,MAAM,EAAErE,OAAO,EAAEJ,KAAK,EAAE,GAAGhC,KAAK0G,KAAK,CAACD;oBACtC,MAAMvC,QAAQ,qBAAkB,CAAlB,IAAIyC,MAAMvE,UAAV,qBAAA;+BAAA;oCAAA;sCAAA;oBAAiB;oBAC/B8B,MAAMlC,KAAK,GAAGA;oBACd4B,aAAa;wBAACM;qBAAM;gBACtB;gBACA;YACF;QACA,KAAKzG,4BAA4BmJ,yBAAyB;YAAE;gBAC1D;YACF;QACA;YAAS,CACT;IACF;AACF;AAEA,eAAe,SAASC,UAAU,KAQjC;IARiC,IAAA,EAChCC,WAAW,EACXC,QAAQ,EACRC,WAAW,EAKZ,GARiC;IAShC,MAAM,CAACC,OAAOC,SAAS,GAAGnK,uBAAuB;IAEjD,MAAMoC,aAAapD,QAAoB;QACrC,OAAO;YACL0D;gBACEyH,SAAS;oBAAEpB,MAAMvJ;gBAAgB;YACnC;YACAyH,cAAa5B,OAAO;gBAClB8E,SAAS;oBAAEpB,MAAMxJ;oBAAoB8F;gBAAQ;YAC/C;YACA/C;gBACE6H,SAAS;oBAAEpB,MAAMzJ;gBAAsB;YACzC;YACAsD;gBACEuH,SAAS;oBAAEpB,MAAMpJ;gBAAe;YAClC;YACAuI,eAAcC,WAAW;gBACvBgC,SAAS;oBAAEpB,MAAMhJ;oBAAqBoI;gBAAY;YACpD;YACAN,mBAAkBrD,MAAe;gBAC/B2F,SAAS;oBAAEpB,MAAMnJ;oBAAyBwK,iBAAiB5F;gBAAO;YACpE;YACA6D,aAAYgC,SAAS;gBACnBF,SAAS;oBAAEpB,MAAMtJ;oBAAmB4K;gBAAU;YAChD;YACA/B,gBAAeC,YAAY;gBACzB4B,SAAS;oBACPpB,MAAMrJ;oBACN6I;gBACF;YACF;QACF;IACF,GAAG;QAAC4B;KAAS;IAEb,iGAAiG;IACjG,2FAA2F;IAC3F,+EAA+E;IAC/E,MAAMG,2BAA2BpL,qBAC/B,IAAM,KAAO,GACb,IAAM,CAAC6B,qCACP,IAAM;IAGR,MAAMwJ,yBAAyB1L,YAC7B,CAACsI;QACC,MAAMqD,eAAe,AAACrD,MAAcsD,OAAO;QAG3C,iGAAiG;QACjG,MAAMC,sBACJ,AAACvD,MAAcwD,eAAe,KAAIH,gCAAAA,aAAcI,cAAc;QAChE,MAAMC,UAAUL,gCAAAA,aAAcK,OAAO;QAErCV,SAAS;YACPpB,MAAMlJ;YACNiL,QAAQ3D;YACR4D,QAAQ9K,WAAWkH,MAAMlC,KAAK,IAAI;YAClC+F,sBACE,OAAON,wBAAwB,WAC3BjK,oBAAoBiK,uBACpBlF;YACNqF;QACF;IACF,GACA;QAACV;KAAS;IAGZ,MAAMc,6BAA6BpM,YACjC,CAACiM;QACC,MAAMI,gBAAgBpK,sBAAsBgK;QAC5CX,SAAS;YACPpB,MAAMjJ;YACNgL,QAAQI;YACRH,QAAQ9K,WAAWiL,cAAcjG,KAAK,IAAI;QAC5C;IACF,GACA;QAACkF;KAAS;IAEZhK,gBAAgBoK,wBAAwBU;IAExC,MAAME,eAAe5K,aAAawJ;IAClCvJ,iBAAiB2K;IACjB,MAAM3I,cAAcnC,eAAe8K;IACnC,MAAM1E,0BAA0BnG,aAAakC,aAAa,CAACuC,MACzDD,kBAAkBC,KAAKvC;IAGzB,MAAMkE,SAASrH;IAEf,8EAA8E;IAC9E,mEAAmE;IACnE,MAAMqE,WAAW7C;IACjB,MAAM8F,oBAAoB1H,OAAuC,CAAC;IAClE,MAAM2H,cAAc3H,OAAOyE;IAE3B,IAAIO,QAAQC,GAAG,CAACwD,oBAAoB,EAAE;QACpC,2DAA2D;QAC3D,mEAAmE;QACnE,sDAAsD;QACtD5I,UAAU;YACR8H,YAAYe,OAAO,GAAGjE;YAEtB,MAAM0H,iBAAiBzE,kBAAkBgB,OAAO;YAEhD,IAAIyD,gBAAgB;gBAClB,IAAI1H,YAAYA,YAAY0H,gBAAgB;oBAC1C,IAAI;wBACFhJ,WAAWyF,iBAAiB,CAAC;oBAC/B,EAAE,OAAOiD,QAAQ;wBACf,IAAIzF,UAAU;wBAEd,IAAIyF,kBAAkBO,cAAc;gCAExBP;4BADV,sEAAsE;4BACtEzF,UAAUyF,CAAAA,gBAAAA,OAAO7F,KAAK,YAAZ6F,gBAAgBA,OAAOzF,OAAO;wBAC1C,OAAO,IAAIyF,kBAAkBlB,OAAO;gCACakB;4BAA/CzF,UAAU,YAAYyF,OAAOzF,OAAO,GAAG,OAAQyF,CAAAA,CAAAA,iBAAAA,OAAO7F,KAAK,YAAZ6F,iBAAgB,EAAC;wBAClE,OAAO;4BACLzF,UAAU,2BAA2ByF;wBACvC;wBAEA/H,QAAQ+C,IAAI,CAAC,WAAWT;oBAC1B;gBACF,OAAO;oBACLjD,WAAWyF,iBAAiB,CAAC;gBAC/B;YACF;QACF,GAAG;YAACnE;YAAUtB;SAAW;IAC3B;IAEAtD,UAAU;QACR,MAAMwM,YAAYH,aAAaxD,OAAO;QACtC,IAAI,CAAC2D,WAAW;QAEhB,MAAM3G,UAAU,CAACxB;YACf,IAAI;gBACF,MAAMqD,MAAMvD,KAAK0G,KAAK,CAACxG,MAAMyE,IAAI;gBACjC5G,iCAAiCwF;gBACjCD,eACEC,KACAhE,aACAiE,yBACAC,QACAtE,YACAuE,mBACAC;YAEJ,EAAE,OAAO7B,KAAU;oBAKZA;gBAJLhC,QAAQ+C,IAAI,CACV,4BACE7C,KAAKC,SAAS,CAACC,MAAMyE,IAAI,IACzB,OACC7C,CAAAA,CAAAA,aAAAA,uBAAAA,IAAKE,KAAK,YAAVF,aAAc,EAAC;YAEtB;QACF;QAEAuG,UAAUC,gBAAgB,CAAC,WAAW5G;QACtC,OAAO,IAAM2G,UAAUE,mBAAmB,CAAC,WAAW7G;IACxD,GAAG;QACDnC;QACAkE;QACAyE;QACA/I;QACAqE;QACAE;KACD;IAED,IAAI2D,0BAA0B;QAC5B,qBACE,KAACpK;YAAcgK,OAAOA;YAAOD,aAAaA;sBACvCD;;IAGP;IAEA,OAAOA;AACT"}