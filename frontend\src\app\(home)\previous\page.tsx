"use client";

import { useState, useEffect } from "react";
import { 
  <PERSON>, 
  CardB<PERSON>, 
  <PERSON><PERSON>, 
  <PERSON>, 
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Tab
} from "@nextui-org/react";
import { Clock, MapPin, User, AlertTriangle, <PERSON>, Shield } from "lucide-react";
import { fetcher } from "@/lib/utils";
import { Modal, ModalBody, ModalTrigger } from "@/components/ui/animated-modal";
import { AlertItemDetails } from "@/components/specific/AlertItemDetails/page";

interface EmergencyNotification {
  id: number;
  emergency_type: string;
  description: string;
  location: string;
  created_at: string;
  user_first_name: string;
  user_last_name: string;
  images?: { id: number; src: string }[];
}

interface ApiResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: EmergencyNotification[];
}

const emergencyTypes = [
  { key: "all", label: "جميع الإشعارات", icon: AlertTriangle },
  { key: "O", label: "طلب مساعدة", icon: Heart },
  { key: "M", label: "طبية", icon: Shield },
  { key: "D", label: "خطر", icon: AlertTriangle },
];

const getEmergencyTypeLabel = (type: string) => {
  switch (type) {
    case "O": return "طلب مساعدة";
    case "M": return "طبية";
    case "D": return "خطر";
    default: return "غير محدد";
  }
};

const getEmergencyTypeColor = (type: string) => {
  switch (type) {
    case "O": return "primary";
    case "M": return "success";
    case "D": return "danger";
    default: return "default";
  }
};

const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return date.toLocaleDateString("ar-EG", {
    year: "numeric",
    month: "long",
    day: "numeric",
    hour: "2-digit",
    minute: "2-digit",
  });
};

export default function PreviousPage() {
  const [notifications, setNotifications] = useState<EmergencyNotification[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [selectedType, setSelectedType] = useState("all");
  const [totalCount, setTotalCount] = useState(0);

  const itemsPerPage = 12;

  const fetchNotifications = async (page: number = 1, type: string = "all") => {
    try {
      setLoading(true);
      setError(null);

      let endpoint = `/emergency/?page=${page}&page_size=${itemsPerPage}`;
      if (type !== "all") {
        endpoint += `&emergency_type=${type}`;
      }

      const response = await fetcher(endpoint, null, "GET");
      
      if (!response.ok) {
        throw new Error(`خطأ في الشبكة: ${response.status}`);
      }

      const text = await response.text();
      const data: ApiResponse = text ? JSON.parse(text) : { count: 0, results: [], next: null, previous: null };

      setNotifications(data.results || []);
      setTotalCount(data.count || 0);
      setTotalPages(Math.ceil((data.count || 0) / itemsPerPage));
    } catch (err) {
      console.error("Error fetching notifications:", err);
      setError("حدث خطأ أثناء تحميل الإشعارات. يرجى المحاولة مرة أخرى.");
      setNotifications([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchNotifications(currentPage, selectedType);
  }, [currentPage, selectedType]);

  const handleTypeChange = (type: string) => {
    setSelectedType(type);
    setCurrentPage(1);
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  if (loading && notifications.length === 0) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <Spinner size="lg" />
          <p className="mt-4 text-gray-600">جارٍ تحميل الإشعارات...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-7xl">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-center mb-2">الإشعارات السابقة</h1>
        <p className="text-gray-600 text-center">
          عرض جميع إشعارات الطوارئ المرسلة سابقاً ({totalCount} إشعار)
        </p>
      </div>

      {/* Filter Tabs */}
      <div className="mb-8">
        <Tabs
          selectedKey={selectedType}
          onSelectionChange={(key) => handleTypeChange(key as string)}
          className="w-full"
          classNames={{
            tabList: "w-full",
            tab: "flex-1",
          }}
        >
          {emergencyTypes.map((type) => {
            const Icon = type.icon;
            return (
              <Tab
                key={type.key}
                title={
                  <div className="flex items-center gap-2">
                    <Icon className="w-4 h-4" />
                    <span>{type.label}</span>
                  </div>
                }
              />
            );
          })}
        </Tabs>
      </div>

      {error && (
        <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
          <p className="text-red-600 text-center">{error}</p>
          <Button
            color="danger"
            variant="light"
            className="mt-2 mx-auto block"
            onPress={() => fetchNotifications(currentPage, selectedType)}
          >
            إعادة المحاولة
          </Button>
        </div>
      )}

      {notifications.length === 0 && !loading ? (
        <div className="text-center py-12">
          <AlertTriangle className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-gray-600 mb-2">لا توجد إشعارات</h3>
          <p className="text-gray-500">
            {selectedType === "all" 
              ? "لم يتم العثور على أي إشعارات طوارئ"
              : `لم يتم العثور على إشعارات من نوع "${getEmergencyTypeLabel(selectedType)}"`
            }
          </p>
        </div>
      ) : (
        <>
          {/* Notifications Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-8">
            {notifications.map((notification) => (
              <Modal key={notification.id}>
                <ModalTrigger>
                  <Card className="hover:shadow-lg transition-shadow cursor-pointer h-full">
                    <CardBody className="p-4">
                      <div className="flex justify-between items-start mb-3">
                        <Chip
                          color={getEmergencyTypeColor(notification.emergency_type) as any}
                          size="sm"
                          variant="flat"
                        >
                          {getEmergencyTypeLabel(notification.emergency_type)}
                        </Chip>
                        <div className="text-xs text-gray-500 flex items-center gap-1">
                          <Clock className="w-3 h-3" />
                          {formatDate(notification.created_at)}
                        </div>
                      </div>

                      <div className="space-y-2 mb-3">
                        <div className="flex items-center gap-2 text-sm">
                          <User className="w-4 h-4 text-gray-500" />
                          <span className="font-medium">
                            {notification.user_first_name} {notification.user_last_name}
                          </span>
                        </div>
                        
                        <div className="flex items-center gap-2 text-sm text-gray-600">
                          <MapPin className="w-4 h-4 text-gray-500" />
                          <span>{notification.location}</span>
                        </div>
                      </div>

                      <p className="text-sm text-gray-700 line-clamp-3">
                        {notification.description}
                      </p>

                      <div className="mt-3 pt-3 border-t border-gray-100">
                        <Button
                          size="sm"
                          color="primary"
                          variant="light"
                          className="w-full"
                        >
                          عرض التفاصيل
                        </Button>
                      </div>
                    </CardBody>
                  </Card>
                </ModalTrigger>
                <ModalBody>
                  <AlertItemDetails id={notification.id.toString()} />
                </ModalBody>
              </Modal>
            ))}
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex justify-center">
              <Pagination
                total={totalPages}
                page={currentPage}
                onChange={handlePageChange}
                showControls
                className="gap-2"
                classNames={{
                  wrapper: "gap-0 overflow-visible h-8",
                  item: "w-8 h-8 text-small rounded-none bg-transparent",
                  cursor: "bg-primary text-white font-bold",
                }}
              />
            </div>
          )}

          {loading && notifications.length > 0 && (
            <div className="flex justify-center mt-4">
              <Spinner size="sm" />
            </div>
          )}
        </>
      )}
    </div>
  );
}
