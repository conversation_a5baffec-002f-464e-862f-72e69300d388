{"version": 3, "sources": ["../../../src/client/react-client-callbacks/error-boundary-callbacks.ts"], "sourcesContent": ["// This file is only used in app router due to the specific error state handling.\n\nimport type { ErrorInfo } from 'react'\nimport { getReactStitchedError } from '../components/errors/stitched-error'\nimport { handleClientError } from '../components/errors/use-error-handler'\nimport { isNextRouterError } from '../components/is-next-router-error'\nimport { isBailoutToCSRError } from '../../shared/lib/lazy-dynamic/bailout-to-csr'\nimport { reportGlobalError } from './report-global-error'\nimport { originConsoleError } from '../components/globals/intercept-console-error'\nimport {\n  <PERSON><PERSON>r<PERSON>oundaryHandler,\n  GlobalError as DefaultErrorBoundary,\n} from '../components/error-boundary'\n\nexport function onCaughtError(\n  err: unknown,\n  errorInfo: ErrorInfo & { errorBoundary?: React.Component }\n) {\n  const errorBoundaryComponent = errorInfo.errorBoundary?.constructor\n\n  let isImplicitErrorBoundary\n\n  if (process.env.NODE_ENV !== 'production') {\n    const { AppDevOverlayErrorBoundary } =\n      require('../components/react-dev-overlay/app/app-dev-overlay-error-boundary') as typeof import('../components/react-dev-overlay/app/app-dev-overlay-error-boundary')\n\n    isImplicitErrorBoundary =\n      errorBoundaryComponent === AppDevOverlayErrorBoundary\n  }\n\n  isImplicitErrorBoundary =\n    isImplicitErrorBoundary ||\n    (errorBoundaryComponent === ErrorBoundaryHandler &&\n      (errorInfo.errorBoundary! as InstanceType<typeof ErrorBoundaryHandler>)\n        .props.errorComponent === DefaultErrorBoundary)\n\n  if (isImplicitErrorBoundary) {\n    // We don't consider errors caught unless they're caught by an explicit error\n    // boundary. The built-in ones are considered implicit.\n    // This mimics how the same app would behave without Next.js.\n    return onUncaughtError(err, errorInfo)\n  }\n\n  // Skip certain custom errors which are not expected to be reported on client\n  if (isBailoutToCSRError(err) || isNextRouterError(err)) return\n\n  if (process.env.NODE_ENV !== 'production') {\n    const errorBoundaryName =\n      // read react component displayName\n      (errorBoundaryComponent as any)?.displayName ||\n      errorBoundaryComponent?.name ||\n      'Unknown'\n\n    const componentThatErroredFrame = errorInfo?.componentStack?.split('\\n')[1]\n\n    // Match chrome or safari stack trace\n    const matches =\n      // regex to match the function name in the stack trace\n      // example 1: at Page (http://localhost:3000/_next/static/chunks/pages/index.js?ts=1631600000000:2:1)\n      // example 2: Page@http://localhost:3000/_next/static/chunks/pages/index.js?ts=1631600000000:2:1\n      componentThatErroredFrame?.match(/\\s+at (\\w+)\\s+|(\\w+)@/) ?? []\n    const componentThatErroredName = matches[1] || matches[2] || 'Unknown'\n\n    // Create error location with errored component and error boundary, to match the behavior of default React onCaughtError handler.\n    const errorBoundaryMessage = `It was handled by the <${errorBoundaryName}> error boundary.`\n    const componentErrorMessage = componentThatErroredName\n      ? `The above error occurred in the <${componentThatErroredName}> component.`\n      : `The above error occurred in one of your components.`\n\n    const errorLocation = `${componentErrorMessage} ${errorBoundaryMessage}`\n\n    const stitchedError = getReactStitchedError(err)\n    // TODO: change to passing down errorInfo later\n    // In development mode, pass along the component stack to the error\n    if (errorInfo.componentStack) {\n      ;(stitchedError as any)._componentStack = errorInfo.componentStack\n    }\n\n    // Log and report the error with location but without modifying the error stack\n    originConsoleError('%o\\n\\n%s', err, errorLocation)\n\n    handleClientError(stitchedError, [])\n  } else {\n    originConsoleError(err)\n  }\n}\n\nexport function onUncaughtError(err: unknown, errorInfo: React.ErrorInfo) {\n  // Skip certain custom errors which are not expected to be reported on client\n  if (isBailoutToCSRError(err) || isNextRouterError(err)) return\n\n  if (process.env.NODE_ENV !== 'production') {\n    const stitchedError = getReactStitchedError(err)\n    // TODO: change to passing down errorInfo later\n    // In development mode, pass along the component stack to the error\n    if (errorInfo.componentStack) {\n      ;(stitchedError as any)._componentStack = errorInfo.componentStack\n    }\n\n    // TODO: Add an adendum to the overlay telling people about custom error boundaries.\n    reportGlobalError(stitchedError)\n  } else {\n    reportGlobalError(err)\n  }\n}\n"], "names": ["onCaughtError", "onUncaughtError", "err", "errorInfo", "errorBoundaryComponent", "errorBoundary", "constructor", "isImplicitErrorBoundary", "process", "env", "NODE_ENV", "AppDevOverlayErrorBoundary", "require", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "props", "errorComponent", "DefaultErrorBoundary", "isBailoutToCSRError", "isNextRouterError", "errorBoundaryName", "displayName", "name", "componentThatErroredFrame", "componentStack", "split", "matches", "match", "componentThatErroredName", "errorBoundaryMessage", "componentErrorMessage", "errorLocation", "stitchedError", "getReactStitchedError", "_componentStack", "originConsoleError", "handleClientError", "reportGlobalError"], "mappings": "AAAA,iFAAiF;;;;;;;;;;;;;;;;IAcjEA,aAAa;eAAbA;;IAyEAC,eAAe;eAAfA;;;+BApFsB;iCACJ;mCACA;8BACE;mCACF;uCACC;+BAI5B;AAEA,SAASD,cACdE,GAAY,EACZC,SAA0D;QAE3BA;IAA/B,MAAMC,0BAAyBD,2BAAAA,UAAUE,aAAa,qBAAvBF,yBAAyBG,WAAW;IAEnE,IAAIC;IAEJ,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;QACzC,MAAM,EAAEC,0BAA0B,EAAE,GAClCC,QAAQ;QAEVL,0BACEH,2BAA2BO;IAC/B;IAEAJ,0BACEA,2BACCH,2BAA2BS,mCAAoB,IAC9C,AAACV,UAAUE,aAAa,CACrBS,KAAK,CAACC,cAAc,KAAKC,0BAAoB;IAEpD,IAAIT,yBAAyB;QAC3B,6EAA6E;QAC7E,uDAAuD;QACvD,6DAA6D;QAC7D,OAAON,gBAAgBC,KAAKC;IAC9B;IAEA,6EAA6E;IAC7E,IAAIc,IAAAA,iCAAmB,EAACf,QAAQgB,IAAAA,oCAAiB,EAAChB,MAAM;IAExD,IAAIM,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;YAOPP;QANlC,MAAMgB,oBAEJ,CADA,mCAAmC;QAClCf,0CAAD,AAACA,uBAAgCgB,WAAW,MAC5ChB,0CAAAA,uBAAwBiB,IAAI,KAC5B;QAEF,MAAMC,4BAA4BnB,8BAAAA,4BAAAA,UAAWoB,cAAc,qBAAzBpB,0BAA2BqB,KAAK,CAAC,KAAK,CAAC,EAAE;YAIzE,sDAAsD;QACtD,qGAAqG;QACrG,gGAAgG;QAChGF;QALF,qCAAqC;QACrC,MAAMG,UAIJH,CAAAA,mCAAAA,6CAAAA,0BAA2BI,KAAK,CAAC,oCAAjCJ,mCAA6D,EAAE;QACjE,MAAMK,2BAA2BF,OAAO,CAAC,EAAE,IAAIA,OAAO,CAAC,EAAE,IAAI;QAE7D,iIAAiI;QACjI,MAAMG,uBAAuB,AAAC,4BAAyBT,oBAAkB;QACzE,MAAMU,wBAAwBF,2BAC1B,AAAC,sCAAmCA,2BAAyB,iBAC5D;QAEL,MAAMG,gBAAgB,AAAGD,wBAAsB,MAAGD;QAElD,MAAMG,gBAAgBC,IAAAA,oCAAqB,EAAC9B;QAC5C,+CAA+C;QAC/C,mEAAmE;QACnE,IAAIC,UAAUoB,cAAc,EAAE;;YAC1BQ,cAAsBE,eAAe,GAAG9B,UAAUoB,cAAc;QACpE;QAEA,+EAA+E;QAC/EW,IAAAA,yCAAkB,EAAC,YAAYhC,KAAK4B;QAEpCK,IAAAA,kCAAiB,EAACJ,eAAe,EAAE;IACrC,OAAO;QACLG,IAAAA,yCAAkB,EAAChC;IACrB;AACF;AAEO,SAASD,gBAAgBC,GAAY,EAAEC,SAA0B;IACtE,6EAA6E;IAC7E,IAAIc,IAAAA,iCAAmB,EAACf,QAAQgB,IAAAA,oCAAiB,EAAChB,MAAM;IAExD,IAAIM,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;QACzC,MAAMqB,gBAAgBC,IAAAA,oCAAqB,EAAC9B;QAC5C,+CAA+C;QAC/C,mEAAmE;QACnE,IAAIC,UAAUoB,cAAc,EAAE;;YAC1BQ,cAAsBE,eAAe,GAAG9B,UAAUoB,cAAc;QACpE;QAEA,oFAAoF;QACpFa,IAAAA,oCAAiB,EAACL;IACpB,OAAO;QACLK,IAAAA,oCAAiB,EAAClC;IACpB;AACF"}