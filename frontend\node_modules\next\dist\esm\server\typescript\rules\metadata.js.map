{"version": 3, "sources": ["../../../../src/server/typescript/rules/metadata.ts"], "sourcesContent": ["import { NEXT_TS_ERRORS } from '../constant'\nimport {\n  getInfo,\n  getSource,\n  getTs,\n  getT<PERSON><PERSON><PERSON><PERSON>,\n  isPositionInsideNode,\n} from '../utils'\n\nimport type tsModule from 'typescript/lib/tsserverlibrary'\n\nconst TYPE_ANNOTATION = ': Metadata | null'\nconst TYPE_ANNOTATION_ASYNC = ': Promise<Metadata | null>'\nconst TYPE_IMPORT = `\\n\\nimport type { Metadata } from 'next'`\n\n// Find the `export const metadata = ...` node.\nfunction getMetadataExport(fileName: string, position: number) {\n  const source = getSource(fileName)\n  let metadataExport: tsModule.VariableDeclaration | undefined\n\n  if (source) {\n    const ts = getTs()\n    ts.forEachChild(source, function visit(node) {\n      if (metadataExport) return\n\n      // Covered by this node\n      if (isPositionInsideNode(position, node)) {\n        // Export variable\n        if (\n          ts.isVariableStatement(node) &&\n          node.modifiers?.some((m) => m.kind === ts.SyntaxKind.ExportKeyword)\n        ) {\n          if (ts.isVariableDeclarationList(node.declarationList)) {\n            for (const declaration of node.declarationList.declarations) {\n              if (\n                isPositionInsideNode(position, declaration) &&\n                declaration.name.getText() === 'metadata'\n              ) {\n                // `export const metadata = ...`\n                metadataExport = declaration\n                return\n              }\n            }\n          }\n        }\n      }\n    })\n  }\n  return metadataExport\n}\n\nlet cachedProxiedLanguageService: tsModule.LanguageService | undefined\nlet cachedProxiedLanguageServiceHost: tsModule.LanguageServiceHost | undefined\nfunction getProxiedLanguageService() {\n  if (cachedProxiedLanguageService)\n    return {\n      languageService: cachedProxiedLanguageService as tsModule.LanguageService,\n      languageServiceHost:\n        cachedProxiedLanguageServiceHost as tsModule.LanguageServiceHost & {\n          addFile: (fileName: string, body: string) => void\n        },\n    }\n\n  const languageServiceHost = getInfo().languageServiceHost\n\n  const ts = getTs()\n  class ProxiedLanguageServiceHost implements tsModule.LanguageServiceHost {\n    files: {\n      [fileName: string]: { file: tsModule.IScriptSnapshot; ver: number }\n    } = {}\n\n    log = () => {}\n    trace = () => {}\n    error = () => {}\n    getCompilationSettings = () => languageServiceHost.getCompilationSettings()\n    getScriptIsOpen = () => true\n    getCurrentDirectory = () => languageServiceHost.getCurrentDirectory()\n    getDefaultLibFileName = (o: any) =>\n      languageServiceHost.getDefaultLibFileName(o)\n\n    getScriptVersion = (fileName: string) => {\n      const file = this.files[fileName]\n      if (!file) return languageServiceHost.getScriptVersion(fileName)\n      return file.ver.toString()\n    }\n\n    getScriptSnapshot = (fileName: string) => {\n      const file = this.files[fileName]\n      if (!file) return languageServiceHost.getScriptSnapshot(fileName)\n      return file.file\n    }\n\n    getScriptFileNames(): string[] {\n      const names: Set<string> = new Set()\n      for (var name in this.files) {\n        if (this.files.hasOwnProperty(name)) {\n          names.add(name)\n        }\n      }\n      const files = languageServiceHost.getScriptFileNames()\n      for (const file of files) {\n        names.add(file)\n      }\n      return [...names]\n    }\n\n    addFile(fileName: string, body: string) {\n      const snap = ts.ScriptSnapshot.fromString(body)\n      snap.getChangeRange = (_) => undefined\n      const existing = this.files[fileName]\n      if (existing) {\n        this.files[fileName].ver++\n        this.files[fileName].file = snap\n      } else {\n        this.files[fileName] = { ver: 1, file: snap }\n      }\n    }\n\n    readFile(fileName: string) {\n      const file = this.files[fileName]\n      return file\n        ? file.file.getText(0, file.file.getLength())\n        : languageServiceHost.readFile(fileName)\n    }\n    fileExists(fileName: string) {\n      return (\n        this.files[fileName] !== undefined ||\n        languageServiceHost.fileExists(fileName)\n      )\n    }\n  }\n\n  cachedProxiedLanguageServiceHost = new ProxiedLanguageServiceHost()\n  cachedProxiedLanguageService = ts.createLanguageService(\n    cachedProxiedLanguageServiceHost,\n    ts.createDocumentRegistry()\n  )\n  return {\n    languageService: cachedProxiedLanguageService as tsModule.LanguageService,\n    languageServiceHost:\n      cachedProxiedLanguageServiceHost as tsModule.LanguageServiceHost & {\n        addFile: (fileName: string, body: string) => void\n      },\n  }\n}\n\nfunction updateVirtualFileWithType(\n  fileName: string,\n  node: tsModule.VariableDeclaration | tsModule.FunctionDeclaration,\n  isGenerateMetadata?: boolean\n) {\n  const source = getSource(fileName)\n  if (!source) return\n\n  // We annotate with the type in a virtual language service\n  const sourceText = source.getFullText()\n  let nodeEnd: number\n  let annotation: string\n\n  const ts = getTs()\n  if (ts.isFunctionDeclaration(node)) {\n    if (isGenerateMetadata) {\n      nodeEnd = node.body!.getFullStart()\n      const isAsync = node.modifiers?.some(\n        (m) => m.kind === ts.SyntaxKind.AsyncKeyword\n      )\n      annotation = isAsync ? TYPE_ANNOTATION_ASYNC : TYPE_ANNOTATION\n    } else {\n      return\n    }\n  } else {\n    nodeEnd = node.name.getFullStart() + node.name.getFullWidth()\n    annotation = TYPE_ANNOTATION\n  }\n\n  const newSource =\n    sourceText.slice(0, nodeEnd) +\n    annotation +\n    sourceText.slice(nodeEnd) +\n    TYPE_IMPORT\n  const { languageServiceHost } = getProxiedLanguageService()\n  languageServiceHost.addFile(fileName, newSource)\n\n  return [nodeEnd, annotation.length]\n}\n\nfunction isTyped(\n  node: tsModule.VariableDeclaration | tsModule.FunctionDeclaration\n) {\n  return node.type !== undefined\n}\n\nfunction proxyDiagnostics(\n  fileName: string,\n  pos: number[],\n  n: tsModule.VariableDeclaration | tsModule.FunctionDeclaration\n) {\n  // Get diagnostics\n  const { languageService } = getProxiedLanguageService()\n  const diagnostics = languageService.getSemanticDiagnostics(fileName)\n  const source = getSource(fileName)\n\n  // Filter and map the results\n  return diagnostics\n    .filter((d) => {\n      if (d.start === undefined || d.length === undefined) return false\n      if (d.start < n.getFullStart()) return false\n      if (d.start + d.length >= n.getFullStart() + n.getFullWidth() + pos[1])\n        return false\n      return true\n    })\n    .map((d) => {\n      return {\n        file: source,\n        category: d.category,\n        code: d.code,\n        messageText: d.messageText,\n        start: d.start! < pos[0] ? d.start : d.start! - pos[1],\n        length: d.length,\n      }\n    })\n}\n\nconst metadata = {\n  filterCompletionsAtPosition(\n    fileName: string,\n    position: number,\n    _options: any,\n    prior: tsModule.WithMetadata<tsModule.CompletionInfo>\n  ) {\n    const node = getMetadataExport(fileName, position)\n    if (!node) return prior\n    if (isTyped(node)) return prior\n\n    const ts = getTs()\n\n    // We annotate with the type in a virtual language service\n    const pos = updateVirtualFileWithType(fileName, node)\n    if (pos === undefined) return prior\n\n    // Get completions\n    const { languageService } = getProxiedLanguageService()\n    const newPos = position <= pos[0] ? position : position + pos[1]\n    const completions = languageService.getCompletionsAtPosition(\n      fileName,\n      newPos,\n      undefined\n    )\n\n    if (completions) {\n      completions.isIncomplete = true\n\n      completions.entries = completions.entries\n        .filter((e) => {\n          return [\n            ts.ScriptElementKind.memberVariableElement,\n            ts.ScriptElementKind.typeElement,\n            ts.ScriptElementKind.string,\n          ].includes(e.kind)\n        })\n        .map((e) => {\n          const insertText =\n            e.kind === ts.ScriptElementKind.memberVariableElement &&\n            /^[a-zA-Z0-9_]+$/.test(e.name)\n              ? e.name + ': '\n              : e.name\n\n          return {\n            name: e.name,\n            insertText,\n            kind: e.kind,\n            kindModifiers: e.kindModifiers,\n            sortText: '!' + e.name,\n            labelDetails: {\n              description: `Next.js metadata`,\n            },\n            data: e.data,\n          }\n        })\n\n      return completions\n    }\n\n    return prior\n  },\n\n  getSemanticDiagnosticsForExportVariableStatementInClientEntry(\n    fileName: string,\n    node: tsModule.VariableStatement | tsModule.FunctionDeclaration\n  ) {\n    const source = getSource(fileName)\n    const ts = getTs()\n\n    // It is not allowed to export `metadata` or `generateMetadata` in client entry\n    if (ts.isFunctionDeclaration(node)) {\n      if (node.name?.getText() === 'generateMetadata') {\n        return [\n          {\n            file: source,\n            category: ts.DiagnosticCategory.Error,\n            code: NEXT_TS_ERRORS.INVALID_METADATA_EXPORT,\n            messageText: `The Next.js 'generateMetadata' API is not allowed in a client component.`,\n            start: node.name.getStart(),\n            length: node.name.getWidth(),\n          },\n        ]\n      }\n    } else {\n      for (const declaration of node.declarationList.declarations) {\n        const name = declaration.name.getText()\n        if (name === 'metadata') {\n          return [\n            {\n              file: source,\n              category: ts.DiagnosticCategory.Error,\n              code: NEXT_TS_ERRORS.INVALID_METADATA_EXPORT,\n              messageText: `The Next.js 'metadata' API is not allowed in a client component.`,\n              start: declaration.name.getStart(),\n              length: declaration.name.getWidth(),\n            },\n          ]\n        }\n      }\n    }\n    return []\n  },\n\n  getSemanticDiagnosticsForExportVariableStatement(\n    fileName: string,\n    node: tsModule.VariableStatement | tsModule.FunctionDeclaration\n  ) {\n    const ts = getTs()\n\n    if (ts.isFunctionDeclaration(node)) {\n      if (node.name?.getText() === 'generateMetadata') {\n        if (isTyped(node)) return []\n\n        // We annotate with the type in a virtual language service\n        const pos = updateVirtualFileWithType(fileName, node, true)\n        if (!pos) return []\n\n        return proxyDiagnostics(fileName, pos, node)\n      }\n    } else {\n      for (const declaration of node.declarationList.declarations) {\n        if (declaration.name.getText() === 'metadata') {\n          if (isTyped(declaration)) break\n\n          // We annotate with the type in a virtual language service\n          const pos = updateVirtualFileWithType(fileName, declaration)\n          if (!pos) break\n\n          return proxyDiagnostics(fileName, pos, declaration)\n        }\n      }\n    }\n    return []\n  },\n\n  getSemanticDiagnosticsForExportDeclarationInClientEntry(\n    fileName: string,\n    node: tsModule.ExportDeclaration\n  ) {\n    const ts = getTs()\n    const source = getSource(fileName)\n    const diagnostics: tsModule.Diagnostic[] = []\n\n    const exportClause = node.exportClause\n    if (exportClause && ts.isNamedExports(exportClause)) {\n      for (const e of exportClause.elements) {\n        if (['generateMetadata', 'metadata'].includes(e.name.getText())) {\n          diagnostics.push({\n            file: source,\n            category: ts.DiagnosticCategory.Error,\n            code: NEXT_TS_ERRORS.INVALID_METADATA_EXPORT,\n            messageText: `The Next.js '${e.name.getText()}' API is not allowed in a client component.`,\n            start: e.name.getStart(),\n            length: e.name.getWidth(),\n          })\n        }\n      }\n    }\n\n    return diagnostics\n  },\n\n  getSemanticDiagnosticsForExportDeclaration(\n    fileName: string,\n    node: tsModule.ExportDeclaration\n  ) {\n    const ts = getTs()\n\n    const exportClause = node.exportClause\n    if (exportClause && ts.isNamedExports(exportClause)) {\n      for (const e of exportClause.elements) {\n        if (e.name.getText() === 'metadata') {\n          // Get the original declaration node of element\n          const typeChecker = getTypeChecker()\n          if (typeChecker) {\n            const symbol = typeChecker.getSymbolAtLocation(e.name)\n            if (symbol) {\n              const metadataSymbol = typeChecker.getAliasedSymbol(symbol)\n              if (metadataSymbol && metadataSymbol.declarations) {\n                const declaration = metadataSymbol.declarations[0]\n                if (declaration && ts.isVariableDeclaration(declaration)) {\n                  if (isTyped(declaration)) break\n\n                  const declarationFileName =\n                    declaration.getSourceFile().fileName\n                  const isSameFile = declarationFileName === fileName\n\n                  // We annotate with the type in a virtual language service\n                  const pos = updateVirtualFileWithType(\n                    declarationFileName,\n                    declaration\n                  )\n                  if (!pos) break\n\n                  const diagnostics = proxyDiagnostics(\n                    declarationFileName,\n                    pos,\n                    declaration\n                  )\n                  if (diagnostics.length) {\n                    if (isSameFile) {\n                      return diagnostics\n                    } else {\n                      return [\n                        {\n                          file: getSource(fileName),\n                          category: ts.DiagnosticCategory.Error,\n                          code: NEXT_TS_ERRORS.INVALID_METADATA_EXPORT,\n                          messageText: `The 'metadata' export value is not typed correctly, please make sure it is typed as 'Metadata':\\nhttps://nextjs.org/docs/app/building-your-application/optimizing/metadata#static-metadata`,\n                          start: e.name.getStart(),\n                          length: e.name.getWidth(),\n                        },\n                      ]\n                    }\n                  }\n                }\n              }\n            }\n          }\n        }\n      }\n    }\n\n    return []\n  },\n\n  getCompletionEntryDetails(\n    fileName: string,\n    position: number,\n    entryName: string,\n    formatOptions: tsModule.FormatCodeOptions,\n    source: string,\n    preferences: tsModule.UserPreferences,\n    data: tsModule.CompletionEntryData\n  ) {\n    const node = getMetadataExport(fileName, position)\n    if (!node) return\n    if (isTyped(node)) return\n\n    // We annotate with the type in a virtual language service\n    const pos = updateVirtualFileWithType(fileName, node)\n    if (pos === undefined) return\n\n    const { languageService } = getProxiedLanguageService()\n    const newPos = position <= pos[0] ? position : position + pos[1]\n\n    const details = languageService.getCompletionEntryDetails(\n      fileName,\n      newPos,\n      entryName,\n      formatOptions,\n      source,\n      preferences,\n      data\n    )\n    return details\n  },\n\n  getQuickInfoAtPosition(fileName: string, position: number) {\n    const node = getMetadataExport(fileName, position)\n    if (!node) return\n    if (isTyped(node)) return\n\n    // We annotate with the type in a virtual language service\n    const pos = updateVirtualFileWithType(fileName, node)\n    if (pos === undefined) return\n\n    const { languageService } = getProxiedLanguageService()\n    const newPos = position <= pos[0] ? position : position + pos[1]\n    const insight = languageService.getQuickInfoAtPosition(fileName, newPos)\n    return insight\n  },\n\n  getDefinitionAndBoundSpan(fileName: string, position: number) {\n    const node = getMetadataExport(fileName, position)\n    if (!node) return\n    if (isTyped(node)) return\n    if (!isPositionInsideNode(position, node)) return\n    // We annotate with the type in a virtual language service\n    const pos = updateVirtualFileWithType(fileName, node)\n    if (pos === undefined) return\n    const { languageService } = getProxiedLanguageService()\n    const newPos = position <= pos[0] ? position : position + pos[1]\n\n    const definitionInfoAndBoundSpan =\n      languageService.getDefinitionAndBoundSpan(fileName, newPos)\n\n    if (definitionInfoAndBoundSpan) {\n      // Adjust the start position of the text span\n      if (definitionInfoAndBoundSpan.textSpan.start > pos[0]) {\n        definitionInfoAndBoundSpan.textSpan.start -= pos[1]\n      }\n    }\n    return definitionInfoAndBoundSpan\n  },\n}\n\nexport default metadata\n"], "names": ["NEXT_TS_ERRORS", "getInfo", "getSource", "getTs", "getType<PERSON><PERSON>cker", "isPositionInsideNode", "TYPE_ANNOTATION", "TYPE_ANNOTATION_ASYNC", "TYPE_IMPORT", "getMetadataExport", "fileName", "position", "source", "metadataExport", "ts", "for<PERSON><PERSON><PERSON><PERSON><PERSON>", "visit", "node", "isVariableStatement", "modifiers", "some", "m", "kind", "SyntaxKind", "ExportKeyword", "isVariableDeclarationList", "declarationList", "declaration", "declarations", "name", "getText", "cachedProxiedLanguageService", "cachedProxiedLanguageServiceHost", "getProxiedLanguageService", "languageService", "languageServiceHost", "ProxiedLanguageServiceHost", "getScriptFileNames", "names", "Set", "files", "hasOwnProperty", "add", "file", "addFile", "body", "snap", "ScriptSnapshot", "fromString", "getChangeRange", "_", "undefined", "existing", "ver", "readFile", "<PERSON><PERSON><PERSON><PERSON>", "fileExists", "log", "trace", "error", "getCompilationSettings", "getScriptIsOpen", "getCurrentDirectory", "getDefaultLibFileName", "o", "getScriptVersion", "toString", "getScriptSnapshot", "createLanguageService", "createDocumentRegistry", "updateVirtualFileWithType", "isGenerateMetadata", "sourceText", "getFullText", "nodeEnd", "annotation", "isFunctionDeclaration", "getFullStart", "isAsync", "AsyncKeyword", "get<PERSON><PERSON><PERSON><PERSON><PERSON>", "newSource", "slice", "length", "isTyped", "type", "proxyDiagnostics", "pos", "n", "diagnostics", "getSemanticDiagnostics", "filter", "d", "start", "map", "category", "code", "messageText", "metadata", "filterCompletionsAtPosition", "_options", "prior", "newPos", "completions", "getCompletionsAtPosition", "isIncomplete", "entries", "e", "ScriptElementKind", "memberVariableElement", "typeElement", "string", "includes", "insertText", "test", "kindModifiers", "sortText", "labelDetails", "description", "data", "getSemanticDiagnosticsForExportVariableStatementInClientEntry", "DiagnosticCategory", "Error", "INVALID_METADATA_EXPORT", "getStart", "getWidth", "getSemanticDiagnosticsForExportVariableStatement", "getSemanticDiagnosticsForExportDeclarationInClientEntry", "exportClause", "isNamedExports", "elements", "push", "getSemanticDiagnosticsForExportDeclaration", "typeC<PERSON>cker", "symbol", "getSymbolAtLocation", "metadataSymbol", "getAliasedSymbol", "isVariableDeclaration", "declarationFileName", "getSourceFile", "isSameFile", "getCompletionEntryDetails", "entryName", "formatOptions", "preferences", "details", "getQuickInfoAtPosition", "insight", "getDefinitionAndBoundSpan", "definitionInfoAndBoundSpan", "textSpan"], "mappings": "AAAA,SAASA,cAAc,QAAQ,cAAa;AAC5C,SACEC,OAAO,EACPC,SAAS,EACTC,KAAK,EACLC,cAAc,EACdC,oBAAoB,QACf,WAAU;AAIjB,MAAMC,kBAAkB;AACxB,MAAMC,wBAAwB;AAC9B,MAAMC,cAAc,CAAC,wCAAwC,CAAC;AAE9D,+CAA+C;AAC/C,SAASC,kBAAkBC,QAAgB,EAAEC,QAAgB;IAC3D,MAAMC,SAASV,UAAUQ;IACzB,IAAIG;IAEJ,IAAID,QAAQ;QACV,MAAME,KAAKX;QACXW,GAAGC,YAAY,CAACH,QAAQ,SAASI,MAAMC,IAAI;YACzC,IAAIJ,gBAAgB;YAEpB,uBAAuB;YACvB,IAAIR,qBAAqBM,UAAUM,OAAO;oBAItCA;gBAHF,kBAAkB;gBAClB,IACEH,GAAGI,mBAAmB,CAACD,WACvBA,kBAAAA,KAAKE,SAAS,qBAAdF,gBAAgBG,IAAI,CAAC,CAACC,IAAMA,EAAEC,IAAI,KAAKR,GAAGS,UAAU,CAACC,aAAa,IAClE;oBACA,IAAIV,GAAGW,yBAAyB,CAACR,KAAKS,eAAe,GAAG;wBACtD,KAAK,MAAMC,eAAeV,KAAKS,eAAe,CAACE,YAAY,CAAE;4BAC3D,IACEvB,qBAAqBM,UAAUgB,gBAC/BA,YAAYE,IAAI,CAACC,OAAO,OAAO,YAC/B;gCACA,gCAAgC;gCAChCjB,iBAAiBc;gCACjB;4BACF;wBACF;oBACF;gBACF;YACF;QACF;IACF;IACA,OAAOd;AACT;AAEA,IAAIkB;AACJ,IAAIC;AACJ,SAASC;IACP,IAAIF,8BACF,OAAO;QACLG,iBAAiBH;QACjBI,qBACEH;IAGJ;IAEF,MAAMG,sBAAsBlC,UAAUkC,mBAAmB;IAEzD,MAAMrB,KAAKX;IACX,MAAMiC;QA0BJC,qBAA+B;YAC7B,MAAMC,QAAqB,IAAIC;YAC/B,IAAK,IAAIV,QAAQ,IAAI,CAACW,KAAK,CAAE;gBAC3B,IAAI,IAAI,CAACA,KAAK,CAACC,cAAc,CAACZ,OAAO;oBACnCS,MAAMI,GAAG,CAACb;gBACZ;YACF;YACA,MAAMW,QAAQL,oBAAoBE,kBAAkB;YACpD,KAAK,MAAMM,QAAQH,MAAO;gBACxBF,MAAMI,GAAG,CAACC;YACZ;YACA,OAAO;mBAAIL;aAAM;QACnB;QAEAM,QAAQlC,QAAgB,EAAEmC,IAAY,EAAE;YACtC,MAAMC,OAAOhC,GAAGiC,cAAc,CAACC,UAAU,CAACH;YAC1CC,KAAKG,cAAc,GAAG,CAACC,IAAMC;YAC7B,MAAMC,WAAW,IAAI,CAACZ,KAAK,CAAC9B,SAAS;YACrC,IAAI0C,UAAU;gBACZ,IAAI,CAACZ,KAAK,CAAC9B,SAAS,CAAC2C,GAAG;gBACxB,IAAI,CAACb,KAAK,CAAC9B,SAAS,CAACiC,IAAI,GAAGG;YAC9B,OAAO;gBACL,IAAI,CAACN,KAAK,CAAC9B,SAAS,GAAG;oBAAE2C,KAAK;oBAAGV,MAAMG;gBAAK;YAC9C;QACF;QAEAQ,SAAS5C,QAAgB,EAAE;YACzB,MAAMiC,OAAO,IAAI,CAACH,KAAK,CAAC9B,SAAS;YACjC,OAAOiC,OACHA,KAAKA,IAAI,CAACb,OAAO,CAAC,GAAGa,KAAKA,IAAI,CAACY,SAAS,MACxCpB,oBAAoBmB,QAAQ,CAAC5C;QACnC;QACA8C,WAAW9C,QAAgB,EAAE;YAC3B,OACE,IAAI,CAAC8B,KAAK,CAAC9B,SAAS,KAAKyC,aACzBhB,oBAAoBqB,UAAU,CAAC9C;QAEnC;;iBA9DA8B,QAEI,CAAC;iBAELiB,MAAM,KAAO;iBACbC,QAAQ,KAAO;iBACfC,QAAQ,KAAO;iBACfC,yBAAyB,IAAMzB,oBAAoByB,sBAAsB;iBACzEC,kBAAkB,IAAM;iBACxBC,sBAAsB,IAAM3B,oBAAoB2B,mBAAmB;iBACnEC,wBAAwB,CAACC,IACvB7B,oBAAoB4B,qBAAqB,CAACC;iBAE5CC,mBAAmB,CAACvD;gBAClB,MAAMiC,OAAO,IAAI,CAACH,KAAK,CAAC9B,SAAS;gBACjC,IAAI,CAACiC,MAAM,OAAOR,oBAAoB8B,gBAAgB,CAACvD;gBACvD,OAAOiC,KAAKU,GAAG,CAACa,QAAQ;YAC1B;iBAEAC,oBAAoB,CAACzD;gBACnB,MAAMiC,OAAO,IAAI,CAACH,KAAK,CAAC9B,SAAS;gBACjC,IAAI,CAACiC,MAAM,OAAOR,oBAAoBgC,iBAAiB,CAACzD;gBACxD,OAAOiC,KAAKA,IAAI;YAClB;;IAwCF;IAEAX,mCAAmC,IAAII;IACvCL,+BAA+BjB,GAAGsD,qBAAqB,CACrDpC,kCACAlB,GAAGuD,sBAAsB;IAE3B,OAAO;QACLnC,iBAAiBH;QACjBI,qBACEH;IAGJ;AACF;AAEA,SAASsC,0BACP5D,QAAgB,EAChBO,IAAiE,EACjEsD,kBAA4B;IAE5B,MAAM3D,SAASV,UAAUQ;IACzB,IAAI,CAACE,QAAQ;IAEb,0DAA0D;IAC1D,MAAM4D,aAAa5D,OAAO6D,WAAW;IACrC,IAAIC;IACJ,IAAIC;IAEJ,MAAM7D,KAAKX;IACX,IAAIW,GAAG8D,qBAAqB,CAAC3D,OAAO;QAClC,IAAIsD,oBAAoB;gBAENtD;YADhByD,UAAUzD,KAAK4B,IAAI,CAAEgC,YAAY;YACjC,MAAMC,WAAU7D,kBAAAA,KAAKE,SAAS,qBAAdF,gBAAgBG,IAAI,CAClC,CAACC,IAAMA,EAAEC,IAAI,KAAKR,GAAGS,UAAU,CAACwD,YAAY;YAE9CJ,aAAaG,UAAUvE,wBAAwBD;QACjD,OAAO;YACL;QACF;IACF,OAAO;QACLoE,UAAUzD,KAAKY,IAAI,CAACgD,YAAY,KAAK5D,KAAKY,IAAI,CAACmD,YAAY;QAC3DL,aAAarE;IACf;IAEA,MAAM2E,YACJT,WAAWU,KAAK,CAAC,GAAGR,WACpBC,aACAH,WAAWU,KAAK,CAACR,WACjBlE;IACF,MAAM,EAAE2B,mBAAmB,EAAE,GAAGF;IAChCE,oBAAoBS,OAAO,CAAClC,UAAUuE;IAEtC,OAAO;QAACP;QAASC,WAAWQ,MAAM;KAAC;AACrC;AAEA,SAASC,QACPnE,IAAiE;IAEjE,OAAOA,KAAKoE,IAAI,KAAKlC;AACvB;AAEA,SAASmC,iBACP5E,QAAgB,EAChB6E,GAAa,EACbC,CAA8D;IAE9D,kBAAkB;IAClB,MAAM,EAAEtD,eAAe,EAAE,GAAGD;IAC5B,MAAMwD,cAAcvD,gBAAgBwD,sBAAsB,CAAChF;IAC3D,MAAME,SAASV,UAAUQ;IAEzB,6BAA6B;IAC7B,OAAO+E,YACJE,MAAM,CAAC,CAACC;QACP,IAAIA,EAAEC,KAAK,KAAK1C,aAAayC,EAAET,MAAM,KAAKhC,WAAW,OAAO;QAC5D,IAAIyC,EAAEC,KAAK,GAAGL,EAAEX,YAAY,IAAI,OAAO;QACvC,IAAIe,EAAEC,KAAK,GAAGD,EAAET,MAAM,IAAIK,EAAEX,YAAY,KAAKW,EAAER,YAAY,KAAKO,GAAG,CAAC,EAAE,EACpE,OAAO;QACT,OAAO;IACT,GACCO,GAAG,CAAC,CAACF;QACJ,OAAO;YACLjD,MAAM/B;YACNmF,UAAUH,EAAEG,QAAQ;YACpBC,MAAMJ,EAAEI,IAAI;YACZC,aAAaL,EAAEK,WAAW;YAC1BJ,OAAOD,EAAEC,KAAK,GAAIN,GAAG,CAAC,EAAE,GAAGK,EAAEC,KAAK,GAAGD,EAAEC,KAAK,GAAIN,GAAG,CAAC,EAAE;YACtDJ,QAAQS,EAAET,MAAM;QAClB;IACF;AACJ;AAEA,MAAMe,WAAW;IACfC,6BACEzF,QAAgB,EAChBC,QAAgB,EAChByF,QAAa,EACbC,KAAqD;QAErD,MAAMpF,OAAOR,kBAAkBC,UAAUC;QACzC,IAAI,CAACM,MAAM,OAAOoF;QAClB,IAAIjB,QAAQnE,OAAO,OAAOoF;QAE1B,MAAMvF,KAAKX;QAEX,0DAA0D;QAC1D,MAAMoF,MAAMjB,0BAA0B5D,UAAUO;QAChD,IAAIsE,QAAQpC,WAAW,OAAOkD;QAE9B,kBAAkB;QAClB,MAAM,EAAEnE,eAAe,EAAE,GAAGD;QAC5B,MAAMqE,SAAS3F,YAAY4E,GAAG,CAAC,EAAE,GAAG5E,WAAWA,WAAW4E,GAAG,CAAC,EAAE;QAChE,MAAMgB,cAAcrE,gBAAgBsE,wBAAwB,CAC1D9F,UACA4F,QACAnD;QAGF,IAAIoD,aAAa;YACfA,YAAYE,YAAY,GAAG;YAE3BF,YAAYG,OAAO,GAAGH,YAAYG,OAAO,CACtCf,MAAM,CAAC,CAACgB;gBACP,OAAO;oBACL7F,GAAG8F,iBAAiB,CAACC,qBAAqB;oBAC1C/F,GAAG8F,iBAAiB,CAACE,WAAW;oBAChChG,GAAG8F,iBAAiB,CAACG,MAAM;iBAC5B,CAACC,QAAQ,CAACL,EAAErF,IAAI;YACnB,GACCwE,GAAG,CAAC,CAACa;gBACJ,MAAMM,aACJN,EAAErF,IAAI,KAAKR,GAAG8F,iBAAiB,CAACC,qBAAqB,IACrD,kBAAkBK,IAAI,CAACP,EAAE9E,IAAI,IACzB8E,EAAE9E,IAAI,GAAG,OACT8E,EAAE9E,IAAI;gBAEZ,OAAO;oBACLA,MAAM8E,EAAE9E,IAAI;oBACZoF;oBACA3F,MAAMqF,EAAErF,IAAI;oBACZ6F,eAAeR,EAAEQ,aAAa;oBAC9BC,UAAU,MAAMT,EAAE9E,IAAI;oBACtBwF,cAAc;wBACZC,aAAa,CAAC,gBAAgB,CAAC;oBACjC;oBACAC,MAAMZ,EAAEY,IAAI;gBACd;YACF;YAEF,OAAOhB;QACT;QAEA,OAAOF;IACT;IAEAmB,+DACE9G,QAAgB,EAChBO,IAA+D;QAE/D,MAAML,SAASV,UAAUQ;QACzB,MAAMI,KAAKX;QAEX,+EAA+E;QAC/E,IAAIW,GAAG8D,qBAAqB,CAAC3D,OAAO;gBAC9BA;YAAJ,IAAIA,EAAAA,aAAAA,KAAKY,IAAI,qBAATZ,WAAWa,OAAO,QAAO,oBAAoB;gBAC/C,OAAO;oBACL;wBACEa,MAAM/B;wBACNmF,UAAUjF,GAAG2G,kBAAkB,CAACC,KAAK;wBACrC1B,MAAMhG,eAAe2H,uBAAuB;wBAC5C1B,aAAa,CAAC,wEAAwE,CAAC;wBACvFJ,OAAO5E,KAAKY,IAAI,CAAC+F,QAAQ;wBACzBzC,QAAQlE,KAAKY,IAAI,CAACgG,QAAQ;oBAC5B;iBACD;YACH;QACF,OAAO;YACL,KAAK,MAAMlG,eAAeV,KAAKS,eAAe,CAACE,YAAY,CAAE;gBAC3D,MAAMC,OAAOF,YAAYE,IAAI,CAACC,OAAO;gBACrC,IAAID,SAAS,YAAY;oBACvB,OAAO;wBACL;4BACEc,MAAM/B;4BACNmF,UAAUjF,GAAG2G,kBAAkB,CAACC,KAAK;4BACrC1B,MAAMhG,eAAe2H,uBAAuB;4BAC5C1B,aAAa,CAAC,gEAAgE,CAAC;4BAC/EJ,OAAOlE,YAAYE,IAAI,CAAC+F,QAAQ;4BAChCzC,QAAQxD,YAAYE,IAAI,CAACgG,QAAQ;wBACnC;qBACD;gBACH;YACF;QACF;QACA,OAAO,EAAE;IACX;IAEAC,kDACEpH,QAAgB,EAChBO,IAA+D;QAE/D,MAAMH,KAAKX;QAEX,IAAIW,GAAG8D,qBAAqB,CAAC3D,OAAO;gBAC9BA;YAAJ,IAAIA,EAAAA,aAAAA,KAAKY,IAAI,qBAATZ,WAAWa,OAAO,QAAO,oBAAoB;gBAC/C,IAAIsD,QAAQnE,OAAO,OAAO,EAAE;gBAE5B,0DAA0D;gBAC1D,MAAMsE,MAAMjB,0BAA0B5D,UAAUO,MAAM;gBACtD,IAAI,CAACsE,KAAK,OAAO,EAAE;gBAEnB,OAAOD,iBAAiB5E,UAAU6E,KAAKtE;YACzC;QACF,OAAO;YACL,KAAK,MAAMU,eAAeV,KAAKS,eAAe,CAACE,YAAY,CAAE;gBAC3D,IAAID,YAAYE,IAAI,CAACC,OAAO,OAAO,YAAY;oBAC7C,IAAIsD,QAAQzD,cAAc;oBAE1B,0DAA0D;oBAC1D,MAAM4D,MAAMjB,0BAA0B5D,UAAUiB;oBAChD,IAAI,CAAC4D,KAAK;oBAEV,OAAOD,iBAAiB5E,UAAU6E,KAAK5D;gBACzC;YACF;QACF;QACA,OAAO,EAAE;IACX;IAEAoG,yDACErH,QAAgB,EAChBO,IAAgC;QAEhC,MAAMH,KAAKX;QACX,MAAMS,SAASV,UAAUQ;QACzB,MAAM+E,cAAqC,EAAE;QAE7C,MAAMuC,eAAe/G,KAAK+G,YAAY;QACtC,IAAIA,gBAAgBlH,GAAGmH,cAAc,CAACD,eAAe;YACnD,KAAK,MAAMrB,KAAKqB,aAAaE,QAAQ,CAAE;gBACrC,IAAI;oBAAC;oBAAoB;iBAAW,CAAClB,QAAQ,CAACL,EAAE9E,IAAI,CAACC,OAAO,KAAK;oBAC/D2D,YAAY0C,IAAI,CAAC;wBACfxF,MAAM/B;wBACNmF,UAAUjF,GAAG2G,kBAAkB,CAACC,KAAK;wBACrC1B,MAAMhG,eAAe2H,uBAAuB;wBAC5C1B,aAAa,CAAC,aAAa,EAAEU,EAAE9E,IAAI,CAACC,OAAO,GAAG,2CAA2C,CAAC;wBAC1F+D,OAAOc,EAAE9E,IAAI,CAAC+F,QAAQ;wBACtBzC,QAAQwB,EAAE9E,IAAI,CAACgG,QAAQ;oBACzB;gBACF;YACF;QACF;QAEA,OAAOpC;IACT;IAEA2C,4CACE1H,QAAgB,EAChBO,IAAgC;QAEhC,MAAMH,KAAKX;QAEX,MAAM6H,eAAe/G,KAAK+G,YAAY;QACtC,IAAIA,gBAAgBlH,GAAGmH,cAAc,CAACD,eAAe;YACnD,KAAK,MAAMrB,KAAKqB,aAAaE,QAAQ,CAAE;gBACrC,IAAIvB,EAAE9E,IAAI,CAACC,OAAO,OAAO,YAAY;oBACnC,+CAA+C;oBAC/C,MAAMuG,cAAcjI;oBACpB,IAAIiI,aAAa;wBACf,MAAMC,SAASD,YAAYE,mBAAmB,CAAC5B,EAAE9E,IAAI;wBACrD,IAAIyG,QAAQ;4BACV,MAAME,iBAAiBH,YAAYI,gBAAgB,CAACH;4BACpD,IAAIE,kBAAkBA,eAAe5G,YAAY,EAAE;gCACjD,MAAMD,cAAc6G,eAAe5G,YAAY,CAAC,EAAE;gCAClD,IAAID,eAAeb,GAAG4H,qBAAqB,CAAC/G,cAAc;oCACxD,IAAIyD,QAAQzD,cAAc;oCAE1B,MAAMgH,sBACJhH,YAAYiH,aAAa,GAAGlI,QAAQ;oCACtC,MAAMmI,aAAaF,wBAAwBjI;oCAE3C,0DAA0D;oCAC1D,MAAM6E,MAAMjB,0BACVqE,qBACAhH;oCAEF,IAAI,CAAC4D,KAAK;oCAEV,MAAME,cAAcH,iBAClBqD,qBACApD,KACA5D;oCAEF,IAAI8D,YAAYN,MAAM,EAAE;wCACtB,IAAI0D,YAAY;4CACd,OAAOpD;wCACT,OAAO;4CACL,OAAO;gDACL;oDACE9C,MAAMzC,UAAUQ;oDAChBqF,UAAUjF,GAAG2G,kBAAkB,CAACC,KAAK;oDACrC1B,MAAMhG,eAAe2H,uBAAuB;oDAC5C1B,aAAa,CAAC,0LAA0L,CAAC;oDACzMJ,OAAOc,EAAE9E,IAAI,CAAC+F,QAAQ;oDACtBzC,QAAQwB,EAAE9E,IAAI,CAACgG,QAAQ;gDACzB;6CACD;wCACH;oCACF;gCACF;4BACF;wBACF;oBACF;gBACF;YACF;QACF;QAEA,OAAO,EAAE;IACX;IAEAiB,2BACEpI,QAAgB,EAChBC,QAAgB,EAChBoI,SAAiB,EACjBC,aAAyC,EACzCpI,MAAc,EACdqI,WAAqC,EACrC1B,IAAkC;QAElC,MAAMtG,OAAOR,kBAAkBC,UAAUC;QACzC,IAAI,CAACM,MAAM;QACX,IAAImE,QAAQnE,OAAO;QAEnB,0DAA0D;QAC1D,MAAMsE,MAAMjB,0BAA0B5D,UAAUO;QAChD,IAAIsE,QAAQpC,WAAW;QAEvB,MAAM,EAAEjB,eAAe,EAAE,GAAGD;QAC5B,MAAMqE,SAAS3F,YAAY4E,GAAG,CAAC,EAAE,GAAG5E,WAAWA,WAAW4E,GAAG,CAAC,EAAE;QAEhE,MAAM2D,UAAUhH,gBAAgB4G,yBAAyB,CACvDpI,UACA4F,QACAyC,WACAC,eACApI,QACAqI,aACA1B;QAEF,OAAO2B;IACT;IAEAC,wBAAuBzI,QAAgB,EAAEC,QAAgB;QACvD,MAAMM,OAAOR,kBAAkBC,UAAUC;QACzC,IAAI,CAACM,MAAM;QACX,IAAImE,QAAQnE,OAAO;QAEnB,0DAA0D;QAC1D,MAAMsE,MAAMjB,0BAA0B5D,UAAUO;QAChD,IAAIsE,QAAQpC,WAAW;QAEvB,MAAM,EAAEjB,eAAe,EAAE,GAAGD;QAC5B,MAAMqE,SAAS3F,YAAY4E,GAAG,CAAC,EAAE,GAAG5E,WAAWA,WAAW4E,GAAG,CAAC,EAAE;QAChE,MAAM6D,UAAUlH,gBAAgBiH,sBAAsB,CAACzI,UAAU4F;QACjE,OAAO8C;IACT;IAEAC,2BAA0B3I,QAAgB,EAAEC,QAAgB;QAC1D,MAAMM,OAAOR,kBAAkBC,UAAUC;QACzC,IAAI,CAACM,MAAM;QACX,IAAImE,QAAQnE,OAAO;QACnB,IAAI,CAACZ,qBAAqBM,UAAUM,OAAO;QAC3C,0DAA0D;QAC1D,MAAMsE,MAAMjB,0BAA0B5D,UAAUO;QAChD,IAAIsE,QAAQpC,WAAW;QACvB,MAAM,EAAEjB,eAAe,EAAE,GAAGD;QAC5B,MAAMqE,SAAS3F,YAAY4E,GAAG,CAAC,EAAE,GAAG5E,WAAWA,WAAW4E,GAAG,CAAC,EAAE;QAEhE,MAAM+D,6BACJpH,gBAAgBmH,yBAAyB,CAAC3I,UAAU4F;QAEtD,IAAIgD,4BAA4B;YAC9B,6CAA6C;YAC7C,IAAIA,2BAA2BC,QAAQ,CAAC1D,KAAK,GAAGN,GAAG,CAAC,EAAE,EAAE;gBACtD+D,2BAA2BC,QAAQ,CAAC1D,KAAK,IAAIN,GAAG,CAAC,EAAE;YACrD;QACF;QACA,OAAO+D;IACT;AACF;AAEA,eAAepD,SAAQ"}