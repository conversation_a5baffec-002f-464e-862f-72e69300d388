{"version": 3, "sources": ["../../../../../src/client/components/router-reducer/reducers/server-action-reducer.ts"], "sourcesContent": ["import type {\n  ActionFlightResponse,\n  ActionResult,\n} from '../../../../server/app-render/types'\nimport { callServer } from '../../../app-call-server'\nimport { findSourceMapURL } from '../../../app-find-source-map-url'\nimport {\n  ACTION_HEADER,\n  NEXT_IS_PRERENDER_HEADER,\n  NEXT_ROUTER_STATE_TREE_HEADER,\n  NEXT_URL,\n  RSC_CONTENT_TYPE_HEADER,\n} from '../../app-router-headers'\n\n// // eslint-disable-next-line import/no-extraneous-dependencies\n// import { createFromFetch } from 'react-server-dom-webpack/client'\n// // eslint-disable-next-line import/no-extraneous-dependencies\n// import { encodeReply } from 'react-server-dom-webpack/client'\nconst { createFromFetch, createTemporaryReferenceSet, encodeReply } = (\n  !!process.env.NEXT_RUNTIME\n    ? // eslint-disable-next-line import/no-extraneous-dependencies\n      require('react-server-dom-webpack/client.edge')\n    : // eslint-disable-next-line import/no-extraneous-dependencies\n      require('react-server-dom-webpack/client')\n) as typeof import('react-server-dom-webpack/client')\n\nimport {\n  PrefetchKind,\n  type ReadonlyReducerState,\n  type ReducerState,\n  type ServerActionAction,\n  type ServerActionMutable,\n} from '../router-reducer-types'\nimport { assignLocation } from '../../../assign-location'\nimport { createHrefFromUrl } from '../create-href-from-url'\nimport { handleExternalUrl } from './navigate-reducer'\nimport { applyRouterStatePatchToTree } from '../apply-router-state-patch-to-tree'\nimport { isNavigatingToNewRootLayout } from '../is-navigating-to-new-root-layout'\nimport type { CacheNode } from '../../../../shared/lib/app-router-context.shared-runtime'\nimport { handleMutable } from '../handle-mutable'\nimport { fillLazyItemsTillLeafWithHead } from '../fill-lazy-items-till-leaf-with-head'\nimport { createEmptyCacheNode } from '../../app-router'\nimport { hasInterceptionRouteInCurrentTree } from './has-interception-route-in-current-tree'\nimport { handleSegmentMismatch } from '../handle-segment-mismatch'\nimport { refreshInactiveParallelSegments } from '../refetch-inactive-parallel-segments'\nimport {\n  normalizeFlightData,\n  type NormalizedFlightData,\n} from '../../../flight-data-helpers'\nimport { getRedirectError } from '../../redirect'\nimport { RedirectType } from '../../redirect-error'\nimport { createSeededPrefetchCacheEntry } from '../prefetch-cache-utils'\nimport { removeBasePath } from '../../../remove-base-path'\nimport { hasBasePath } from '../../../has-base-path'\nimport {\n  extractInfoFromServerReferenceId,\n  omitUnusedArgs,\n} from '../../../../shared/lib/server-reference-info'\nimport { revalidateEntireCache } from '../../segment-cache'\n\ntype FetchServerActionResult = {\n  redirectLocation: URL | undefined\n  redirectType: RedirectType | undefined\n  actionResult?: ActionResult\n  actionFlightData?: NormalizedFlightData[] | string\n  isPrerender: boolean\n  revalidatedParts: {\n    tag: boolean\n    cookie: boolean\n    paths: string[]\n  }\n}\n\nasync function fetchServerAction(\n  state: ReadonlyReducerState,\n  nextUrl: ReadonlyReducerState['nextUrl'],\n  { actionId, actionArgs }: ServerActionAction\n): Promise<FetchServerActionResult> {\n  const temporaryReferences = createTemporaryReferenceSet()\n  const info = extractInfoFromServerReferenceId(actionId)\n\n  // TODO: Currently, we're only omitting unused args for the experimental \"use\n  // cache\" functions. Once the server reference info byte feature is stable, we\n  // should apply this to server actions as well.\n  const usedArgs =\n    info.type === 'use-cache' ? omitUnusedArgs(actionArgs, info) : actionArgs\n\n  const body = await encodeReply(usedArgs, { temporaryReferences })\n\n  const res = await fetch('', {\n    method: 'POST',\n    headers: {\n      Accept: RSC_CONTENT_TYPE_HEADER,\n      [ACTION_HEADER]: actionId,\n      [NEXT_ROUTER_STATE_TREE_HEADER]: encodeURIComponent(\n        JSON.stringify(state.tree)\n      ),\n      ...(process.env.NEXT_DEPLOYMENT_ID\n        ? {\n            'x-deployment-id': process.env.NEXT_DEPLOYMENT_ID,\n          }\n        : {}),\n      ...(nextUrl\n        ? {\n            [NEXT_URL]: nextUrl,\n          }\n        : {}),\n    },\n    body,\n  })\n\n  const redirectHeader = res.headers.get('x-action-redirect')\n  const [location, _redirectType] = redirectHeader?.split(';') || []\n  let redirectType: RedirectType | undefined\n  switch (_redirectType) {\n    case 'push':\n      redirectType = RedirectType.push\n      break\n    case 'replace':\n      redirectType = RedirectType.replace\n      break\n    default:\n      redirectType = undefined\n  }\n\n  const isPrerender = !!res.headers.get(NEXT_IS_PRERENDER_HEADER)\n  let revalidatedParts: FetchServerActionResult['revalidatedParts']\n  try {\n    const revalidatedHeader = JSON.parse(\n      res.headers.get('x-action-revalidated') || '[[],0,0]'\n    )\n    revalidatedParts = {\n      paths: revalidatedHeader[0] || [],\n      tag: !!revalidatedHeader[1],\n      cookie: revalidatedHeader[2],\n    }\n  } catch (e) {\n    revalidatedParts = {\n      paths: [],\n      tag: false,\n      cookie: false,\n    }\n  }\n\n  const redirectLocation = location\n    ? assignLocation(\n        location,\n        new URL(state.canonicalUrl, window.location.href)\n      )\n    : undefined\n\n  const contentType = res.headers.get('content-type')\n\n  if (contentType?.startsWith(RSC_CONTENT_TYPE_HEADER)) {\n    const response: ActionFlightResponse = await createFromFetch(\n      Promise.resolve(res),\n      { callServer, findSourceMapURL, temporaryReferences }\n    )\n\n    if (location) {\n      // if it was a redirection, then result is just a regular RSC payload\n      return {\n        actionFlightData: normalizeFlightData(response.f),\n        redirectLocation,\n        redirectType,\n        revalidatedParts,\n        isPrerender,\n      }\n    }\n\n    return {\n      actionResult: response.a,\n      actionFlightData: normalizeFlightData(response.f),\n      redirectLocation,\n      redirectType,\n      revalidatedParts,\n      isPrerender,\n    }\n  }\n\n  // Handle invalid server action responses\n  if (res.status >= 400) {\n    // The server can respond with a text/plain error message, but we'll fallback to something generic\n    // if there isn't one.\n    const error =\n      contentType === 'text/plain'\n        ? await res.text()\n        : 'An unexpected response was received from the server.'\n\n    throw new Error(error)\n  }\n\n  return {\n    redirectLocation,\n    redirectType,\n    revalidatedParts,\n    isPrerender,\n  }\n}\n\n/*\n * This reducer is responsible for calling the server action and processing any side-effects from the server action.\n * It does not mutate the state by itself but rather delegates to other reducers to do the actual mutation.\n */\nexport function serverActionReducer(\n  state: ReadonlyReducerState,\n  action: ServerActionAction\n): ReducerState {\n  const { resolve, reject } = action\n  const mutable: ServerActionMutable = {}\n\n  let currentTree = state.tree\n\n  mutable.preserveCustomHistoryState = false\n\n  // only pass along the `nextUrl` param (used for interception routes) if the current route was intercepted.\n  // If the route has been intercepted, the action should be as well.\n  // Otherwise the server action might be intercepted with the wrong action id\n  // (ie, one that corresponds with the intercepted route)\n  const nextUrl =\n    state.nextUrl && hasInterceptionRouteInCurrentTree(state.tree)\n      ? state.nextUrl\n      : null\n\n  return fetchServerAction(state, nextUrl, action).then(\n    async ({\n      actionResult,\n      actionFlightData: flightData,\n      redirectLocation,\n      redirectType,\n      isPrerender,\n      revalidatedParts,\n    }) => {\n      let redirectHref: string | undefined\n\n      // honor the redirect type instead of defaulting to push in case of server actions.\n      if (redirectLocation) {\n        if (redirectType === RedirectType.replace) {\n          state.pushRef.pendingPush = false\n          mutable.pendingPush = false\n        } else {\n          state.pushRef.pendingPush = true\n          mutable.pendingPush = true\n        }\n\n        redirectHref = createHrefFromUrl(redirectLocation, false)\n        mutable.canonicalUrl = redirectHref\n      }\n\n      if (!flightData) {\n        resolve(actionResult)\n\n        // If there is a redirect but no flight data we need to do a mpaNavigation.\n        if (redirectLocation) {\n          return handleExternalUrl(\n            state,\n            mutable,\n            redirectLocation.href,\n            state.pushRef.pendingPush\n          )\n        }\n        return state\n      }\n\n      if (typeof flightData === 'string') {\n        // Handle case when navigating to page in `pages` from `app`\n        resolve(actionResult)\n\n        return handleExternalUrl(\n          state,\n          mutable,\n          flightData,\n          state.pushRef.pendingPush\n        )\n      }\n\n      const actionRevalidated =\n        revalidatedParts.paths.length > 0 ||\n        revalidatedParts.tag ||\n        revalidatedParts.cookie\n\n      for (const normalizedFlightData of flightData) {\n        const {\n          tree: treePatch,\n          seedData: cacheNodeSeedData,\n          head,\n          isRootRender,\n        } = normalizedFlightData\n\n        if (!isRootRender) {\n          // TODO-APP: handle this case better\n          console.log('SERVER ACTION APPLY FAILED')\n          resolve(actionResult)\n\n          return state\n        }\n\n        // Given the path can only have two items the items are only the router state and rsc for the root.\n        const newTree = applyRouterStatePatchToTree(\n          // TODO-APP: remove ''\n          [''],\n          currentTree,\n          treePatch,\n          redirectHref ? redirectHref : state.canonicalUrl\n        )\n\n        if (newTree === null) {\n          resolve(actionResult)\n\n          return handleSegmentMismatch(state, action, treePatch)\n        }\n\n        if (isNavigatingToNewRootLayout(currentTree, newTree)) {\n          resolve(actionResult)\n\n          return handleExternalUrl(\n            state,\n            mutable,\n            redirectHref || state.canonicalUrl,\n            state.pushRef.pendingPush\n          )\n        }\n\n        // The server sent back RSC data for the server action, so we need to apply it to the cache.\n        if (cacheNodeSeedData !== null) {\n          const rsc = cacheNodeSeedData[1]\n          const cache: CacheNode = createEmptyCacheNode()\n          cache.rsc = rsc\n          cache.prefetchRsc = null\n          cache.loading = cacheNodeSeedData[3]\n          fillLazyItemsTillLeafWithHead(\n            cache,\n            // Existing cache is not passed in as server actions have to invalidate the entire cache.\n            undefined,\n            treePatch,\n            cacheNodeSeedData,\n            head,\n            undefined\n          )\n\n          mutable.cache = cache\n          if (process.env.__NEXT_CLIENT_SEGMENT_CACHE) {\n            revalidateEntireCache(state.nextUrl, newTree)\n          } else {\n            mutable.prefetchCache = new Map()\n          }\n          if (actionRevalidated) {\n            await refreshInactiveParallelSegments({\n              state,\n              updatedTree: newTree,\n              updatedCache: cache,\n              includeNextUrl: Boolean(nextUrl),\n              canonicalUrl: mutable.canonicalUrl || state.canonicalUrl,\n            })\n          }\n        }\n\n        mutable.patchedTree = newTree\n        currentTree = newTree\n      }\n\n      if (redirectLocation && redirectHref) {\n        if (!process.env.__NEXT_CLIENT_SEGMENT_CACHE && !actionRevalidated) {\n          // Because the RedirectBoundary will trigger a navigation, we need to seed the prefetch cache\n          // with the FlightData that we got from the server action for the target page, so that it's\n          // available when the page is navigated to and doesn't need to be re-fetched.\n          // We only do this if the server action didn't revalidate any data, as in that case the\n          // client cache will be cleared and the data will be re-fetched anyway.\n          // NOTE: We don't do this in the Segment Cache implementation.\n          // Dynamic data should never be placed into the cache, unless it's\n          // \"converted\" to static data using <Link prefetch={true}>. What we\n          // do instead is re-prefetch links and forms whenever the cache is\n          // invalidated.\n          createSeededPrefetchCacheEntry({\n            url: redirectLocation,\n            data: {\n              flightData,\n              canonicalUrl: undefined,\n              couldBeIntercepted: false,\n              prerendered: false,\n              postponed: false,\n              // TODO: We should be able to set this if the server action\n              // returned a fully static response.\n              staleTime: -1,\n            },\n            tree: state.tree,\n            prefetchCache: state.prefetchCache,\n            nextUrl: state.nextUrl,\n            kind: isPrerender ? PrefetchKind.FULL : PrefetchKind.AUTO,\n          })\n          mutable.prefetchCache = state.prefetchCache\n        }\n\n        // If the action triggered a redirect, the action promise will be rejected with\n        // a redirect so that it's handled by RedirectBoundary as we won't have a valid\n        // action result to resolve the promise with. This will effectively reset the state of\n        // the component that called the action as the error boundary will remount the tree.\n        // The status code doesn't matter here as the action handler will have already sent\n        // a response with the correct status code.\n        reject(\n          getRedirectError(\n            hasBasePath(redirectHref)\n              ? removeBasePath(redirectHref)\n              : redirectHref,\n            redirectType || RedirectType.push\n          )\n        )\n      } else {\n        resolve(actionResult)\n      }\n\n      return handleMutable(state, mutable)\n    },\n    (e: any) => {\n      // When the server action is rejected we don't update the state and instead call the reject handler of the promise.\n      reject(e)\n\n      return state\n    }\n  )\n}\n"], "names": ["serverActionReducer", "createFromFetch", "createTemporaryReferenceSet", "encodeReply", "process", "env", "NEXT_RUNTIME", "require", "fetchServerAction", "state", "nextUrl", "actionId", "actionArgs", "temporaryReferences", "info", "extractInfoFromServerReferenceId", "usedArgs", "type", "omit<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "body", "res", "fetch", "method", "headers", "Accept", "RSC_CONTENT_TYPE_HEADER", "ACTION_HEADER", "NEXT_ROUTER_STATE_TREE_HEADER", "encodeURIComponent", "JSON", "stringify", "tree", "NEXT_DEPLOYMENT_ID", "NEXT_URL", "redirectHeader", "get", "location", "_redirectType", "split", "redirectType", "RedirectType", "push", "replace", "undefined", "<PERSON><PERSON><PERSON><PERSON>", "NEXT_IS_PRERENDER_HEADER", "revalidatedParts", "revalidatedHeader", "parse", "paths", "tag", "cookie", "e", "redirectLocation", "assignLocation", "URL", "canonicalUrl", "window", "href", "contentType", "startsWith", "response", "Promise", "resolve", "callServer", "findSourceMapURL", "actionFlightData", "normalizeFlightData", "f", "actionResult", "a", "status", "error", "text", "Error", "action", "reject", "mutable", "currentTree", "preserveCustomHistoryState", "hasInterceptionRouteInCurrentTree", "then", "flightData", "redirectHref", "pushRef", "pendingPush", "createHrefFromUrl", "handleExternalUrl", "actionRevalidated", "length", "normalizedFlightData", "treePatch", "seedData", "cacheNodeSeedData", "head", "isRootRender", "console", "log", "newTree", "applyRouterStatePatchToTree", "handleSegmentMismatch", "isNavigatingToNewRootLayout", "rsc", "cache", "createEmptyCacheNode", "prefetchRsc", "loading", "fillLazyItemsTillLeafWithHead", "__NEXT_CLIENT_SEGMENT_CACHE", "revalidateEntireCache", "prefetchCache", "Map", "refreshInactiveParallelSegments", "updatedTree", "updatedCache", "includeNextUrl", "Boolean", "patchedTree", "createSeededPrefetchCacheEntry", "url", "data", "couldBeIntercepted", "prerendered", "postponed", "staleTime", "kind", "PrefetchKind", "FULL", "AUTO", "getRedirectError", "has<PERSON>ase<PERSON><PERSON>", "removeBasePath", "handleMutable"], "mappings": ";;;;+BA4MgBA;;;eAAAA;;;+BAxMW;qCACM;kCAO1B;oCAoBA;gCACwB;mCACG;iCACA;6CACU;6CACA;+BAEd;+CACgB;2BACT;mDACa;uCACZ;iDACU;mCAIzC;0BAC0B;+BACJ;oCACkB;gCAChB;6BACH;qCAIrB;8BAC+B;AA5CtC,gEAAgE;AAChE,oEAAoE;AACpE,gEAAgE;AAChE,gEAAgE;AAChE,MAAM,EAAEC,eAAe,EAAEC,2BAA2B,EAAEC,WAAW,EAAE,GACjE,CAAC,CAACC,QAAQC,GAAG,CAACC,YAAY,GAEtBC,QAAQ,0CAERA,QAAQ;AAkDd,eAAeC,kBACbC,KAA2B,EAC3BC,OAAwC,EACxC,KAA4C;IAA5C,IAAA,EAAEC,QAAQ,EAAEC,UAAU,EAAsB,GAA5C;IAEA,MAAMC,sBAAsBX;IAC5B,MAAMY,OAAOC,IAAAA,qDAAgC,EAACJ;IAE9C,6EAA6E;IAC7E,8EAA8E;IAC9E,+CAA+C;IAC/C,MAAMK,WACJF,KAAKG,IAAI,KAAK,cAAcC,IAAAA,mCAAc,EAACN,YAAYE,QAAQF;IAEjE,MAAMO,OAAO,MAAMhB,YAAYa,UAAU;QAAEH;IAAoB;IAE/D,MAAMO,MAAM,MAAMC,MAAM,IAAI;QAC1BC,QAAQ;QACRC,SAAS;YACPC,QAAQC,yCAAuB;YAC/B,CAACC,+BAAa,CAAC,EAAEf;YACjB,CAACgB,+CAA6B,CAAC,EAAEC,mBAC/BC,KAAKC,SAAS,CAACrB,MAAMsB,IAAI;YAE3B,GAAI3B,QAAQC,GAAG,CAAC2B,kBAAkB,GAC9B;gBACE,mBAAmB5B,QAAQC,GAAG,CAAC2B,kBAAkB;YACnD,IACA,CAAC,CAAC;YACN,GAAItB,UACA;gBACE,CAACuB,0BAAQ,CAAC,EAAEvB;YACd,IACA,CAAC,CAAC;QACR;QACAS;IACF;IAEA,MAAMe,iBAAiBd,IAAIG,OAAO,CAACY,GAAG,CAAC;IACvC,MAAM,CAACC,UAAUC,cAAc,GAAGH,CAAAA,kCAAAA,eAAgBI,KAAK,CAAC,SAAQ,EAAE;IAClE,IAAIC;IACJ,OAAQF;QACN,KAAK;YACHE,eAAeC,2BAAY,CAACC,IAAI;YAChC;QACF,KAAK;YACHF,eAAeC,2BAAY,CAACE,OAAO;YACnC;QACF;YACEH,eAAeI;IACnB;IAEA,MAAMC,cAAc,CAAC,CAACxB,IAAIG,OAAO,CAACY,GAAG,CAACU,0CAAwB;IAC9D,IAAIC;IACJ,IAAI;QACF,MAAMC,oBAAoBlB,KAAKmB,KAAK,CAClC5B,IAAIG,OAAO,CAACY,GAAG,CAAC,2BAA2B;QAE7CW,mBAAmB;YACjBG,OAAOF,iBAAiB,CAAC,EAAE,IAAI,EAAE;YACjCG,KAAK,CAAC,CAACH,iBAAiB,CAAC,EAAE;YAC3BI,QAAQJ,iBAAiB,CAAC,EAAE;QAC9B;IACF,EAAE,OAAOK,GAAG;QACVN,mBAAmB;YACjBG,OAAO,EAAE;YACTC,KAAK;YACLC,QAAQ;QACV;IACF;IAEA,MAAME,mBAAmBjB,WACrBkB,IAAAA,8BAAc,EACZlB,UACA,IAAImB,IAAI9C,MAAM+C,YAAY,EAAEC,OAAOrB,QAAQ,CAACsB,IAAI,KAElDf;IAEJ,MAAMgB,cAAcvC,IAAIG,OAAO,CAACY,GAAG,CAAC;IAEpC,IAAIwB,+BAAAA,YAAaC,UAAU,CAACnC,yCAAuB,GAAG;QACpD,MAAMoC,WAAiC,MAAM5D,gBAC3C6D,QAAQC,OAAO,CAAC3C,MAChB;YAAE4C,YAAAA,yBAAU;YAAEC,kBAAAA,qCAAgB;YAAEpD;QAAoB;QAGtD,IAAIuB,UAAU;YACZ,qEAAqE;YACrE,OAAO;gBACL8B,kBAAkBC,IAAAA,sCAAmB,EAACN,SAASO,CAAC;gBAChDf;gBACAd;gBACAO;gBACAF;YACF;QACF;QAEA,OAAO;YACLyB,cAAcR,SAASS,CAAC;YACxBJ,kBAAkBC,IAAAA,sCAAmB,EAACN,SAASO,CAAC;YAChDf;YACAd;YACAO;YACAF;QACF;IACF;IAEA,yCAAyC;IACzC,IAAIxB,IAAImD,MAAM,IAAI,KAAK;QACrB,kGAAkG;QAClG,sBAAsB;QACtB,MAAMC,QACJb,gBAAgB,eACZ,MAAMvC,IAAIqD,IAAI,KACd;QAEN,MAAM,qBAAgB,CAAhB,IAAIC,MAAMF,QAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAe;IACvB;IAEA,OAAO;QACLnB;QACAd;QACAO;QACAF;IACF;AACF;AAMO,SAAS5C,oBACdS,KAA2B,EAC3BkE,MAA0B;IAE1B,MAAM,EAAEZ,OAAO,EAAEa,MAAM,EAAE,GAAGD;IAC5B,MAAME,UAA+B,CAAC;IAEtC,IAAIC,cAAcrE,MAAMsB,IAAI;IAE5B8C,QAAQE,0BAA0B,GAAG;IAErC,2GAA2G;IAC3G,mEAAmE;IACnE,4EAA4E;IAC5E,wDAAwD;IACxD,MAAMrE,UACJD,MAAMC,OAAO,IAAIsE,IAAAA,oEAAiC,EAACvE,MAAMsB,IAAI,IACzDtB,MAAMC,OAAO,GACb;IAEN,OAAOF,kBAAkBC,OAAOC,SAASiE,QAAQM,IAAI,CACnD;YAAO,EACLZ,YAAY,EACZH,kBAAkBgB,UAAU,EAC5B7B,gBAAgB,EAChBd,YAAY,EACZK,WAAW,EACXE,gBAAgB,EACjB;QACC,IAAIqC;QAEJ,mFAAmF;QACnF,IAAI9B,kBAAkB;YACpB,IAAId,iBAAiBC,2BAAY,CAACE,OAAO,EAAE;gBACzCjC,MAAM2E,OAAO,CAACC,WAAW,GAAG;gBAC5BR,QAAQQ,WAAW,GAAG;YACxB,OAAO;gBACL5E,MAAM2E,OAAO,CAACC,WAAW,GAAG;gBAC5BR,QAAQQ,WAAW,GAAG;YACxB;YAEAF,eAAeG,IAAAA,oCAAiB,EAACjC,kBAAkB;YACnDwB,QAAQrB,YAAY,GAAG2B;QACzB;QAEA,IAAI,CAACD,YAAY;YACfnB,QAAQM;YAER,2EAA2E;YAC3E,IAAIhB,kBAAkB;gBACpB,OAAOkC,IAAAA,kCAAiB,EACtB9E,OACAoE,SACAxB,iBAAiBK,IAAI,EACrBjD,MAAM2E,OAAO,CAACC,WAAW;YAE7B;YACA,OAAO5E;QACT;QAEA,IAAI,OAAOyE,eAAe,UAAU;YAClC,4DAA4D;YAC5DnB,QAAQM;YAER,OAAOkB,IAAAA,kCAAiB,EACtB9E,OACAoE,SACAK,YACAzE,MAAM2E,OAAO,CAACC,WAAW;QAE7B;QAEA,MAAMG,oBACJ1C,iBAAiBG,KAAK,CAACwC,MAAM,GAAG,KAChC3C,iBAAiBI,GAAG,IACpBJ,iBAAiBK,MAAM;QAEzB,KAAK,MAAMuC,wBAAwBR,WAAY;YAC7C,MAAM,EACJnD,MAAM4D,SAAS,EACfC,UAAUC,iBAAiB,EAC3BC,IAAI,EACJC,YAAY,EACb,GAAGL;YAEJ,IAAI,CAACK,cAAc;gBACjB,oCAAoC;gBACpCC,QAAQC,GAAG,CAAC;gBACZlC,QAAQM;gBAER,OAAO5D;YACT;YAEA,mGAAmG;YACnG,MAAMyF,UAAUC,IAAAA,wDAA2B,EACzC,sBAAsB;YACtB;gBAAC;aAAG,EACJrB,aACAa,WACAR,eAAeA,eAAe1E,MAAM+C,YAAY;YAGlD,IAAI0C,YAAY,MAAM;gBACpBnC,QAAQM;gBAER,OAAO+B,IAAAA,4CAAqB,EAAC3F,OAAOkE,QAAQgB;YAC9C;YAEA,IAAIU,IAAAA,wDAA2B,EAACvB,aAAaoB,UAAU;gBACrDnC,QAAQM;gBAER,OAAOkB,IAAAA,kCAAiB,EACtB9E,OACAoE,SACAM,gBAAgB1E,MAAM+C,YAAY,EAClC/C,MAAM2E,OAAO,CAACC,WAAW;YAE7B;YAEA,4FAA4F;YAC5F,IAAIQ,sBAAsB,MAAM;gBAC9B,MAAMS,MAAMT,iBAAiB,CAAC,EAAE;gBAChC,MAAMU,QAAmBC,IAAAA,+BAAoB;gBAC7CD,MAAMD,GAAG,GAAGA;gBACZC,MAAME,WAAW,GAAG;gBACpBF,MAAMG,OAAO,GAAGb,iBAAiB,CAAC,EAAE;gBACpCc,IAAAA,4DAA6B,EAC3BJ,OACA,yFAAyF;gBACzF5D,WACAgD,WACAE,mBACAC,MACAnD;gBAGFkC,QAAQ0B,KAAK,GAAGA;gBAChB,IAAInG,QAAQC,GAAG,CAACuG,2BAA2B,EAAE;oBAC3CC,IAAAA,mCAAqB,EAACpG,MAAMC,OAAO,EAAEwF;gBACvC,OAAO;oBACLrB,QAAQiC,aAAa,GAAG,IAAIC;gBAC9B;gBACA,IAAIvB,mBAAmB;oBACrB,MAAMwB,IAAAA,gEAA+B,EAAC;wBACpCvG;wBACAwG,aAAaf;wBACbgB,cAAcX;wBACdY,gBAAgBC,QAAQ1G;wBACxB8C,cAAcqB,QAAQrB,YAAY,IAAI/C,MAAM+C,YAAY;oBAC1D;gBACF;YACF;YAEAqB,QAAQwC,WAAW,GAAGnB;YACtBpB,cAAcoB;QAChB;QAEA,IAAI7C,oBAAoB8B,cAAc;YACpC,IAAI,CAAC/E,QAAQC,GAAG,CAACuG,2BAA2B,IAAI,CAACpB,mBAAmB;gBAClE,6FAA6F;gBAC7F,2FAA2F;gBAC3F,6EAA6E;gBAC7E,uFAAuF;gBACvF,uEAAuE;gBACvE,8DAA8D;gBAC9D,kEAAkE;gBAClE,mEAAmE;gBACnE,kEAAkE;gBAClE,eAAe;gBACf8B,IAAAA,kDAA8B,EAAC;oBAC7BC,KAAKlE;oBACLmE,MAAM;wBACJtC;wBACA1B,cAAcb;wBACd8E,oBAAoB;wBACpBC,aAAa;wBACbC,WAAW;wBACX,2DAA2D;wBAC3D,oCAAoC;wBACpCC,WAAW,CAAC;oBACd;oBACA7F,MAAMtB,MAAMsB,IAAI;oBAChB+E,eAAerG,MAAMqG,aAAa;oBAClCpG,SAASD,MAAMC,OAAO;oBACtBmH,MAAMjF,cAAckF,gCAAY,CAACC,IAAI,GAAGD,gCAAY,CAACE,IAAI;gBAC3D;gBACAnD,QAAQiC,aAAa,GAAGrG,MAAMqG,aAAa;YAC7C;YAEA,+EAA+E;YAC/E,+EAA+E;YAC/E,sFAAsF;YACtF,oFAAoF;YACpF,mFAAmF;YACnF,2CAA2C;YAC3ClC,OACEqD,IAAAA,0BAAgB,EACdC,IAAAA,wBAAW,EAAC/C,gBACRgD,IAAAA,8BAAc,EAAChD,gBACfA,cACJ5C,gBAAgBC,2BAAY,CAACC,IAAI;QAGvC,OAAO;YACLsB,QAAQM;QACV;QAEA,OAAO+D,IAAAA,4BAAa,EAAC3H,OAAOoE;IAC9B,GACA,CAACzB;QACC,mHAAmH;QACnHwB,OAAOxB;QAEP,OAAO3C;IACT;AAEJ"}