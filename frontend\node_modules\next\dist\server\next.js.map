{"version": 3, "sources": ["../../src/server/next.ts"], "sourcesContent": ["import type { Options as DevServerOptions } from './dev/next-dev-server'\nimport type {\n  NodeRequestHandler,\n  Options as ServerOptions,\n} from './next-server'\nimport type { UrlWithParsedQuery } from 'url'\nimport type { IncomingMessage, ServerResponse } from 'http'\nimport type { Duplex } from 'stream'\nimport type { NextUrlWithParsedQuery } from './request-meta'\n\nimport './require-hook'\nimport './node-polyfill-crypto'\n\nimport type { default as NextNodeServer } from './next-server'\nimport * as log from '../build/output/log'\nimport loadConfig from './config'\nimport path, { resolve } from 'path'\nimport { NON_STANDARD_NODE_ENV } from '../lib/constants'\nimport {\n  PHASE_DEVELOPMENT_SERVER,\n  SERVER_FILES_MANIFEST,\n} from '../shared/lib/constants'\nimport { PHASE_PRODUCTION_SERVER } from '../shared/lib/constants'\nimport { getTracer } from './lib/trace/tracer'\nimport { NextServerSpan } from './lib/trace/constants'\nimport { formatUrl } from '../shared/lib/router/utils/format-url'\nimport type { ServerFields } from './lib/router-utils/setup-dev-bundler'\nimport type { ServerInitResult } from './lib/render-server'\nimport { AsyncCallbackSet } from './lib/async-callback-set'\n\nlet ServerImpl: typeof NextNodeServer\n\nconst getServerImpl = async () => {\n  if (ServerImpl === undefined) {\n    ServerImpl = (await Promise.resolve(require('./next-server'))).default\n  }\n  return ServerImpl\n}\n\nexport type NextServerOptions = Omit<\n  ServerOptions | DevServerOptions,\n  // This is assigned in this server abstraction.\n  'conf'\n> &\n  Partial<Pick<ServerOptions | DevServerOptions, 'conf'>>\n\nexport type RequestHandler = (\n  req: IncomingMessage,\n  res: ServerResponse,\n  parsedUrl?: NextUrlWithParsedQuery | undefined\n) => Promise<void>\n\nexport type UpgradeHandler = (\n  req: IncomingMessage,\n  socket: Duplex,\n  head: Buffer\n) => Promise<void>\n\nconst SYMBOL_LOAD_CONFIG = Symbol('next.load_config')\n\ninterface NextWrapperServer {\n  // NOTE: the methods/properties here are the public API for custom servers.\n  // Consider backwards compatibilty when changing something here!\n\n  options: NextServerOptions\n  hostname: string | undefined\n  port: number | undefined\n\n  getRequestHandler(): RequestHandler\n  prepare(serverFields?: ServerFields): Promise<void>\n  setAssetPrefix(assetPrefix: string): void\n  close(): Promise<void>\n\n  // used internally\n  getUpgradeHandler(): UpgradeHandler\n\n  // legacy methods that we left exposed in the past\n\n  logError(...args: Parameters<NextNodeServer['logError']>): void\n\n  render(\n    ...args: Parameters<NextNodeServer['render']>\n  ): ReturnType<NextNodeServer['render']>\n\n  renderToHTML(\n    ...args: Parameters<NextNodeServer['renderToHTML']>\n  ): ReturnType<NextNodeServer['renderToHTML']>\n\n  renderError(\n    ...args: Parameters<NextNodeServer['renderError']>\n  ): ReturnType<NextNodeServer['renderError']>\n\n  renderErrorToHTML(\n    ...args: Parameters<NextNodeServer['renderErrorToHTML']>\n  ): ReturnType<NextNodeServer['renderErrorToHTML']>\n\n  render404(\n    ...args: Parameters<NextNodeServer['render404']>\n  ): ReturnType<NextNodeServer['render404']>\n}\n\n/** The wrapper server used by `next start` */\nexport class NextServer implements NextWrapperServer {\n  private serverPromise?: Promise<NextNodeServer>\n  private server?: NextNodeServer\n  private reqHandler?: NodeRequestHandler\n  private reqHandlerPromise?: Promise<NodeRequestHandler>\n  private preparedAssetPrefix?: string\n\n  public options: NextServerOptions\n\n  constructor(options: NextServerOptions) {\n    this.options = options\n  }\n\n  get hostname() {\n    return this.options.hostname\n  }\n\n  get port() {\n    return this.options.port\n  }\n\n  getRequestHandler(): RequestHandler {\n    return async (\n      req: IncomingMessage,\n      res: ServerResponse,\n      parsedUrl?: UrlWithParsedQuery\n    ) => {\n      return getTracer().trace(NextServerSpan.getRequestHandler, async () => {\n        const requestHandler = await this.getServerRequestHandler()\n        return requestHandler(req, res, parsedUrl)\n      })\n    }\n  }\n\n  getUpgradeHandler(): UpgradeHandler {\n    return async (req: IncomingMessage, socket: any, head: any) => {\n      const server = await this.getServer()\n      // @ts-expect-error we mark this as protected so it\n      // causes an error here\n      return server.handleUpgrade.apply(server, [req, socket, head])\n    }\n  }\n\n  setAssetPrefix(assetPrefix: string) {\n    if (this.server) {\n      this.server.setAssetPrefix(assetPrefix)\n    } else {\n      this.preparedAssetPrefix = assetPrefix\n    }\n  }\n\n  logError(...args: Parameters<NextWrapperServer['logError']>) {\n    if (this.server) {\n      this.server.logError(...args)\n    }\n  }\n\n  async render(...args: Parameters<NextWrapperServer['render']>) {\n    const server = await this.getServer()\n    return server.render(...args)\n  }\n\n  async renderToHTML(...args: Parameters<NextWrapperServer['renderToHTML']>) {\n    const server = await this.getServer()\n    return server.renderToHTML(...args)\n  }\n\n  async renderError(...args: Parameters<NextWrapperServer['renderError']>) {\n    const server = await this.getServer()\n    return server.renderError(...args)\n  }\n\n  async renderErrorToHTML(\n    ...args: Parameters<NextWrapperServer['renderErrorToHTML']>\n  ) {\n    const server = await this.getServer()\n    return server.renderErrorToHTML(...args)\n  }\n\n  async render404(...args: Parameters<NextWrapperServer['render404']>) {\n    const server = await this.getServer()\n    return server.render404(...args)\n  }\n\n  async prepare(serverFields?: ServerFields) {\n    const server = await this.getServer()\n\n    if (serverFields) {\n      Object.assign(server, serverFields)\n    }\n    // We shouldn't prepare the server in production,\n    // because this code won't be executed when deployed\n    if (this.options.dev) {\n      await server.prepare()\n    }\n  }\n\n  async close() {\n    if (this.server) {\n      await this.server.close()\n    }\n  }\n\n  private async createServer(\n    options: ServerOptions | DevServerOptions\n  ): Promise<NextNodeServer> {\n    let ServerImplementation: typeof NextNodeServer\n    if (options.dev) {\n      ServerImplementation = require('./dev/next-dev-server')\n        .default as typeof import('./dev/next-dev-server').default\n    } else {\n      ServerImplementation = await getServerImpl()\n    }\n    const server = new ServerImplementation(options)\n\n    return server\n  }\n\n  private async [SYMBOL_LOAD_CONFIG]() {\n    const dir = resolve(this.options.dir || '.')\n\n    const config = await loadConfig(\n      this.options.dev ? PHASE_DEVELOPMENT_SERVER : PHASE_PRODUCTION_SERVER,\n      dir,\n      {\n        customConfig: this.options.conf,\n        silent: true,\n      }\n    )\n\n    // check serialized build config when available\n    if (process.env.NODE_ENV === 'production') {\n      try {\n        const serializedConfig = require(\n          path.join(dir, '.next', SERVER_FILES_MANIFEST)\n        ).config\n\n        // @ts-expect-error internal field\n        config.experimental.isExperimentalCompile =\n          serializedConfig.experimental.isExperimentalCompile\n      } catch (_) {\n        // if distDir is customized we don't know until we\n        // load the config so fallback to loading the config\n        // from next.config.js\n      }\n    }\n\n    return config\n  }\n\n  private async getServer() {\n    if (!this.serverPromise) {\n      this.serverPromise = this[SYMBOL_LOAD_CONFIG]().then(async (conf) => {\n        if (!this.options.dev) {\n          if (conf.output === 'standalone') {\n            if (!process.env.__NEXT_PRIVATE_STANDALONE_CONFIG) {\n              log.warn(\n                `\"next start\" does not work with \"output: standalone\" configuration. Use \"node .next/standalone/server.js\" instead.`\n              )\n            }\n          } else if (conf.output === 'export') {\n            throw new Error(\n              `\"next start\" does not work with \"output: export\" configuration. Use \"npx serve@latest out\" instead.`\n            )\n          }\n        }\n\n        this.server = await this.createServer({\n          ...this.options,\n          conf,\n        })\n        if (this.preparedAssetPrefix) {\n          this.server.setAssetPrefix(this.preparedAssetPrefix)\n        }\n        return this.server\n      })\n    }\n    return this.serverPromise\n  }\n\n  private async getServerRequestHandler() {\n    if (this.reqHandler) return this.reqHandler\n\n    // Memoize request handler creation\n    if (!this.reqHandlerPromise) {\n      this.reqHandlerPromise = this.getServer().then((server) => {\n        this.reqHandler = getTracer().wrap(\n          NextServerSpan.getServerRequestHandler,\n          server.getRequestHandler().bind(server)\n        )\n        delete this.reqHandlerPromise\n        return this.reqHandler\n      })\n    }\n    return this.reqHandlerPromise\n  }\n}\n\n/** The wrapper server used for `import next from \"next\" (in a custom server)` */\nclass NextCustomServer implements NextWrapperServer {\n  private didWebSocketSetup: boolean = false\n  protected cleanupListeners?: AsyncCallbackSet\n\n  protected init?: ServerInitResult\n\n  public options: NextServerOptions\n\n  constructor(options: NextServerOptions) {\n    this.options = options\n  }\n\n  protected getInit() {\n    if (!this.init) {\n      throw new Error(\n        'prepare() must be called before performing this operation'\n      )\n    }\n    return this.init\n  }\n\n  protected get requestHandler() {\n    return this.getInit().requestHandler\n  }\n  protected get upgradeHandler() {\n    return this.getInit().upgradeHandler\n  }\n  protected get server() {\n    return this.getInit().server\n  }\n\n  get hostname() {\n    return this.options.hostname\n  }\n\n  get port() {\n    return this.options.port\n  }\n\n  async prepare() {\n    const { getRequestHandlers } =\n      require('./lib/start-server') as typeof import('./lib/start-server')\n\n    let onDevServerCleanup: AsyncCallbackSet['add'] | undefined\n    if (this.options.dev) {\n      this.cleanupListeners = new AsyncCallbackSet()\n      onDevServerCleanup = this.cleanupListeners.add.bind(this.cleanupListeners)\n    }\n\n    const initResult = await getRequestHandlers({\n      dir: this.options.dir!,\n      port: this.options.port || 3000,\n      isDev: !!this.options.dev,\n      onDevServerCleanup,\n      hostname: this.options.hostname || 'localhost',\n      minimalMode: this.options.minimalMode,\n      quiet: this.options.quiet,\n    })\n    this.init = initResult\n  }\n\n  private setupWebSocketHandler(\n    customServer?: import('http').Server,\n    _req?: IncomingMessage\n  ) {\n    if (!this.didWebSocketSetup) {\n      this.didWebSocketSetup = true\n      customServer = customServer || (_req?.socket as any)?.server\n\n      if (customServer) {\n        customServer.on('upgrade', async (req, socket, head) => {\n          this.upgradeHandler(req, socket, head)\n        })\n      }\n    }\n  }\n\n  getRequestHandler(): RequestHandler {\n    return async (\n      req: IncomingMessage,\n      res: ServerResponse,\n      parsedUrl?: UrlWithParsedQuery\n    ) => {\n      this.setupWebSocketHandler(this.options.httpServer, req)\n\n      if (parsedUrl) {\n        req.url = formatUrl(parsedUrl)\n      }\n\n      return this.requestHandler(req, res)\n    }\n  }\n\n  async render(...args: Parameters<NextWrapperServer['render']>) {\n    let [req, res, pathname, query, parsedUrl] = args\n    this.setupWebSocketHandler(this.options.httpServer, req as IncomingMessage)\n\n    if (!pathname.startsWith('/')) {\n      console.error(`Cannot render page with path \"${pathname}\"`)\n      pathname = `/${pathname}`\n    }\n    pathname = pathname === '/index' ? '/' : pathname\n\n    req.url = formatUrl({\n      ...parsedUrl,\n      pathname,\n      query,\n    })\n\n    await this.requestHandler(req as IncomingMessage, res as ServerResponse)\n    return\n  }\n\n  setAssetPrefix(assetPrefix: string): void {\n    this.server.setAssetPrefix(assetPrefix)\n  }\n\n  getUpgradeHandler(): UpgradeHandler {\n    return this.server.getUpgradeHandler()\n  }\n\n  logError(...args: Parameters<NextWrapperServer['logError']>) {\n    this.server.logError(...args)\n  }\n\n  async renderToHTML(...args: Parameters<NextWrapperServer['renderToHTML']>) {\n    return this.server.renderToHTML(...args)\n  }\n\n  async renderError(...args: Parameters<NextWrapperServer['renderError']>) {\n    return this.server.renderError(...args)\n  }\n\n  async renderErrorToHTML(\n    ...args: Parameters<NextWrapperServer['renderErrorToHTML']>\n  ) {\n    return this.server.renderErrorToHTML(...args)\n  }\n\n  async render404(...args: Parameters<NextWrapperServer['render404']>) {\n    return this.server.render404(...args)\n  }\n\n  async close() {\n    await Promise.allSettled([\n      this.init?.server.close(),\n      this.cleanupListeners?.runAll(),\n    ])\n  }\n}\n\n// This file is used for when users run `require('next')`\nfunction createServer(\n  options: NextServerOptions & {\n    turbo?: boolean\n    turbopack?: boolean\n  }\n): NextWrapperServer {\n  if (options && (options.turbo || options.turbopack)) {\n    process.env.TURBOPACK = '1'\n  }\n  // The package is used as a TypeScript plugin.\n  if (\n    options &&\n    'typescript' in options &&\n    'version' in (options as any).typescript\n  ) {\n    const pluginMod: typeof import('./next-typescript') = require('./next-typescript')\n    return pluginMod.createTSPlugin(\n      options as any\n    ) as unknown as NextWrapperServer\n  }\n\n  if (options == null) {\n    throw new Error(\n      'The server has not been instantiated properly. https://nextjs.org/docs/messages/invalid-server-options'\n    )\n  }\n\n  if (\n    !('isNextDevCommand' in options) &&\n    process.env.NODE_ENV &&\n    !['production', 'development', 'test'].includes(process.env.NODE_ENV)\n  ) {\n    log.warn(NON_STANDARD_NODE_ENV)\n  }\n\n  if (options.dev && typeof options.dev !== 'boolean') {\n    console.warn(\n      \"Warning: 'dev' is not a boolean which could introduce unexpected behavior. https://nextjs.org/docs/messages/invalid-server-options\"\n    )\n  }\n\n  // When the caller is a custom server (using next()).\n  if (options.customServer !== false) {\n    const dir = resolve(options.dir || '.')\n\n    return new NextCustomServer({\n      ...options,\n      dir,\n    })\n  }\n\n  // When the caller is Next.js internals (i.e. render worker, start server, etc)\n  return new NextServer(options)\n}\n\n// Support commonjs `require('next')`\nmodule.exports = createServer\n// exports = module.exports\n\n// Support `import next from 'next'`\nexport default createServer\n"], "names": ["NextServer", "ServerImpl", "getServerImpl", "undefined", "Promise", "resolve", "require", "default", "SYMBOL_LOAD_CONFIG", "Symbol", "constructor", "options", "hostname", "port", "getRequestHandler", "req", "res", "parsedUrl", "getTracer", "trace", "NextServerSpan", "requestHandler", "getServerRequestHandler", "getUpgradeHandler", "socket", "head", "server", "getServer", "handleUpgrade", "apply", "setAssetPrefix", "assetPrefix", "preparedAssetPrefix", "logError", "args", "render", "renderToHTML", "renderError", "renderErrorToHTML", "render404", "prepare", "serverFields", "Object", "assign", "dev", "close", "createServer", "ServerImplementation", "dir", "config", "loadConfig", "PHASE_DEVELOPMENT_SERVER", "PHASE_PRODUCTION_SERVER", "customConfig", "conf", "silent", "process", "env", "NODE_ENV", "serializedConfig", "path", "join", "SERVER_FILES_MANIFEST", "experimental", "isExperimentalCompile", "_", "serverPromise", "then", "output", "__NEXT_PRIVATE_STANDALONE_CONFIG", "log", "warn", "Error", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "reqHandlerPromise", "wrap", "bind", "NextCustomServer", "didWebSocketSetup", "getInit", "init", "upgradeHandler", "getRequestHandlers", "onDevServerCleanup", "cleanupListeners", "AsyncCallbackSet", "add", "initResult", "isDev", "minimalMode", "quiet", "setupWebSocketHandler", "customServer", "_req", "on", "httpServer", "url", "formatUrl", "pathname", "query", "startsWith", "console", "error", "allSettled", "runAll", "turbo", "turbopack", "TURBOPACK", "typescript", "pluginMod", "createTSPlugin", "includes", "NON_STANDARD_NODE_ENV", "module", "exports"], "mappings": ";;;;;;;;;;;;;;;IAsGaA,UAAU;eAAVA;;IAwZb,2BAA2B;IAE3B,oCAAoC;IACpC,OAA2B;eAA3B;;;QAvfO;QACA;6DAGc;+DACE;8DACO;2BACQ;4BAI/B;wBAEmB;4BACK;2BACL;kCAGO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEjC,IAAIC;AAEJ,MAAMC,gBAAgB;IACpB,IAAID,eAAeE,WAAW;QAC5BF,aAAa,AAAC,CAAA,MAAMG,QAAQC,OAAO,CAACC,QAAQ,iBAAgB,EAAGC,OAAO;IACxE;IACA,OAAON;AACT;AAqBA,MAAMO,qBAAqBC,OAAO;AA4C3B,MAAMT;IASXU,YAAYC,OAA0B,CAAE;QACtC,IAAI,CAACA,OAAO,GAAGA;IACjB;IAEA,IAAIC,WAAW;QACb,OAAO,IAAI,CAACD,OAAO,CAACC,QAAQ;IAC9B;IAEA,IAAIC,OAAO;QACT,OAAO,IAAI,CAACF,OAAO,CAACE,IAAI;IAC1B;IAEAC,oBAAoC;QAClC,OAAO,OACLC,KACAC,KACAC;YAEA,OAAOC,IAAAA,iBAAS,IAAGC,KAAK,CAACC,0BAAc,CAACN,iBAAiB,EAAE;gBACzD,MAAMO,iBAAiB,MAAM,IAAI,CAACC,uBAAuB;gBACzD,OAAOD,eAAeN,KAAKC,KAAKC;YAClC;QACF;IACF;IAEAM,oBAAoC;QAClC,OAAO,OAAOR,KAAsBS,QAAaC;YAC/C,MAAMC,SAAS,MAAM,IAAI,CAACC,SAAS;YACnC,mDAAmD;YACnD,uBAAuB;YACvB,OAAOD,OAAOE,aAAa,CAACC,KAAK,CAACH,QAAQ;gBAACX;gBAAKS;gBAAQC;aAAK;QAC/D;IACF;IAEAK,eAAeC,WAAmB,EAAE;QAClC,IAAI,IAAI,CAACL,MAAM,EAAE;YACf,IAAI,CAACA,MAAM,CAACI,cAAc,CAACC;QAC7B,OAAO;YACL,IAAI,CAACC,mBAAmB,GAAGD;QAC7B;IACF;IAEAE,SAAS,GAAGC,IAA+C,EAAE;QAC3D,IAAI,IAAI,CAACR,MAAM,EAAE;YACf,IAAI,CAACA,MAAM,CAACO,QAAQ,IAAIC;QAC1B;IACF;IAEA,MAAMC,OAAO,GAAGD,IAA6C,EAAE;QAC7D,MAAMR,SAAS,MAAM,IAAI,CAACC,SAAS;QACnC,OAAOD,OAAOS,MAAM,IAAID;IAC1B;IAEA,MAAME,aAAa,GAAGF,IAAmD,EAAE;QACzE,MAAMR,SAAS,MAAM,IAAI,CAACC,SAAS;QACnC,OAAOD,OAAOU,YAAY,IAAIF;IAChC;IAEA,MAAMG,YAAY,GAAGH,IAAkD,EAAE;QACvE,MAAMR,SAAS,MAAM,IAAI,CAACC,SAAS;QACnC,OAAOD,OAAOW,WAAW,IAAIH;IAC/B;IAEA,MAAMI,kBACJ,GAAGJ,IAAwD,EAC3D;QACA,MAAMR,SAAS,MAAM,IAAI,CAACC,SAAS;QACnC,OAAOD,OAAOY,iBAAiB,IAAIJ;IACrC;IAEA,MAAMK,UAAU,GAAGL,IAAgD,EAAE;QACnE,MAAMR,SAAS,MAAM,IAAI,CAACC,SAAS;QACnC,OAAOD,OAAOa,SAAS,IAAIL;IAC7B;IAEA,MAAMM,QAAQC,YAA2B,EAAE;QACzC,MAAMf,SAAS,MAAM,IAAI,CAACC,SAAS;QAEnC,IAAIc,cAAc;YAChBC,OAAOC,MAAM,CAACjB,QAAQe;QACxB;QACA,iDAAiD;QACjD,oDAAoD;QACpD,IAAI,IAAI,CAAC9B,OAAO,CAACiC,GAAG,EAAE;YACpB,MAAMlB,OAAOc,OAAO;QACtB;IACF;IAEA,MAAMK,QAAQ;QACZ,IAAI,IAAI,CAACnB,MAAM,EAAE;YACf,MAAM,IAAI,CAACA,MAAM,CAACmB,KAAK;QACzB;IACF;IAEA,MAAcC,aACZnC,OAAyC,EAChB;QACzB,IAAIoC;QACJ,IAAIpC,QAAQiC,GAAG,EAAE;YACfG,uBAAuBzC,QAAQ,yBAC5BC,OAAO;QACZ,OAAO;YACLwC,uBAAuB,MAAM7C;QAC/B;QACA,MAAMwB,SAAS,IAAIqB,qBAAqBpC;QAExC,OAAOe;IACT;IAEA,MAAc,CAAClB,mBAAmB,GAAG;QACnC,MAAMwC,MAAM3C,IAAAA,aAAO,EAAC,IAAI,CAACM,OAAO,CAACqC,GAAG,IAAI;QAExC,MAAMC,SAAS,MAAMC,IAAAA,eAAU,EAC7B,IAAI,CAACvC,OAAO,CAACiC,GAAG,GAAGO,oCAAwB,GAAGC,mCAAuB,EACrEJ,KACA;YACEK,cAAc,IAAI,CAAC1C,OAAO,CAAC2C,IAAI;YAC/BC,QAAQ;QACV;QAGF,+CAA+C;QAC/C,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;YACzC,IAAI;gBACF,MAAMC,mBAAmBrD,QACvBsD,aAAI,CAACC,IAAI,CAACb,KAAK,SAASc,iCAAqB,GAC7Cb,MAAM;gBAER,kCAAkC;gBAClCA,OAAOc,YAAY,CAACC,qBAAqB,GACvCL,iBAAiBI,YAAY,CAACC,qBAAqB;YACvD,EAAE,OAAOC,GAAG;YACV,kDAAkD;YAClD,oDAAoD;YACpD,sBAAsB;YACxB;QACF;QAEA,OAAOhB;IACT;IAEA,MAActB,YAAY;QACxB,IAAI,CAAC,IAAI,CAACuC,aAAa,EAAE;YACvB,IAAI,CAACA,aAAa,GAAG,IAAI,CAAC1D,mBAAmB,GAAG2D,IAAI,CAAC,OAAOb;gBAC1D,IAAI,CAAC,IAAI,CAAC3C,OAAO,CAACiC,GAAG,EAAE;oBACrB,IAAIU,KAAKc,MAAM,KAAK,cAAc;wBAChC,IAAI,CAACZ,QAAQC,GAAG,CAACY,gCAAgC,EAAE;4BACjDC,KAAIC,IAAI,CACN,CAAC,kHAAkH,CAAC;wBAExH;oBACF,OAAO,IAAIjB,KAAKc,MAAM,KAAK,UAAU;wBACnC,MAAM,qBAEL,CAFK,IAAII,MACR,CAAC,mGAAmG,CAAC,GADjG,qBAAA;mCAAA;wCAAA;0CAAA;wBAEN;oBACF;gBACF;gBAEA,IAAI,CAAC9C,MAAM,GAAG,MAAM,IAAI,CAACoB,YAAY,CAAC;oBACpC,GAAG,IAAI,CAACnC,OAAO;oBACf2C;gBACF;gBACA,IAAI,IAAI,CAACtB,mBAAmB,EAAE;oBAC5B,IAAI,CAACN,MAAM,CAACI,cAAc,CAAC,IAAI,CAACE,mBAAmB;gBACrD;gBACA,OAAO,IAAI,CAACN,MAAM;YACpB;QACF;QACA,OAAO,IAAI,CAACwC,aAAa;IAC3B;IAEA,MAAc5C,0BAA0B;QACtC,IAAI,IAAI,CAACmD,UAAU,EAAE,OAAO,IAAI,CAACA,UAAU;QAE3C,mCAAmC;QACnC,IAAI,CAAC,IAAI,CAACC,iBAAiB,EAAE;YAC3B,IAAI,CAACA,iBAAiB,GAAG,IAAI,CAAC/C,SAAS,GAAGwC,IAAI,CAAC,CAACzC;gBAC9C,IAAI,CAAC+C,UAAU,GAAGvD,IAAAA,iBAAS,IAAGyD,IAAI,CAChCvD,0BAAc,CAACE,uBAAuB,EACtCI,OAAOZ,iBAAiB,GAAG8D,IAAI,CAAClD;gBAElC,OAAO,IAAI,CAACgD,iBAAiB;gBAC7B,OAAO,IAAI,CAACD,UAAU;YACxB;QACF;QACA,OAAO,IAAI,CAACC,iBAAiB;IAC/B;AACF;AAEA,+EAA+E,GAC/E,MAAMG;IAQJnE,YAAYC,OAA0B,CAAE;aAPhCmE,oBAA6B;QAQnC,IAAI,CAACnE,OAAO,GAAGA;IACjB;IAEUoE,UAAU;QAClB,IAAI,CAAC,IAAI,CAACC,IAAI,EAAE;YACd,MAAM,qBAEL,CAFK,IAAIR,MACR,8DADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QACA,OAAO,IAAI,CAACQ,IAAI;IAClB;IAEA,IAAc3D,iBAAiB;QAC7B,OAAO,IAAI,CAAC0D,OAAO,GAAG1D,cAAc;IACtC;IACA,IAAc4D,iBAAiB;QAC7B,OAAO,IAAI,CAACF,OAAO,GAAGE,cAAc;IACtC;IACA,IAAcvD,SAAS;QACrB,OAAO,IAAI,CAACqD,OAAO,GAAGrD,MAAM;IAC9B;IAEA,IAAId,WAAW;QACb,OAAO,IAAI,CAACD,OAAO,CAACC,QAAQ;IAC9B;IAEA,IAAIC,OAAO;QACT,OAAO,IAAI,CAACF,OAAO,CAACE,IAAI;IAC1B;IAEA,MAAM2B,UAAU;QACd,MAAM,EAAE0C,kBAAkB,EAAE,GAC1B5E,QAAQ;QAEV,IAAI6E;QACJ,IAAI,IAAI,CAACxE,OAAO,CAACiC,GAAG,EAAE;YACpB,IAAI,CAACwC,gBAAgB,GAAG,IAAIC,kCAAgB;YAC5CF,qBAAqB,IAAI,CAACC,gBAAgB,CAACE,GAAG,CAACV,IAAI,CAAC,IAAI,CAACQ,gBAAgB;QAC3E;QAEA,MAAMG,aAAa,MAAML,mBAAmB;YAC1ClC,KAAK,IAAI,CAACrC,OAAO,CAACqC,GAAG;YACrBnC,MAAM,IAAI,CAACF,OAAO,CAACE,IAAI,IAAI;YAC3B2E,OAAO,CAAC,CAAC,IAAI,CAAC7E,OAAO,CAACiC,GAAG;YACzBuC;YACAvE,UAAU,IAAI,CAACD,OAAO,CAACC,QAAQ,IAAI;YACnC6E,aAAa,IAAI,CAAC9E,OAAO,CAAC8E,WAAW;YACrCC,OAAO,IAAI,CAAC/E,OAAO,CAAC+E,KAAK;QAC3B;QACA,IAAI,CAACV,IAAI,GAAGO;IACd;IAEQI,sBACNC,YAAoC,EACpCC,IAAsB,EACtB;QACA,IAAI,CAAC,IAAI,CAACf,iBAAiB,EAAE;gBAEKe;YADhC,IAAI,CAACf,iBAAiB,GAAG;YACzBc,eAAeA,iBAAiBC,yBAAAA,cAAAA,KAAMrE,MAAM,qBAAb,AAACqE,YAAsBnE,MAAM;YAE5D,IAAIkE,cAAc;gBAChBA,aAAaE,EAAE,CAAC,WAAW,OAAO/E,KAAKS,QAAQC;oBAC7C,IAAI,CAACwD,cAAc,CAAClE,KAAKS,QAAQC;gBACnC;YACF;QACF;IACF;IAEAX,oBAAoC;QAClC,OAAO,OACLC,KACAC,KACAC;YAEA,IAAI,CAAC0E,qBAAqB,CAAC,IAAI,CAAChF,OAAO,CAACoF,UAAU,EAAEhF;YAEpD,IAAIE,WAAW;gBACbF,IAAIiF,GAAG,GAAGC,IAAAA,oBAAS,EAAChF;YACtB;YAEA,OAAO,IAAI,CAACI,cAAc,CAACN,KAAKC;QAClC;IACF;IAEA,MAAMmB,OAAO,GAAGD,IAA6C,EAAE;QAC7D,IAAI,CAACnB,KAAKC,KAAKkF,UAAUC,OAAOlF,UAAU,GAAGiB;QAC7C,IAAI,CAACyD,qBAAqB,CAAC,IAAI,CAAChF,OAAO,CAACoF,UAAU,EAAEhF;QAEpD,IAAI,CAACmF,SAASE,UAAU,CAAC,MAAM;YAC7BC,QAAQC,KAAK,CAAC,CAAC,8BAA8B,EAAEJ,SAAS,CAAC,CAAC;YAC1DA,WAAW,CAAC,CAAC,EAAEA,UAAU;QAC3B;QACAA,WAAWA,aAAa,WAAW,MAAMA;QAEzCnF,IAAIiF,GAAG,GAAGC,IAAAA,oBAAS,EAAC;YAClB,GAAGhF,SAAS;YACZiF;YACAC;QACF;QAEA,MAAM,IAAI,CAAC9E,cAAc,CAACN,KAAwBC;QAClD;IACF;IAEAc,eAAeC,WAAmB,EAAQ;QACxC,IAAI,CAACL,MAAM,CAACI,cAAc,CAACC;IAC7B;IAEAR,oBAAoC;QAClC,OAAO,IAAI,CAACG,MAAM,CAACH,iBAAiB;IACtC;IAEAU,SAAS,GAAGC,IAA+C,EAAE;QAC3D,IAAI,CAACR,MAAM,CAACO,QAAQ,IAAIC;IAC1B;IAEA,MAAME,aAAa,GAAGF,IAAmD,EAAE;QACzE,OAAO,IAAI,CAACR,MAAM,CAACU,YAAY,IAAIF;IACrC;IAEA,MAAMG,YAAY,GAAGH,IAAkD,EAAE;QACvE,OAAO,IAAI,CAACR,MAAM,CAACW,WAAW,IAAIH;IACpC;IAEA,MAAMI,kBACJ,GAAGJ,IAAwD,EAC3D;QACA,OAAO,IAAI,CAACR,MAAM,CAACY,iBAAiB,IAAIJ;IAC1C;IAEA,MAAMK,UAAU,GAAGL,IAAgD,EAAE;QACnE,OAAO,IAAI,CAACR,MAAM,CAACa,SAAS,IAAIL;IAClC;IAEA,MAAMW,QAAQ;YAEV,YACA;QAFF,MAAMzC,QAAQmG,UAAU,CAAC;aACvB,aAAA,IAAI,CAACvB,IAAI,qBAAT,WAAWtD,MAAM,CAACmB,KAAK;aACvB,yBAAA,IAAI,CAACuC,gBAAgB,qBAArB,uBAAuBoB,MAAM;SAC9B;IACH;AACF;AAEA,yDAAyD;AACzD,SAAS1D,aACPnC,OAGC;IAED,IAAIA,WAAYA,CAAAA,QAAQ8F,KAAK,IAAI9F,QAAQ+F,SAAS,AAAD,GAAI;QACnDlD,QAAQC,GAAG,CAACkD,SAAS,GAAG;IAC1B;IACA,8CAA8C;IAC9C,IACEhG,WACA,gBAAgBA,WAChB,aAAa,AAACA,QAAgBiG,UAAU,EACxC;QACA,MAAMC,YAAgDvG,QAAQ;QAC9D,OAAOuG,UAAUC,cAAc,CAC7BnG;IAEJ;IAEA,IAAIA,WAAW,MAAM;QACnB,MAAM,qBAEL,CAFK,IAAI6D,MACR,2GADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,IACE,CAAE,CAAA,sBAAsB7D,OAAM,KAC9B6C,QAAQC,GAAG,CAACC,QAAQ,IACpB,CAAC;QAAC;QAAc;QAAe;KAAO,CAACqD,QAAQ,CAACvD,QAAQC,GAAG,CAACC,QAAQ,GACpE;QACAY,KAAIC,IAAI,CAACyC,gCAAqB;IAChC;IAEA,IAAIrG,QAAQiC,GAAG,IAAI,OAAOjC,QAAQiC,GAAG,KAAK,WAAW;QACnDyD,QAAQ9B,IAAI,CACV;IAEJ;IAEA,qDAAqD;IACrD,IAAI5D,QAAQiF,YAAY,KAAK,OAAO;QAClC,MAAM5C,MAAM3C,IAAAA,aAAO,EAACM,QAAQqC,GAAG,IAAI;QAEnC,OAAO,IAAI6B,iBAAiB;YAC1B,GAAGlE,OAAO;YACVqC;QACF;IACF;IAEA,+EAA+E;IAC/E,OAAO,IAAIhD,WAAWW;AACxB;AAEA,qCAAqC;AACrCsG,OAAOC,OAAO,GAAGpE;MAIjB,WAAeA"}