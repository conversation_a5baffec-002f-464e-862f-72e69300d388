{"version": 3, "sources": ["../../../src/server/lib/implicit-tags.ts"], "sourcesContent": ["import { NEXT_CACHE_IMPLICIT_TAG_ID } from '../../lib/constants'\nimport type { FallbackRouteParams } from '../request/fallback-params'\n\nconst getDerivedTags = (pathname: string): string[] => {\n  const derivedTags: string[] = [`/layout`]\n\n  // we automatically add the current path segments as tags\n  // for revalidatePath handling\n  if (pathname.startsWith('/')) {\n    const pathnameParts = pathname.split('/')\n\n    for (let i = 1; i < pathnameParts.length + 1; i++) {\n      let curPathname = pathnameParts.slice(0, i).join('/')\n\n      if (curPathname) {\n        // all derived tags other than the page are layout tags\n        if (!curPathname.endsWith('/page') && !curPathname.endsWith('/route')) {\n          curPathname = `${curPathname}${\n            !curPathname.endsWith('/') ? '/' : ''\n          }layout`\n        }\n        derivedTags.push(curPathname)\n      }\n    }\n  }\n  return derivedTags\n}\n\nexport function getImplicitTags(\n  page: string,\n  url: {\n    pathname: string\n    search?: string\n  },\n  fallbackRouteParams: null | FallbackRouteParams\n) {\n  // TODO: Cache the result\n  const newTags: string[] = []\n  const hasFallbackRouteParams =\n    fallbackRouteParams && fallbackRouteParams.size > 0\n\n  // Add the derived tags from the page.\n  const derivedTags = getDerivedTags(page)\n  for (let tag of derivedTags) {\n    tag = `${NEXT_CACHE_IMPLICIT_TAG_ID}${tag}`\n    newTags.push(tag)\n  }\n\n  // Add the tags from the pathname. If the route has unknown params, we don't\n  // want to add the pathname as a tag, as it will be invalid.\n  if (url.pathname && !hasFallbackRouteParams) {\n    const tag = `${NEXT_CACHE_IMPLICIT_TAG_ID}${url.pathname}`\n    newTags.push(tag)\n  }\n\n  return newTags\n}\n"], "names": ["getImplicitTags", "getDerivedTags", "pathname", "derivedTags", "startsWith", "pathnameParts", "split", "i", "length", "curPathname", "slice", "join", "endsWith", "push", "page", "url", "fallbackRouteParams", "newTags", "hasFallbackRouteParams", "size", "tag", "NEXT_CACHE_IMPLICIT_TAG_ID"], "mappings": ";;;;+BA4BgBA;;;eAAAA;;;2BA5B2B;AAG3C,MAAMC,iBAAiB,CAACC;IACtB,MAAMC,cAAwB;QAAC,CAAC,OAAO,CAAC;KAAC;IAEzC,yDAAyD;IACzD,8BAA8B;IAC9B,IAAID,SAASE,UAAU,CAAC,MAAM;QAC5B,MAAMC,gBAAgBH,SAASI,KAAK,CAAC;QAErC,IAAK,IAAIC,IAAI,GAAGA,IAAIF,cAAcG,MAAM,GAAG,GAAGD,IAAK;YACjD,IAAIE,cAAcJ,cAAcK,KAAK,CAAC,GAAGH,GAAGI,IAAI,CAAC;YAEjD,IAAIF,aAAa;gBACf,uDAAuD;gBACvD,IAAI,CAACA,YAAYG,QAAQ,CAAC,YAAY,CAACH,YAAYG,QAAQ,CAAC,WAAW;oBACrEH,cAAc,GAAGA,cACf,CAACA,YAAYG,QAAQ,CAAC,OAAO,MAAM,GACpC,MAAM,CAAC;gBACV;gBACAT,YAAYU,IAAI,CAACJ;YACnB;QACF;IACF;IACA,OAAON;AACT;AAEO,SAASH,gBACdc,IAAY,EACZC,GAGC,EACDC,mBAA+C;IAE/C,yBAAyB;IACzB,MAAMC,UAAoB,EAAE;IAC5B,MAAMC,yBACJF,uBAAuBA,oBAAoBG,IAAI,GAAG;IAEpD,sCAAsC;IACtC,MAAMhB,cAAcF,eAAea;IACnC,KAAK,IAAIM,OAAOjB,YAAa;QAC3BiB,MAAM,GAAGC,qCAA0B,GAAGD,KAAK;QAC3CH,QAAQJ,IAAI,CAACO;IACf;IAEA,4EAA4E;IAC5E,4DAA4D;IAC5D,IAAIL,IAAIb,QAAQ,IAAI,CAACgB,wBAAwB;QAC3C,MAAME,MAAM,GAAGC,qCAA0B,GAAGN,IAAIb,QAAQ,EAAE;QAC1De,QAAQJ,IAAI,CAACO;IACf;IAEA,OAAOH;AACT"}