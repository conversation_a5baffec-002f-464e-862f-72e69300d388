(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[365],{9342:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>N});var r=a(95155),t=a(12115),l=a(62177),n=a(90221),i=a(78749),o=a(92657),d=a(81838),c=a(93176),m=a(66146),u=a(58096),x=a(81495),p=a(82842),j=a(59434),h=a(35695),v=a(56671);function f(){let e=(0,h.useRouter)(),[s,a]=(0,t.useState)(!1),[f,g,w]=(0,p.lT)(),{control:b,handleSubmit:y,formState:{errors:N,isSubmitting:I}}=(0,l.mN)({resolver:(0,n.u)(d.X5),defaultValues:{email:"",password:""}});async function _(s){let a=await (0,j.G)("/auth/jwt/create/",s,"POST");if(200===a.status){let s=await a.json();g("access",s.access),g("refresh",s.refresh),v.o.success("تم تسجيل الدخول بنجاح"),e.push("/")}else v.o.error("فشل تسجيل الدخول، تأكد من صحة البيانات المدخلة")}return(0,r.jsxs)("form",{onSubmit:y(_),className:"space-y-6 mt-6",children:[(0,r.jsx)(l.xI,{name:"email",control:b,render:e=>{var s;let{field:a}=e;return(0,r.jsx)(c.r,{...a,type:"tel",label:"رقم الهاتف",variant:"bordered",isInvalid:!!N.email,errorMessage:null===(s=N.email)||void 0===s?void 0:s.message})}}),(0,r.jsx)(l.xI,{name:"password",control:b,render:e=>{var t;let{field:l}=e;return(0,r.jsx)(c.r,{...l,type:s?"text":"password",label:"كلمة المرور",variant:"bordered",isInvalid:!!N.password,errorMessage:null===(t=N.password)||void 0===t?void 0:t.message,endContent:(0,r.jsx)("button",{type:"button",className:"h-full pl-2",onClick:()=>a(!s),children:s?(0,r.jsx)(i.A,{size:20}):(0,r.jsx)(o.A,{size:20})})})}}),(0,r.jsx)("div",{className:"flex items-center justify-end",children:(0,r.jsx)(m.T,{as:x.h,href:"/auth/forget-password",variant:"light",className:"px-0 font-normal",children:"هل نسيت كلمة المرور؟"})}),(0,r.jsx)(m.T,{type:"submit",color:"primary",className:(0,u.cn)("w-full",I?"opacity-50":""),disabled:I,isLoading:I,children:I?"جاري تسجيل الدخول...":"تسجيل الدخول"})]})}function g(){var e;let[s,a]=(0,p.lT)(),u=(0,h.useRouter)(),[x,v]=(0,t.useState)(!1),[f,g]=(0,t.useState)(!1),[w,b]=(0,t.useState)(""),{control:y,handleSubmit:N,formState:{errors:I,isSubmitting:_}}=(0,l.mN)({resolver:(0,n.u)(d.Sd),defaultValues:{first_name:"",last_name:"",email:"",password:"",password2:""}});async function S(e){let s=await (0,j.G)("/users/",e,"POST"),r=await s.json();201!==s.status?b(Object.values(r)[0][0]):(a("access",r.access),a("refresh",r.refresh),u.push("/"))}return(0,r.jsxs)(r.Fragment,{children:[w&&(0,r.jsx)("div",{className:"p-3 mb-4 text-sm text-red-500 bg-red-50 rounded-md",children:(null===(e=w[0])||void 0===e?void 0:e.toUpperCase())+w.slice(1)}),(0,r.jsxs)("form",{onSubmit:N(S),className:"space-y-6 mt-6",children:[(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsx)(l.xI,{name:"first_name",control:y,render:e=>{var s;let{field:a}=e;return(0,r.jsx)(c.r,{...a,label:"الاسم الاول",variant:"bordered",isInvalid:!!I.first_name,errorMessage:null===(s=I.first_name)||void 0===s?void 0:s.message})}}),(0,r.jsx)(l.xI,{name:"last_name",control:y,render:e=>{var s;let{field:a}=e;return(0,r.jsx)(c.r,{...a,label:"اسم العائلة",variant:"bordered",isInvalid:!!I.last_name,errorMessage:null===(s=I.last_name)||void 0===s?void 0:s.message})}})]}),(0,r.jsx)(l.xI,{name:"email",control:y,render:e=>{var s;let{field:a}=e;return(0,r.jsx)(c.r,{...a,type:"email",label:"البريد الالكتروني",variant:"bordered",isInvalid:!!I.email,errorMessage:null===(s=I.email)||void 0===s?void 0:s.message})}}),(0,r.jsx)(l.xI,{name:"password",control:y,render:e=>{var s;let{field:a}=e;return(0,r.jsx)(c.r,{...a,type:x?"text":"password",label:"كلمة المرور",variant:"bordered",isInvalid:!!I.password,errorMessage:null===(s=I.password)||void 0===s?void 0:s.message,endContent:(0,r.jsx)("button",{type:"button",className:"h-full pl-2",onClick:()=>v(!x),children:x?(0,r.jsx)(i.A,{size:20}):(0,r.jsx)(o.A,{size:20})})})}}),(0,r.jsx)(l.xI,{name:"password2",control:y,render:e=>{var s;let{field:a}=e;return(0,r.jsx)(c.r,{...a,type:f?"text":"password",label:"تأكيد كلمة المرور",variant:"bordered",isInvalid:!!I.password2,errorMessage:null===(s=I.password2)||void 0===s?void 0:s.message,endContent:(0,r.jsx)("button",{type:"button",className:"h-full pl-2",onClick:()=>g(!f),children:f?(0,r.jsx)(i.A,{size:20}):(0,r.jsx)(o.A,{size:20})})})}}),(0,r.jsx)(m.T,{type:"submit",color:"primary",className:(0,j.cn)("w-full",_?"opacity-50":""),disabled:_,children:"سجل الآن"})]})]})}var w=a(90262),b=a(87036),y=a(66766);function N(e){let{}=e,s=(0,h.useRouter)(),a=async()=>{console.log("Hitted");let e=await (0,j.G)("/auth/o/google-oauth2/?redirect_uri=http://localhost:3000",null,"GET"),a=(await e.json()).authorization_url;s.push(a)};return(0,r.jsx)(r.Fragment,{children:(0,r.jsxs)("div",{className:"w-full max-w-md space-y-8",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold",children:"مرحباً بعودتك!"}),(0,r.jsx)("p",{className:"text-gray-500 mt-2",children:"مرحباً بعودتك، من فضلك ادخل بياناتك."})]}),(0,r.jsxs)(w.r,{"aria-label":"Auth options",color:"primary",variant:"underlined",className:"w-full",children:[(0,r.jsx)(b.i,{title:"تسجيل الدخول",children:(0,r.jsx)(f,{})},"login"),(0,r.jsx)(b.i,{title:"انشاء حساب ",children:(0,r.jsx)(g,{})},"signup")]}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,r.jsx)("span",{className:"w-full border-t"})}),(0,r.jsx)("div",{className:"relative flex justify-center text-xs uppercase",children:(0,r.jsx)("span",{className:"bg-white px-2 text-gray-500",children:"او اتصل باستخدام"})})]}),(0,r.jsx)("div",{className:"grid grid-cols-3 gap-4",children:(0,r.jsx)(m.T,{variant:"ghost",onPress:a,className:"border-1 border-gray-200",children:(0,r.jsx)(y.default,{src:"/google-icon.png",alt:"Google",fill:!0})})})]})})}},59434:(e,s,a)=>{"use strict";a.d(s,{G:()=>n,cn:()=>l});var r=a(52596),t=a(39688);function l(){for(var e=arguments.length,s=Array(e),a=0;a<e;a++)s[a]=arguments[a];return(0,t.QP)((0,r.$)(s))}async function n(e,s){let a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"GET",r=arguments.length>3?arguments[3]:void 0,t={"Content-Type":"application/json"};r&&(t.Authorization="Bearer ".concat(r));let l={method:a,headers:t,next:{revalidate:60}};s&&"GET"!==a&&(l.body=JSON.stringify(s));try{let s="".concat("http://localhost:8000").concat(e);console.log("Fetching: ".concat(s));let a=await fetch(s,l);return a.ok||console.warn("API request failed: ".concat(s," returned status ").concat(a.status)),a}catch(e){throw console.error("API request failed:",e),e}}},81838:(e,s,a)=>{"use strict";a.d(s,{Ie:()=>n,Sd:()=>l,X5:()=>t,oW:()=>i});var r=a(55594);let t=r.Ik({email:r.Yj().email({message:"البريد الإلكتروني غير صالح"}),password:r.Yj().min(1,{message:"كلمة المرور مطلوبة"})}),l=r.Ik({first_name:r.Yj().min(2,{message:"الاسم الأول يجب أن يكون على الأقل حرفين"}),last_name:r.Yj().min(2,{message:"اسم العائلة يجب أن يكون على الأقل حرفين"}),email:r.Yj().email({message:"البريد الإلكتروني غير صالح"}),password:r.Yj().min(8,{message:"كلمة المرور يجب أن تكون على الأقل 8 أحرف"}),password2:r.Yj()}).refine(e=>e.password===e.password2,{message:"كلمات المرور غير متطابقة",path:["confirmPassword"]}),n=r.Ik({email:r.Yj().email({message:"البريد الإلكتروني غير صالح"}).optional().or(r.eu(""))}),i=r.Ik({password:r.Yj().min(8,{message:"كلمة المرور يجب أن تكون على الأقل 8 أحرف"}),confirmPassword:r.Yj()}).refine(e=>e.password===e.confirmPassword,{message:"كلمات المرور غير متطابقة",path:["confirmPassword"]})},91709:(e,s,a)=>{Promise.resolve().then(a.bind(a,9342))}},e=>{var s=s=>e(e.s=s);e.O(0,[146,314,688,671,829,686,842,161,63,373,441,684,358],()=>s(91709)),_N_E=e.O()}]);