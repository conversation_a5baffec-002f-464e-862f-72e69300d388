{"version": 3, "sources": ["../../../../src/build/webpack/plugins/flight-client-entry-plugin.ts"], "sourcesContent": ["import type {\n  CssImports,\n  ClientComponentImports,\n} from '../loaders/next-flight-client-entry-loader'\n\nimport { webpack } from 'next/dist/compiled/webpack/webpack'\nimport { parse, stringify } from 'querystring'\nimport path from 'path'\nimport { sources } from 'next/dist/compiled/webpack/webpack'\nimport {\n  getInvalidator,\n  getEntries,\n  EntryTypes,\n  getEntryKey,\n} from '../../../server/dev/on-demand-entry-handler'\nimport {\n  WEBPACK_LAYERS,\n  WEBPACK_RESOURCE_QUERIES,\n} from '../../../lib/constants'\nimport {\n  APP_CLIENT_INTERNALS,\n  BARREL_OPTIMIZATION_PREFIX,\n  COMPILER_NAMES,\n  DEFAULT_RUNTIME_WEBPACK,\n  EDGE_RUNTIME_WEBPACK,\n  SERVER_REFERENCE_MANIFEST,\n  UNDERSCORE_NOT_FOUND_ROUTE_ENTRY,\n} from '../../../shared/lib/constants'\nimport {\n  isClientComponentEntryModule,\n  isCSSMod,\n  regexCSS,\n} from '../loaders/utils'\nimport {\n  traverseModules,\n  forEachEntryModule,\n  formatBarrelOptimizedResource,\n  getModuleReferencesInOrder,\n} from '../utils'\nimport { normalizePathSep } from '../../../shared/lib/page-path/normalize-path-sep'\nimport { getProxiedPluginState } from '../../build-context'\nimport { PAGE_TYPES } from '../../../lib/page-types'\nimport { getModuleBuildInfo } from '../loaders/get-module-build-info'\nimport { getAssumedSourceType } from '../loaders/next-flight-loader'\nimport { isAppRouteRoute } from '../../../lib/is-app-route-route'\nimport { isMetadataRoute } from '../../../lib/metadata/is-metadata-route'\nimport type { MetadataRouteLoaderOptions } from '../loaders/next-metadata-route-loader'\nimport type { FlightActionEntryLoaderActions } from '../loaders/next-flight-action-entry-loader'\n\ninterface Options {\n  dev: boolean\n  appDir: string\n  isEdgeServer: boolean\n  encryptionKey: string\n}\n\nconst PLUGIN_NAME = 'FlightClientEntryPlugin'\n\ntype Actions = {\n  [actionId: string]: {\n    workers: {\n      [name: string]: { moduleId: string | number; async: boolean }\n    }\n    // Record which layer the action is in (rsc or sc_action), in the specific entry.\n    layer: {\n      [name: string]: string\n    }\n  }\n}\n\ntype ActionIdNamePair = { id: string; exportedName: string }\n\nexport type ActionManifest = {\n  // Assign a unique encryption key during production build.\n  encryptionKey: string\n  node: Actions\n  edge: Actions\n}\n\nexport interface ModuleInfo {\n  moduleId: string | number\n  async: boolean\n}\n\nconst pluginState = getProxiedPluginState({\n  // A map to track \"action\" -> \"list of bundles\".\n  serverActions: {} as ActionManifest['node'],\n  edgeServerActions: {} as ActionManifest['edge'],\n\n  serverActionModules: {} as {\n    [workerName: string]: { server?: ModuleInfo; client?: ModuleInfo }\n  },\n\n  edgeServerActionModules: {} as {\n    [workerName: string]: { server?: ModuleInfo; client?: ModuleInfo }\n  },\n\n  ssrModules: {} as { [ssrModuleId: string]: ModuleInfo },\n  edgeSsrModules: {} as { [ssrModuleId: string]: ModuleInfo },\n\n  rscModules: {} as { [rscModuleId: string]: ModuleInfo },\n  edgeRscModules: {} as { [rscModuleId: string]: ModuleInfo },\n\n  injectedClientEntries: {} as Record<string, string>,\n})\n\nfunction deduplicateCSSImportsForEntry(mergedCSSimports: CssImports) {\n  // If multiple entry module connections are having the same CSS import,\n  // we only need to have one module to keep track of that CSS import.\n  // It is based on the fact that if a page or a layout is rendered in the\n  // given entry, all its parent layouts are always rendered too.\n  // This can avoid duplicate CSS imports in the generated CSS manifest,\n  // for example, if a page and its parent layout are both using the same\n  // CSS import, we only need to have the layout to keep track of that CSS\n  // import.\n  // To achieve this, we need to first collect all the CSS imports from\n  // every connection, and deduplicate them in the order of layers from\n  // top to bottom. The implementation can be generally described as:\n  // - Sort by number of `/` in the request path (the more `/`, the deeper)\n  // - When in the same depth, sort by the filename (template < layout < page and others)\n\n  // Sort the connections as described above.\n  const sortedCSSImports = Object.entries(mergedCSSimports).sort((a, b) => {\n    const [aPath] = a\n    const [bPath] = b\n\n    const aDepth = aPath.split('/').length\n    const bDepth = bPath.split('/').length\n\n    if (aDepth !== bDepth) {\n      return aDepth - bDepth\n    }\n\n    const aName = path.parse(aPath).name\n    const bName = path.parse(bPath).name\n\n    const indexA = ['template', 'layout'].indexOf(aName)\n    const indexB = ['template', 'layout'].indexOf(bName)\n\n    if (indexA === -1) return 1\n    if (indexB === -1) return -1\n    return indexA - indexB\n  })\n\n  const dedupedCSSImports: CssImports = {}\n  const trackedCSSImports = new Set<string>()\n  for (const [entryName, cssImports] of sortedCSSImports) {\n    for (const cssImport of cssImports) {\n      if (trackedCSSImports.has(cssImport)) continue\n\n      // Only track CSS imports that are in files that can inherit CSS.\n      const filename = path.parse(entryName).name\n      if (['template', 'layout'].includes(filename)) {\n        trackedCSSImports.add(cssImport)\n      }\n\n      if (!dedupedCSSImports[entryName]) {\n        dedupedCSSImports[entryName] = []\n      }\n      dedupedCSSImports[entryName].push(cssImport)\n    }\n  }\n\n  return dedupedCSSImports\n}\n\nexport class FlightClientEntryPlugin {\n  dev: boolean\n  appDir: string\n  encryptionKey: string\n  isEdgeServer: boolean\n  assetPrefix: string\n  webpackRuntime: string\n\n  constructor(options: Options) {\n    this.dev = options.dev\n    this.appDir = options.appDir\n    this.isEdgeServer = options.isEdgeServer\n    this.assetPrefix = !this.dev && !this.isEdgeServer ? '../' : ''\n    this.encryptionKey = options.encryptionKey\n    this.webpackRuntime = this.isEdgeServer\n      ? EDGE_RUNTIME_WEBPACK\n      : DEFAULT_RUNTIME_WEBPACK\n  }\n\n  apply(compiler: webpack.Compiler) {\n    compiler.hooks.finishMake.tapPromise(PLUGIN_NAME, (compilation) =>\n      this.createClientEntries(compiler, compilation)\n    )\n\n    compiler.hooks.afterCompile.tap(PLUGIN_NAME, (compilation) => {\n      const recordModule = (modId: string, mod: any) => {\n        // Match Resource is undefined unless an import is using the inline match resource syntax\n        // https://webpack.js.org/api/loaders/#inline-matchresource\n        const modPath = mod.matchResource || mod.resourceResolveData?.path\n        const modQuery = mod.resourceResolveData?.query || ''\n        // query is already part of mod.resource\n        // so it's only necessary to add it for matchResource or mod.resourceResolveData\n        const modResource = modPath\n          ? modPath.startsWith(BARREL_OPTIMIZATION_PREFIX)\n            ? formatBarrelOptimizedResource(mod.resource, modPath)\n            : modPath + modQuery\n          : mod.resource\n\n        if (typeof modId !== 'undefined' && modResource) {\n          if (mod.layer === WEBPACK_LAYERS.reactServerComponents) {\n            const key = path\n              .relative(compiler.context, modResource)\n              .replace(/\\/next\\/dist\\/esm\\//, '/next/dist/')\n\n            const moduleInfo: ModuleInfo = {\n              moduleId: modId,\n              async: compilation.moduleGraph.isAsync(mod),\n            }\n\n            if (this.isEdgeServer) {\n              pluginState.edgeRscModules[key] = moduleInfo\n            } else {\n              pluginState.rscModules[key] = moduleInfo\n            }\n          }\n        }\n\n        if (mod.layer !== WEBPACK_LAYERS.serverSideRendering) {\n          return\n        }\n\n        // Check mod resource to exclude the empty resource module like virtual module created by next-flight-client-entry-loader\n        if (typeof modId !== 'undefined' && modResource) {\n          // Note that this isn't that reliable as webpack is still possible to assign\n          // additional queries to make sure there's no conflict even using the `named`\n          // module ID strategy.\n          let ssrNamedModuleId = path.relative(compiler.context, modResource)\n\n          if (!ssrNamedModuleId.startsWith('.')) {\n            // TODO use getModuleId instead\n            ssrNamedModuleId = `./${normalizePathSep(ssrNamedModuleId)}`\n          }\n\n          const moduleInfo: ModuleInfo = {\n            moduleId: modId,\n            async: compilation.moduleGraph.isAsync(mod),\n          }\n\n          if (this.isEdgeServer) {\n            pluginState.edgeSsrModules[\n              ssrNamedModuleId.replace(/\\/next\\/dist\\/esm\\//, '/next/dist/')\n            ] = moduleInfo\n          } else {\n            pluginState.ssrModules[ssrNamedModuleId] = moduleInfo\n          }\n        }\n      }\n\n      traverseModules(compilation, (mod, _chunk, _chunkGroup, modId) => {\n        if (modId) recordModule(modId, mod)\n      })\n    })\n\n    compiler.hooks.make.tap(PLUGIN_NAME, (compilation) => {\n      compilation.hooks.processAssets.tapPromise(\n        {\n          name: PLUGIN_NAME,\n          stage: webpack.Compilation.PROCESS_ASSETS_STAGE_OPTIMIZE_HASH,\n        },\n        () => this.createActionAssets(compilation)\n      )\n    })\n  }\n\n  async createClientEntries(\n    compiler: webpack.Compiler,\n    compilation: webpack.Compilation\n  ) {\n    const addClientEntryAndSSRModulesList: Array<\n      ReturnType<typeof this.injectClientEntryAndSSRModules>\n    > = []\n    const createdSSRDependenciesForEntry: Record<\n      string,\n      ReturnType<typeof this.injectClientEntryAndSSRModules>[3][]\n    > = {}\n\n    const addActionEntryList: Array<ReturnType<typeof this.injectActionEntry>> =\n      []\n    const actionMapsPerEntry: Record<\n      string,\n      Map<string, ActionIdNamePair[]>\n    > = {}\n    const createdActionIds = new Set<string>()\n\n    // For each SC server compilation entry, we need to create its corresponding\n    // client component entry.\n    forEachEntryModule(compilation, ({ name, entryModule }) => {\n      const internalClientComponentEntryImports: ClientComponentImports = {}\n      const actionEntryImports = new Map<string, ActionIdNamePair[]>()\n      const clientEntriesToInject = []\n      const mergedCSSimports: CssImports = {}\n\n      for (const connection of getModuleReferencesInOrder(\n        entryModule,\n        compilation.moduleGraph\n      )) {\n        // Entry can be any user defined entry files such as layout, page, error, loading, etc.\n        let entryRequest = (\n          connection.dependency as unknown as webpack.NormalModule\n        ).request\n\n        if (entryRequest.endsWith(WEBPACK_RESOURCE_QUERIES.metadataRoute)) {\n          const { filePath, isDynamicRouteExtension } =\n            getMetadataRouteResource(entryRequest)\n\n          if (isDynamicRouteExtension === '1') {\n            entryRequest = filePath\n          }\n        }\n\n        const { clientComponentImports, actionImports, cssImports } =\n          this.collectComponentInfoFromServerEntryDependency({\n            entryRequest,\n            compilation,\n            resolvedModule: connection.resolvedModule,\n          })\n\n        actionImports.forEach(([dep, actions]) =>\n          actionEntryImports.set(dep, actions)\n        )\n\n        const isAbsoluteRequest = path.isAbsolute(entryRequest)\n\n        // Next.js internals are put into a separate entry.\n        if (!isAbsoluteRequest) {\n          Object.keys(clientComponentImports).forEach(\n            (value) => (internalClientComponentEntryImports[value] = new Set())\n          )\n          continue\n        }\n\n        // TODO-APP: Enable these lines. This ensures no entrypoint is created for layout/page when there are no client components.\n        // Currently disabled because it causes test failures in CI.\n        // if (clientImports.length === 0 && actionImports.length === 0) {\n        //   continue\n        // }\n\n        const relativeRequest = isAbsoluteRequest\n          ? path.relative(compilation.options.context!, entryRequest)\n          : entryRequest\n\n        // Replace file suffix as `.js` will be added.\n        let bundlePath = normalizePathSep(\n          relativeRequest.replace(/\\.[^.\\\\/]+$/, '').replace(/^src[\\\\/]/, '')\n        )\n\n        // For metadata routes, the entry name can be used as the bundle path,\n        // as it has been normalized already.\n        if (isMetadataRoute(bundlePath)) {\n          bundlePath = name\n        }\n\n        Object.assign(mergedCSSimports, cssImports)\n        clientEntriesToInject.push({\n          compiler,\n          compilation,\n          entryName: name,\n          clientComponentImports,\n          bundlePath,\n          absolutePagePath: entryRequest,\n        })\n\n        // The webpack implementation of writing the client reference manifest relies on all entrypoints writing a page.js even when there is no client components in the page.\n        // It needs the file in order to write the reference manifest for the path in the `.next/server` folder.\n        // TODO-APP: This could be better handled, however Turbopack does not have the same problem as we resolve client components in a single graph.\n        if (\n          name === `app${UNDERSCORE_NOT_FOUND_ROUTE_ENTRY}` &&\n          bundlePath === 'app/not-found'\n        ) {\n          clientEntriesToInject.push({\n            compiler,\n            compilation,\n            entryName: name,\n            clientComponentImports: {},\n            bundlePath: `app${UNDERSCORE_NOT_FOUND_ROUTE_ENTRY}`,\n            absolutePagePath: entryRequest,\n          })\n        }\n      }\n\n      // Make sure CSS imports are deduplicated before injecting the client entry\n      // and SSR modules.\n      const dedupedCSSImports = deduplicateCSSImportsForEntry(mergedCSSimports)\n      for (const clientEntryToInject of clientEntriesToInject) {\n        const injected = this.injectClientEntryAndSSRModules({\n          ...clientEntryToInject,\n          clientImports: {\n            ...clientEntryToInject.clientComponentImports,\n            ...(\n              dedupedCSSImports[clientEntryToInject.absolutePagePath] || []\n            ).reduce<ClientComponentImports>((res, curr) => {\n              res[curr] = new Set()\n              return res\n            }, {}),\n          },\n        })\n\n        // Track all created SSR dependencies for each entry from the server layer.\n        if (!createdSSRDependenciesForEntry[clientEntryToInject.entryName]) {\n          createdSSRDependenciesForEntry[clientEntryToInject.entryName] = []\n        }\n        createdSSRDependenciesForEntry[clientEntryToInject.entryName].push(\n          injected[3]\n        )\n\n        addClientEntryAndSSRModulesList.push(injected)\n      }\n\n      if (!isAppRouteRoute(name)) {\n        // Create internal app\n        addClientEntryAndSSRModulesList.push(\n          this.injectClientEntryAndSSRModules({\n            compiler,\n            compilation,\n            entryName: name,\n            clientImports: { ...internalClientComponentEntryImports },\n            bundlePath: APP_CLIENT_INTERNALS,\n          })\n        )\n      }\n\n      if (actionEntryImports.size > 0) {\n        if (!actionMapsPerEntry[name]) {\n          actionMapsPerEntry[name] = new Map()\n        }\n        actionMapsPerEntry[name] = new Map([\n          ...actionMapsPerEntry[name],\n          ...actionEntryImports,\n        ])\n      }\n    })\n\n    for (const [name, actionEntryImports] of Object.entries(\n      actionMapsPerEntry\n    )) {\n      addActionEntryList.push(\n        this.injectActionEntry({\n          compiler,\n          compilation,\n          actions: actionEntryImports,\n          entryName: name,\n          bundlePath: name,\n          createdActionIds,\n        })\n      )\n    }\n\n    // Invalidate in development to trigger recompilation\n    const invalidator = getInvalidator(compiler.outputPath)\n    // Check if any of the entry injections need an invalidation\n    if (\n      invalidator &&\n      addClientEntryAndSSRModulesList.some(\n        ([shouldInvalidate]) => shouldInvalidate === true\n      )\n    ) {\n      invalidator.invalidate([COMPILER_NAMES.client])\n    }\n\n    // Client compiler is invalidated before awaiting the compilation of the SSR\n    // and RSC client component entries so that the client compiler is running\n    // in parallel to the server compiler.\n    await Promise.all(\n      addClientEntryAndSSRModulesList.flatMap((addClientEntryAndSSRModules) => [\n        addClientEntryAndSSRModules[1],\n        addClientEntryAndSSRModules[2],\n      ])\n    )\n\n    // Wait for action entries to be added.\n    await Promise.all(addActionEntryList)\n\n    const addedClientActionEntryList: Promise<any>[] = []\n    const actionMapsPerClientEntry: Record<\n      string,\n      Map<string, ActionIdNamePair[]>\n    > = {}\n\n    // We need to create extra action entries that are created from the\n    // client layer.\n    // Start from each entry's created SSR dependency from our previous step.\n    for (const [name, ssrEntryDependencies] of Object.entries(\n      createdSSRDependenciesForEntry\n    )) {\n      // Collect from all entries, e.g. layout.js, page.js, loading.js, ...\n      // add aggregate them.\n      const actionEntryImports = this.collectClientActionsFromDependencies({\n        compilation,\n        dependencies: ssrEntryDependencies,\n      })\n\n      if (actionEntryImports.size > 0) {\n        if (!actionMapsPerClientEntry[name]) {\n          actionMapsPerClientEntry[name] = new Map()\n        }\n        actionMapsPerClientEntry[name] = new Map([\n          ...actionMapsPerClientEntry[name],\n          ...actionEntryImports,\n        ])\n      }\n    }\n\n    for (const [entryName, actionEntryImports] of Object.entries(\n      actionMapsPerClientEntry\n    )) {\n      // If an action method is already created in the server layer, we don't\n      // need to create it again in the action layer.\n      // This is to avoid duplicate action instances and make sure the module\n      // state is shared.\n      let remainingClientImportedActions = false\n      const remainingActionEntryImports = new Map<string, ActionIdNamePair[]>()\n      for (const [dep, actions] of actionEntryImports) {\n        const remainingActionNames = []\n        for (const action of actions) {\n          if (!createdActionIds.has(entryName + '@' + action.id)) {\n            remainingActionNames.push(action)\n          }\n        }\n        if (remainingActionNames.length > 0) {\n          remainingActionEntryImports.set(dep, remainingActionNames)\n          remainingClientImportedActions = true\n        }\n      }\n\n      if (remainingClientImportedActions) {\n        addedClientActionEntryList.push(\n          this.injectActionEntry({\n            compiler,\n            compilation,\n            actions: remainingActionEntryImports,\n            entryName,\n            bundlePath: entryName,\n            fromClient: true,\n            createdActionIds,\n          })\n        )\n      }\n    }\n\n    await Promise.all(addedClientActionEntryList)\n  }\n\n  collectClientActionsFromDependencies({\n    compilation,\n    dependencies,\n  }: {\n    compilation: webpack.Compilation\n    dependencies: ReturnType<typeof webpack.EntryPlugin.createDependency>[]\n  }) {\n    // action file path -> action names\n    const collectedActions = new Map<string, ActionIdNamePair[]>()\n\n    // Keep track of checked modules to avoid infinite loops with recursive imports.\n    const visitedModule = new Set<string>()\n    const visitedEntry = new Set<string>()\n\n    const collectActions = ({\n      entryRequest,\n      resolvedModule,\n    }: {\n      entryRequest: string\n      resolvedModule: any\n    }) => {\n      const collectActionsInDep = (mod: webpack.NormalModule): void => {\n        if (!mod) return\n\n        const modResource = getModuleResource(mod)\n\n        if (!modResource) return\n\n        if (visitedModule.has(modResource)) return\n        visitedModule.add(modResource)\n\n        const actionIds = getModuleBuildInfo(mod).rsc?.actionIds\n        if (actionIds) {\n          collectedActions.set(\n            modResource,\n            Object.entries(actionIds).map(([id, exportedName]) => ({\n              id,\n              exportedName,\n            }))\n          )\n        }\n\n        // Collect used exported actions transversely.\n        getModuleReferencesInOrder(mod, compilation.moduleGraph).forEach(\n          (connection: any) => {\n            collectActionsInDep(\n              connection.resolvedModule as webpack.NormalModule\n            )\n          }\n        )\n      }\n\n      // Don't traverse the module graph anymore once hitting the action layer.\n      if (\n        entryRequest &&\n        !entryRequest.includes('next-flight-action-entry-loader')\n      ) {\n        // Traverse the module graph to find all client components.\n        collectActionsInDep(resolvedModule)\n      }\n    }\n\n    for (const entryDependency of dependencies) {\n      const ssrEntryModule =\n        compilation.moduleGraph.getResolvedModule(entryDependency)!\n      for (const connection of getModuleReferencesInOrder(\n        ssrEntryModule,\n        compilation.moduleGraph\n      )) {\n        const depModule = connection.dependency\n        const request = (depModule as unknown as webpack.NormalModule).request\n\n        // It is possible that the same entry is added multiple times in the\n        // connection graph. We can just skip these to speed up the process.\n        if (visitedEntry.has(request)) continue\n        visitedEntry.add(request)\n\n        collectActions({\n          entryRequest: request,\n          resolvedModule: connection.resolvedModule,\n        })\n      }\n    }\n\n    return collectedActions\n  }\n\n  collectComponentInfoFromServerEntryDependency({\n    entryRequest,\n    compilation,\n    resolvedModule,\n  }: {\n    entryRequest: string\n    compilation: webpack.Compilation\n    resolvedModule: any /* Dependency */\n  }): {\n    cssImports: CssImports\n    clientComponentImports: ClientComponentImports\n    actionImports: [string, ActionIdNamePair[]][]\n  } {\n    // Keep track of checked modules to avoid infinite loops with recursive imports.\n    const visitedOfClientComponentsTraverse = new Set()\n\n    // Info to collect.\n    const clientComponentImports: ClientComponentImports = {}\n    const actionImports: [string, ActionIdNamePair[]][] = []\n    const CSSImports = new Set<string>()\n\n    const filterClientComponents = (\n      mod: webpack.NormalModule,\n      importedIdentifiers: string[]\n    ): void => {\n      if (!mod) return\n\n      const modResource = getModuleResource(mod)\n\n      if (!modResource) return\n      if (visitedOfClientComponentsTraverse.has(modResource)) {\n        if (clientComponentImports[modResource]) {\n          addClientImport(\n            mod,\n            modResource,\n            clientComponentImports,\n            importedIdentifiers,\n            false\n          )\n        }\n        return\n      }\n      visitedOfClientComponentsTraverse.add(modResource)\n\n      const actionIds = getModuleBuildInfo(mod).rsc?.actionIds\n      if (actionIds) {\n        actionImports.push([\n          modResource,\n          Object.entries(actionIds).map(([id, exportedName]) => ({\n            id,\n            exportedName,\n          })),\n        ])\n      }\n\n      if (isCSSMod(mod)) {\n        const sideEffectFree =\n          mod.factoryMeta && (mod.factoryMeta as any).sideEffectFree\n\n        if (sideEffectFree) {\n          const unused = !compilation.moduleGraph\n            .getExportsInfo(mod)\n            .isModuleUsed(this.webpackRuntime)\n\n          if (unused) return\n        }\n\n        CSSImports.add(modResource)\n      } else if (isClientComponentEntryModule(mod)) {\n        if (!clientComponentImports[modResource]) {\n          clientComponentImports[modResource] = new Set()\n        }\n        addClientImport(\n          mod,\n          modResource,\n          clientComponentImports,\n          importedIdentifiers,\n          true\n        )\n\n        return\n      }\n\n      getModuleReferencesInOrder(mod, compilation.moduleGraph).forEach(\n        (connection: any) => {\n          let dependencyIds: string[] = []\n\n          // `ids` are the identifiers that are imported from the dependency,\n          // if it's present, it's an array of strings.\n          if (connection.dependency?.ids) {\n            dependencyIds.push(...connection.dependency.ids)\n          } else {\n            dependencyIds = ['*']\n          }\n\n          filterClientComponents(connection.resolvedModule, dependencyIds)\n        }\n      )\n    }\n\n    // Traverse the module graph to find all client components.\n    filterClientComponents(resolvedModule, [])\n\n    return {\n      clientComponentImports,\n      cssImports: CSSImports.size\n        ? {\n            [entryRequest]: Array.from(CSSImports),\n          }\n        : {},\n      actionImports,\n    }\n  }\n\n  injectClientEntryAndSSRModules({\n    compiler,\n    compilation,\n    entryName,\n    clientImports,\n    bundlePath,\n    absolutePagePath,\n  }: {\n    compiler: webpack.Compiler\n    compilation: webpack.Compilation\n    entryName: string\n    clientImports: ClientComponentImports\n    bundlePath: string\n    absolutePagePath?: string\n  }): [\n    shouldInvalidate: boolean,\n    addSSREntryPromise: Promise<void>,\n    addRSCEntryPromise: Promise<void>,\n    ssrDep: ReturnType<typeof webpack.EntryPlugin.createDependency>,\n  ] {\n    let shouldInvalidate = false\n\n    const modules = Object.keys(clientImports)\n      .sort((a, b) => (regexCSS.test(b) ? 1 : a.localeCompare(b)))\n      .map((clientImportPath) => ({\n        request: clientImportPath,\n        ids: [...clientImports[clientImportPath]],\n      }))\n\n    // For the client entry, we always use the CJS build of Next.js. If the\n    // server is using the ESM build (when using the Edge runtime), we need to\n    // replace them.\n    const clientBrowserLoader = `next-flight-client-entry-loader?${stringify({\n      modules: (this.isEdgeServer\n        ? modules.map(({ request, ids }) => ({\n            request: request.replace(\n              /[\\\\/]next[\\\\/]dist[\\\\/]esm[\\\\/]/,\n              '/next/dist/'.replace(/\\//g, path.sep)\n            ),\n            ids,\n          }))\n        : modules\n      ).map((x) => JSON.stringify(x)),\n      server: false,\n    })}!`\n\n    const clientServerLoader = `next-flight-client-entry-loader?${stringify({\n      modules: modules.map((x) => JSON.stringify(x)),\n      server: true,\n    })}!`\n\n    // Add for the client compilation\n    // Inject the entry to the client compiler.\n    if (this.dev) {\n      const entries = getEntries(compiler.outputPath)\n      const pageKey = getEntryKey(\n        COMPILER_NAMES.client,\n        PAGE_TYPES.APP,\n        bundlePath\n      )\n\n      if (!entries[pageKey]) {\n        entries[pageKey] = {\n          type: EntryTypes.CHILD_ENTRY,\n          parentEntries: new Set([entryName]),\n          absoluteEntryFilePath: absolutePagePath,\n          bundlePath,\n          request: clientBrowserLoader,\n          dispose: false,\n          lastActiveTime: Date.now(),\n        }\n        shouldInvalidate = true\n      } else {\n        const entryData = entries[pageKey]\n        // New version of the client loader\n        if (entryData.request !== clientBrowserLoader) {\n          entryData.request = clientBrowserLoader\n          shouldInvalidate = true\n        }\n        if (entryData.type === EntryTypes.CHILD_ENTRY) {\n          entryData.parentEntries.add(entryName)\n        }\n        entryData.dispose = false\n        entryData.lastActiveTime = Date.now()\n      }\n    } else {\n      pluginState.injectedClientEntries[bundlePath] = clientBrowserLoader\n    }\n\n    const clientComponentSSREntryDep = webpack.EntryPlugin.createDependency(\n      clientServerLoader,\n      { name: bundlePath }\n    )\n\n    const clientComponentRSCEntryDep = webpack.EntryPlugin.createDependency(\n      clientServerLoader,\n      { name: bundlePath }\n    )\n\n    return [\n      shouldInvalidate,\n      // Add the entries to the server compiler for the SSR and RSC layers. The\n      // promises are awaited later using `Promise.all` in order to parallelize\n      // adding the entries.\n      this.addEntry(compilation, compiler.context, clientComponentSSREntryDep, {\n        name: entryName,\n        layer: WEBPACK_LAYERS.serverSideRendering,\n      }),\n      this.addEntry(compilation, compiler.context, clientComponentRSCEntryDep, {\n        name: entryName,\n        layer: WEBPACK_LAYERS.reactServerComponents,\n      }),\n      clientComponentSSREntryDep,\n    ]\n  }\n\n  injectActionEntry({\n    compiler,\n    compilation,\n    actions,\n    entryName,\n    bundlePath,\n    fromClient,\n    createdActionIds,\n  }: {\n    compiler: webpack.Compiler\n    compilation: webpack.Compilation\n    actions: Map<string, ActionIdNamePair[]>\n    entryName: string\n    bundlePath: string\n    createdActionIds: Set<string>\n    fromClient?: boolean\n  }) {\n    const actionsArray = Array.from(actions.entries())\n    for (const [, actionsFromModule] of actions) {\n      for (const { id } of actionsFromModule) {\n        createdActionIds.add(entryName + '@' + id)\n      }\n    }\n\n    if (actionsArray.length === 0) {\n      return Promise.resolve()\n    }\n\n    const actionLoader = `next-flight-action-entry-loader?${stringify({\n      actions: JSON.stringify(\n        actionsArray satisfies FlightActionEntryLoaderActions\n      ),\n      __client_imported__: fromClient,\n    })}!`\n\n    const currentCompilerServerActions = this.isEdgeServer\n      ? pluginState.edgeServerActions\n      : pluginState.serverActions\n\n    for (const [, actionsFromModule] of actionsArray) {\n      for (const { id } of actionsFromModule) {\n        if (typeof currentCompilerServerActions[id] === 'undefined') {\n          currentCompilerServerActions[id] = {\n            workers: {},\n            layer: {},\n          }\n        }\n        currentCompilerServerActions[id].workers[bundlePath] = {\n          moduleId: '', // TODO: What's the meaning of this?\n          async: false,\n        }\n\n        currentCompilerServerActions[id].layer[bundlePath] = fromClient\n          ? WEBPACK_LAYERS.actionBrowser\n          : WEBPACK_LAYERS.reactServerComponents\n      }\n    }\n\n    // Inject the entry to the server compiler\n    const actionEntryDep = webpack.EntryPlugin.createDependency(actionLoader, {\n      name: bundlePath,\n    })\n\n    return this.addEntry(\n      compilation,\n      // Reuse compilation context.\n      compiler.context,\n      actionEntryDep,\n      {\n        name: entryName,\n        layer: fromClient\n          ? WEBPACK_LAYERS.actionBrowser\n          : WEBPACK_LAYERS.reactServerComponents,\n      }\n    )\n  }\n\n  addEntry(\n    compilation: webpack.Compilation,\n    context: string,\n    dependency: webpack.Dependency,\n    options: webpack.EntryOptions\n  ): Promise<any> /* Promise<module> */ {\n    return new Promise((resolve, reject) => {\n      if ('rspack' in compilation.compiler) {\n        compilation.addInclude(context, dependency, options, (err, module) => {\n          if (err) {\n            return reject(err)\n          }\n\n          compilation.moduleGraph\n            .getExportsInfo(module!)\n            .setUsedInUnknownWay(\n              this.isEdgeServer ? EDGE_RUNTIME_WEBPACK : DEFAULT_RUNTIME_WEBPACK\n            )\n          return resolve(module)\n        })\n      } else {\n        const entry = compilation.entries.get(options.name!)!\n        entry.includeDependencies.push(dependency)\n        compilation.hooks.addEntry.call(entry as any, options)\n        compilation.addModuleTree(\n          {\n            context,\n            dependency,\n            contextInfo: { issuerLayer: options.layer },\n          },\n          (err: any, module: any) => {\n            if (err) {\n              compilation.hooks.failedEntry.call(dependency, options, err)\n              return reject(err)\n            }\n\n            compilation.hooks.succeedEntry.call(dependency, options, module)\n\n            compilation.moduleGraph\n              .getExportsInfo(module)\n              .setUsedInUnknownWay(\n                this.isEdgeServer\n                  ? EDGE_RUNTIME_WEBPACK\n                  : DEFAULT_RUNTIME_WEBPACK\n              )\n\n            return resolve(module)\n          }\n        )\n      }\n    })\n  }\n\n  async createActionAssets(compilation: webpack.Compilation) {\n    const serverActions: ActionManifest['node'] = {}\n    const edgeServerActions: ActionManifest['edge'] = {}\n\n    traverseModules(compilation, (mod, _chunk, chunkGroup, modId) => {\n      // Go through all action entries and record the module ID for each entry.\n      if (\n        chunkGroup.name &&\n        mod.request &&\n        modId &&\n        /next-flight-action-entry-loader/.test(mod.request)\n      ) {\n        const fromClient = /&__client_imported__=true/.test(mod.request)\n\n        const mapping = this.isEdgeServer\n          ? pluginState.edgeServerActionModules\n          : pluginState.serverActionModules\n\n        if (!mapping[chunkGroup.name]) {\n          mapping[chunkGroup.name] = {}\n        }\n        mapping[chunkGroup.name][fromClient ? 'client' : 'server'] = {\n          moduleId: modId,\n          async: compilation.moduleGraph.isAsync(mod),\n        }\n      }\n    })\n\n    for (let id in pluginState.serverActions) {\n      const action = pluginState.serverActions[id]\n      for (let name in action.workers) {\n        const modId =\n          pluginState.serverActionModules[name][\n            action.layer[name] === WEBPACK_LAYERS.actionBrowser\n              ? 'client'\n              : 'server'\n          ]\n        action.workers[name] = modId!\n      }\n      serverActions[id] = action\n    }\n\n    for (let id in pluginState.edgeServerActions) {\n      const action = pluginState.edgeServerActions[id]\n      for (let name in action.workers) {\n        const modId =\n          pluginState.edgeServerActionModules[name][\n            action.layer[name] === WEBPACK_LAYERS.actionBrowser\n              ? 'client'\n              : 'server'\n          ]\n        action.workers[name] = modId!\n      }\n      edgeServerActions[id] = action\n    }\n\n    const serverManifest = {\n      node: serverActions,\n      edge: edgeServerActions,\n      encryptionKey: this.encryptionKey,\n    }\n    const edgeServerManifest = {\n      ...serverManifest,\n      encryptionKey: 'process.env.NEXT_SERVER_ACTIONS_ENCRYPTION_KEY',\n    }\n\n    const json = JSON.stringify(serverManifest, null, this.dev ? 2 : undefined)\n    const edgeJson = JSON.stringify(\n      edgeServerManifest,\n      null,\n      this.dev ? 2 : undefined\n    )\n\n    compilation.emitAsset(\n      `${this.assetPrefix}${SERVER_REFERENCE_MANIFEST}.js`,\n      new sources.RawSource(\n        `self.__RSC_SERVER_MANIFEST=${JSON.stringify(edgeJson)}`\n      ) as unknown as webpack.sources.RawSource\n    )\n    compilation.emitAsset(\n      `${this.assetPrefix}${SERVER_REFERENCE_MANIFEST}.json`,\n      new sources.RawSource(json) as unknown as webpack.sources.RawSource\n    )\n  }\n}\n\nfunction addClientImport(\n  mod: webpack.NormalModule,\n  modRequest: string,\n  clientComponentImports: ClientComponentImports,\n  importedIdentifiers: string[],\n  isFirstVisitModule: boolean\n) {\n  const clientEntryType = getModuleBuildInfo(mod).rsc?.clientEntryType\n  const isCjsModule = clientEntryType === 'cjs'\n  const assumedSourceType = getAssumedSourceType(\n    mod,\n    isCjsModule ? 'commonjs' : 'auto'\n  )\n\n  const clientImportsSet = clientComponentImports[modRequest]\n\n  if (importedIdentifiers[0] === '*') {\n    // If there's collected import path with named import identifiers,\n    // or there's nothing in collected imports are empty.\n    // we should include the whole module.\n    if (!isFirstVisitModule && [...clientImportsSet][0] !== '*') {\n      clientComponentImports[modRequest] = new Set(['*'])\n    }\n  } else {\n    const isAutoModuleSourceType = assumedSourceType === 'auto'\n    if (isAutoModuleSourceType) {\n      clientComponentImports[modRequest] = new Set(['*'])\n    } else {\n      // If it's not analyzed as named ESM exports, e.g. if it's mixing `export *` with named exports,\n      // We'll include all modules since it's not able to do tree-shaking.\n      for (const name of importedIdentifiers) {\n        // For cjs module default import, we include the whole module since\n        const isCjsDefaultImport = isCjsModule && name === 'default'\n\n        // Always include __esModule along with cjs module default export,\n        // to make sure it work with client module proxy from React.\n        if (isCjsDefaultImport) {\n          clientComponentImports[modRequest].add('__esModule')\n        }\n\n        clientComponentImports[modRequest].add(name)\n      }\n    }\n  }\n}\n\nfunction getModuleResource(mod: webpack.NormalModule): string {\n  const modPath: string = mod.resourceResolveData?.path || ''\n  const modQuery = mod.resourceResolveData?.query || ''\n  // We have to always use the resolved request here to make sure the\n  // server and client are using the same module path (required by RSC), as\n  // the server compiler and client compiler have different resolve configs.\n  let modResource: string = modPath + modQuery\n\n  // Context modules don't have a resource path, we use the identifier instead.\n  if (mod.constructor.name === 'ContextModule') {\n    modResource = mod.identifier()\n  }\n\n  // For the barrel optimization, we need to use the match resource instead\n  // because there will be 2 modules for the same file (same resource path)\n  // but they're different modules and can't be deduped via `visitedModule`.\n  // The first module is a virtual re-export module created by the loader.\n  if (mod.matchResource?.startsWith(BARREL_OPTIMIZATION_PREFIX)) {\n    modResource = mod.matchResource + ':' + modResource\n  }\n\n  if (mod.resource === `?${WEBPACK_RESOURCE_QUERIES.metadataRoute}`) {\n    return getMetadataRouteResource(mod.rawRequest).filePath\n  }\n\n  return modResource\n}\n\nfunction getMetadataRouteResource(request: string): MetadataRouteLoaderOptions {\n  // e.g. next-metadata-route-loader?filePath=<some-url-encoded-path>&isDynamicRouteExtension=1!?__next_metadata_route__\n  const query = request.split('!')[0].split('next-metadata-route-loader?')[1]\n\n  return parse(query) as MetadataRouteLoaderOptions\n}\n"], "names": ["webpack", "parse", "stringify", "path", "sources", "getInvalidator", "getEntries", "EntryTypes", "getEntry<PERSON>ey", "WEBPACK_LAYERS", "WEBPACK_RESOURCE_QUERIES", "APP_CLIENT_INTERNALS", "BARREL_OPTIMIZATION_PREFIX", "COMPILER_NAMES", "DEFAULT_RUNTIME_WEBPACK", "EDGE_RUNTIME_WEBPACK", "SERVER_REFERENCE_MANIFEST", "UNDERSCORE_NOT_FOUND_ROUTE_ENTRY", "isClientComponentEntryModule", "isCSSMod", "regexCSS", "traverseModules", "forEachEntryModule", "formatBarrelOptimizedResource", "getModuleReferencesInOrder", "normalizePathSep", "getProxiedPluginState", "PAGE_TYPES", "getModuleBuildInfo", "getAssumedSourceType", "isAppRouteRoute", "isMetadataRoute", "PLUGIN_NAME", "pluginState", "serverActions", "edgeServerActions", "serverActionModules", "edgeServerActionModules", "ssrModules", "edgeSsrModules", "rscModules", "edgeRscModules", "injectedClientEntries", "deduplicateCSSImportsForEntry", "mergedCSSimports", "sortedCSSImports", "Object", "entries", "sort", "a", "b", "a<PERSON><PERSON>", "bPath", "a<PERSON><PERSON><PERSON>", "split", "length", "b<PERSON><PERSON><PERSON>", "aName", "name", "bName", "indexA", "indexOf", "indexB", "dedupedCSSImports", "trackedCSSImports", "Set", "entryName", "cssImports", "cssImport", "has", "filename", "includes", "add", "push", "FlightClientEntryPlugin", "constructor", "options", "dev", "appDir", "isEdgeServer", "assetPrefix", "<PERSON><PERSON><PERSON>", "webpackRuntime", "apply", "compiler", "hooks", "finishMake", "tapPromise", "compilation", "createClientEntries", "afterCompile", "tap", "recordModule", "modId", "mod", "modPath", "matchResource", "resourceResolveData", "mod<PERSON><PERSON><PERSON>", "query", "modResource", "startsWith", "resource", "layer", "reactServerComponents", "key", "relative", "context", "replace", "moduleInfo", "moduleId", "async", "moduleGraph", "isAsync", "serverSideRendering", "ssrNamedModuleId", "_chunk", "_chunkGroup", "make", "processAssets", "stage", "Compilation", "PROCESS_ASSETS_STAGE_OPTIMIZE_HASH", "createActionAssets", "addClientEntryAndSSRModulesList", "createdSSRDependenciesForEntry", "addActionEntryList", "actionMapsPerEntry", "createdActionIds", "entryModule", "internalClientComponentEntryImports", "actionEntryImports", "Map", "clientEntriesToInject", "connection", "entryRequest", "dependency", "request", "endsWith", "metadataRoute", "filePath", "isDynamicRouteExtension", "getMetadataRouteResource", "clientComponentImports", "actionImports", "collectComponentInfoFromServerEntryDependency", "resolvedModule", "for<PERSON>ach", "dep", "actions", "set", "isAbsoluteRequest", "isAbsolute", "keys", "value", "relativeRequest", "bundlePath", "assign", "absolutePagePath", "clientEntryToInject", "injected", "injectClientEntryAndSSRModules", "clientImports", "reduce", "res", "curr", "size", "injectActionEntry", "invalidator", "outputPath", "some", "shouldInvalidate", "invalidate", "client", "Promise", "all", "flatMap", "addClientEntryAndSSRModules", "addedClientActionEntryList", "actionMapsPerClientEntry", "ssrEntryDependencies", "collectClientActionsFromDependencies", "dependencies", "remainingClientImportedActions", "remainingActionEntryImports", "remainingActionNames", "action", "id", "fromClient", "collectedActions", "visitedModule", "visitedEntry", "collectActions", "collectActionsInDep", "getModuleResource", "actionIds", "rsc", "map", "exportedName", "entryDependency", "ssrEntryModule", "getResolvedModule", "depModule", "visitedOfClientComponentsTraverse", "CSSImports", "filterClientComponents", "importedIdentifiers", "addClientImport", "sideEffectFree", "factoryMeta", "unused", "getExportsInfo", "isModuleUsed", "dependencyIds", "ids", "Array", "from", "modules", "test", "localeCompare", "clientImportPath", "clientBrowserLoader", "sep", "x", "JSON", "server", "clientServerLoader", "page<PERSON><PERSON>", "APP", "type", "CHILD_ENTRY", "parentEntries", "absoluteEntryFilePath", "dispose", "lastActiveTime", "Date", "now", "entryData", "clientComponentSSREntryDep", "EntryPlugin", "createDependency", "clientComponentRSCEntryDep", "addEntry", "actionsArray", "actionsFromModule", "resolve", "actionLoader", "__client_imported__", "currentCompilerServerActions", "workers", "<PERSON><PERSON><PERSON><PERSON>", "actionEntryDep", "reject", "addInclude", "err", "module", "setUsedInUnknownWay", "entry", "get", "includeDependencies", "call", "addModuleTree", "contextInfo", "issuer<PERSON><PERSON>er", "failedEntry", "<PERSON><PERSON><PERSON><PERSON>", "chunkGroup", "mapping", "serverManifest", "node", "edge", "edgeServerManifest", "json", "undefined", "edgeJson", "emitAsset", "RawSource", "modRequest", "isFirstVisitModule", "clientEntryType", "isCjsModule", "assumedSourceType", "clientImportsSet", "isAutoModuleSourceType", "isCjsDefaultImport", "identifier", "rawRequest"], "mappings": "AAKA,SAASA,OAAO,QAAQ,qCAAoC;AAC5D,SAASC,KAAK,EAAEC,SAAS,QAAQ,cAAa;AAC9C,OAAOC,UAAU,OAAM;AACvB,SAASC,OAAO,QAAQ,qCAAoC;AAC5D,SACEC,cAAc,EACdC,UAAU,EACVC,UAAU,EACVC,WAAW,QACN,8CAA6C;AACpD,SACEC,cAAc,EACdC,wBAAwB,QACnB,yBAAwB;AAC/B,SACEC,oBAAoB,EACpBC,0BAA0B,EAC1BC,cAAc,EACdC,uBAAuB,EACvBC,oBAAoB,EACpBC,yBAAyB,EACzBC,gCAAgC,QAC3B,gCAA+B;AACtC,SACEC,4BAA4B,EAC5BC,QAAQ,EACRC,QAAQ,QACH,mBAAkB;AACzB,SACEC,eAAe,EACfC,kBAAkB,EAClBC,6BAA6B,EAC7BC,0BAA0B,QACrB,WAAU;AACjB,SAASC,gBAAgB,QAAQ,mDAAkD;AACnF,SAASC,qBAAqB,QAAQ,sBAAqB;AAC3D,SAASC,UAAU,QAAQ,0BAAyB;AACpD,SAASC,kBAAkB,QAAQ,mCAAkC;AACrE,SAASC,oBAAoB,QAAQ,gCAA+B;AACpE,SAASC,eAAe,QAAQ,kCAAiC;AACjE,SAASC,eAAe,QAAQ,0CAAyC;AAWzE,MAAMC,cAAc;AA4BpB,MAAMC,cAAcP,sBAAsB;IACxC,gDAAgD;IAChDQ,eAAe,CAAC;IAChBC,mBAAmB,CAAC;IAEpBC,qBAAqB,CAAC;IAItBC,yBAAyB,CAAC;IAI1BC,YAAY,CAAC;IACbC,gBAAgB,CAAC;IAEjBC,YAAY,CAAC;IACbC,gBAAgB,CAAC;IAEjBC,uBAAuB,CAAC;AAC1B;AAEA,SAASC,8BAA8BC,gBAA4B;IACjE,uEAAuE;IACvE,oEAAoE;IACpE,wEAAwE;IACxE,+DAA+D;IAC/D,sEAAsE;IACtE,uEAAuE;IACvE,wEAAwE;IACxE,UAAU;IACV,qEAAqE;IACrE,qEAAqE;IACrE,mEAAmE;IACnE,yEAAyE;IACzE,uFAAuF;IAEvF,2CAA2C;IAC3C,MAAMC,mBAAmBC,OAAOC,OAAO,CAACH,kBAAkBI,IAAI,CAAC,CAACC,GAAGC;QACjE,MAAM,CAACC,MAAM,GAAGF;QAChB,MAAM,CAACG,MAAM,GAAGF;QAEhB,MAAMG,SAASF,MAAMG,KAAK,CAAC,KAAKC,MAAM;QACtC,MAAMC,SAASJ,MAAME,KAAK,CAAC,KAAKC,MAAM;QAEtC,IAAIF,WAAWG,QAAQ;YACrB,OAAOH,SAASG;QAClB;QAEA,MAAMC,QAAQtD,KAAKF,KAAK,CAACkD,OAAOO,IAAI;QACpC,MAAMC,QAAQxD,KAAKF,KAAK,CAACmD,OAAOM,IAAI;QAEpC,MAAME,SAAS;YAAC;YAAY;SAAS,CAACC,OAAO,CAACJ;QAC9C,MAAMK,SAAS;YAAC;YAAY;SAAS,CAACD,OAAO,CAACF;QAE9C,IAAIC,WAAW,CAAC,GAAG,OAAO;QAC1B,IAAIE,WAAW,CAAC,GAAG,OAAO,CAAC;QAC3B,OAAOF,SAASE;IAClB;IAEA,MAAMC,oBAAgC,CAAC;IACvC,MAAMC,oBAAoB,IAAIC;IAC9B,KAAK,MAAM,CAACC,WAAWC,WAAW,IAAItB,iBAAkB;QACtD,KAAK,MAAMuB,aAAaD,WAAY;YAClC,IAAIH,kBAAkBK,GAAG,CAACD,YAAY;YAEtC,iEAAiE;YACjE,MAAME,WAAWnE,KAAKF,KAAK,CAACiE,WAAWR,IAAI;YAC3C,IAAI;gBAAC;gBAAY;aAAS,CAACa,QAAQ,CAACD,WAAW;gBAC7CN,kBAAkBQ,GAAG,CAACJ;YACxB;YAEA,IAAI,CAACL,iBAAiB,CAACG,UAAU,EAAE;gBACjCH,iBAAiB,CAACG,UAAU,GAAG,EAAE;YACnC;YACAH,iBAAiB,CAACG,UAAU,CAACO,IAAI,CAACL;QACpC;IACF;IAEA,OAAOL;AACT;AAEA,OAAO,MAAMW;IAQXC,YAAYC,OAAgB,CAAE;QAC5B,IAAI,CAACC,GAAG,GAAGD,QAAQC,GAAG;QACtB,IAAI,CAACC,MAAM,GAAGF,QAAQE,MAAM;QAC5B,IAAI,CAACC,YAAY,GAAGH,QAAQG,YAAY;QACxC,IAAI,CAACC,WAAW,GAAG,CAAC,IAAI,CAACH,GAAG,IAAI,CAAC,IAAI,CAACE,YAAY,GAAG,QAAQ;QAC7D,IAAI,CAACE,aAAa,GAAGL,QAAQK,aAAa;QAC1C,IAAI,CAACC,cAAc,GAAG,IAAI,CAACH,YAAY,GACnChE,uBACAD;IACN;IAEAqE,MAAMC,QAA0B,EAAE;QAChCA,SAASC,KAAK,CAACC,UAAU,CAACC,UAAU,CAACvD,aAAa,CAACwD,cACjD,IAAI,CAACC,mBAAmB,CAACL,UAAUI;QAGrCJ,SAASC,KAAK,CAACK,YAAY,CAACC,GAAG,CAAC3D,aAAa,CAACwD;YAC5C,MAAMI,eAAe,CAACC,OAAeC;oBAGEA,0BACpBA;gBAHjB,yFAAyF;gBACzF,2DAA2D;gBAC3D,MAAMC,UAAUD,IAAIE,aAAa,MAAIF,2BAAAA,IAAIG,mBAAmB,qBAAvBH,yBAAyB3F,IAAI;gBAClE,MAAM+F,WAAWJ,EAAAA,4BAAAA,IAAIG,mBAAmB,qBAAvBH,0BAAyBK,KAAK,KAAI;gBACnD,wCAAwC;gBACxC,gFAAgF;gBAChF,MAAMC,cAAcL,UAChBA,QAAQM,UAAU,CAACzF,8BACjBW,8BAA8BuE,IAAIQ,QAAQ,EAAEP,WAC5CA,UAAUG,WACZJ,IAAIQ,QAAQ;gBAEhB,IAAI,OAAOT,UAAU,eAAeO,aAAa;oBAC/C,IAAIN,IAAIS,KAAK,KAAK9F,eAAe+F,qBAAqB,EAAE;wBACtD,MAAMC,MAAMtG,KACTuG,QAAQ,CAACtB,SAASuB,OAAO,EAAEP,aAC3BQ,OAAO,CAAC,uBAAuB;wBAElC,MAAMC,aAAyB;4BAC7BC,UAAUjB;4BACVkB,OAAOvB,YAAYwB,WAAW,CAACC,OAAO,CAACnB;wBACzC;wBAEA,IAAI,IAAI,CAACf,YAAY,EAAE;4BACrB9C,YAAYQ,cAAc,CAACgE,IAAI,GAAGI;wBACpC,OAAO;4BACL5E,YAAYO,UAAU,CAACiE,IAAI,GAAGI;wBAChC;oBACF;gBACF;gBAEA,IAAIf,IAAIS,KAAK,KAAK9F,eAAeyG,mBAAmB,EAAE;oBACpD;gBACF;gBAEA,yHAAyH;gBACzH,IAAI,OAAOrB,UAAU,eAAeO,aAAa;oBAC/C,4EAA4E;oBAC5E,6EAA6E;oBAC7E,sBAAsB;oBACtB,IAAIe,mBAAmBhH,KAAKuG,QAAQ,CAACtB,SAASuB,OAAO,EAAEP;oBAEvD,IAAI,CAACe,iBAAiBd,UAAU,CAAC,MAAM;wBACrC,+BAA+B;wBAC/Bc,mBAAmB,CAAC,EAAE,EAAE1F,iBAAiB0F,mBAAmB;oBAC9D;oBAEA,MAAMN,aAAyB;wBAC7BC,UAAUjB;wBACVkB,OAAOvB,YAAYwB,WAAW,CAACC,OAAO,CAACnB;oBACzC;oBAEA,IAAI,IAAI,CAACf,YAAY,EAAE;wBACrB9C,YAAYM,cAAc,CACxB4E,iBAAiBP,OAAO,CAAC,uBAAuB,eACjD,GAAGC;oBACN,OAAO;wBACL5E,YAAYK,UAAU,CAAC6E,iBAAiB,GAAGN;oBAC7C;gBACF;YACF;YAEAxF,gBAAgBmE,aAAa,CAACM,KAAKsB,QAAQC,aAAaxB;gBACtD,IAAIA,OAAOD,aAAaC,OAAOC;YACjC;QACF;QAEAV,SAASC,KAAK,CAACiC,IAAI,CAAC3B,GAAG,CAAC3D,aAAa,CAACwD;YACpCA,YAAYH,KAAK,CAACkC,aAAa,CAAChC,UAAU,CACxC;gBACE7B,MAAM1B;gBACNwF,OAAOxH,QAAQyH,WAAW,CAACC,kCAAkC;YAC/D,GACA,IAAM,IAAI,CAACC,kBAAkB,CAACnC;QAElC;IACF;IAEA,MAAMC,oBACJL,QAA0B,EAC1BI,WAAgC,EAChC;QACA,MAAMoC,kCAEF,EAAE;QACN,MAAMC,iCAGF,CAAC;QAEL,MAAMC,qBACJ,EAAE;QACJ,MAAMC,qBAGF,CAAC;QACL,MAAMC,mBAAmB,IAAI/D;QAE7B,4EAA4E;QAC5E,0BAA0B;QAC1B3C,mBAAmBkE,aAAa,CAAC,EAAE9B,IAAI,EAAEuE,WAAW,EAAE;YACpD,MAAMC,sCAA8D,CAAC;YACrE,MAAMC,qBAAqB,IAAIC;YAC/B,MAAMC,wBAAwB,EAAE;YAChC,MAAMzF,mBAA+B,CAAC;YAEtC,KAAK,MAAM0F,cAAc9G,2BACvByG,aACAzC,YAAYwB,WAAW,EACtB;gBACD,uFAAuF;gBACvF,IAAIuB,eAAe,AACjBD,WAAWE,UAAU,CACrBC,OAAO;gBAET,IAAIF,aAAaG,QAAQ,CAAChI,yBAAyBiI,aAAa,GAAG;oBACjE,MAAM,EAAEC,QAAQ,EAAEC,uBAAuB,EAAE,GACzCC,yBAAyBP;oBAE3B,IAAIM,4BAA4B,KAAK;wBACnCN,eAAeK;oBACjB;gBACF;gBAEA,MAAM,EAAEG,sBAAsB,EAAEC,aAAa,EAAE7E,UAAU,EAAE,GACzD,IAAI,CAAC8E,6CAA6C,CAAC;oBACjDV;oBACA/C;oBACA0D,gBAAgBZ,WAAWY,cAAc;gBAC3C;gBAEFF,cAAcG,OAAO,CAAC,CAAC,CAACC,KAAKC,QAAQ,GACnClB,mBAAmBmB,GAAG,CAACF,KAAKC;gBAG9B,MAAME,oBAAoBpJ,KAAKqJ,UAAU,CAACjB;gBAE1C,mDAAmD;gBACnD,IAAI,CAACgB,mBAAmB;oBACtBzG,OAAO2G,IAAI,CAACV,wBAAwBI,OAAO,CACzC,CAACO,QAAWxB,mCAAmC,CAACwB,MAAM,GAAG,IAAIzF;oBAE/D;gBACF;gBAEA,2HAA2H;gBAC3H,4DAA4D;gBAC5D,kEAAkE;gBAClE,aAAa;gBACb,IAAI;gBAEJ,MAAM0F,kBAAkBJ,oBACpBpJ,KAAKuG,QAAQ,CAAClB,YAAYZ,OAAO,CAAC+B,OAAO,EAAG4B,gBAC5CA;gBAEJ,8CAA8C;gBAC9C,IAAIqB,aAAanI,iBACfkI,gBAAgB/C,OAAO,CAAC,eAAe,IAAIA,OAAO,CAAC,aAAa;gBAGlE,sEAAsE;gBACtE,qCAAqC;gBACrC,IAAI7E,gBAAgB6H,aAAa;oBAC/BA,aAAalG;gBACf;gBAEAZ,OAAO+G,MAAM,CAACjH,kBAAkBuB;gBAChCkE,sBAAsB5D,IAAI,CAAC;oBACzBW;oBACAI;oBACAtB,WAAWR;oBACXqF;oBACAa;oBACAE,kBAAkBvB;gBACpB;gBAEA,uKAAuK;gBACvK,wGAAwG;gBACxG,8IAA8I;gBAC9I,IACE7E,SAAS,CAAC,GAAG,EAAEzC,kCAAkC,IACjD2I,eAAe,iBACf;oBACAvB,sBAAsB5D,IAAI,CAAC;wBACzBW;wBACAI;wBACAtB,WAAWR;wBACXqF,wBAAwB,CAAC;wBACzBa,YAAY,CAAC,GAAG,EAAE3I,kCAAkC;wBACpD6I,kBAAkBvB;oBACpB;gBACF;YACF;YAEA,2EAA2E;YAC3E,mBAAmB;YACnB,MAAMxE,oBAAoBpB,8BAA8BC;YACxD,KAAK,MAAMmH,uBAAuB1B,sBAAuB;gBACvD,MAAM2B,WAAW,IAAI,CAACC,8BAA8B,CAAC;oBACnD,GAAGF,mBAAmB;oBACtBG,eAAe;wBACb,GAAGH,oBAAoBhB,sBAAsB;wBAC7C,GAAG,AACDhF,CAAAA,iBAAiB,CAACgG,oBAAoBD,gBAAgB,CAAC,IAAI,EAAE,AAAD,EAC5DK,MAAM,CAAyB,CAACC,KAAKC;4BACrCD,GAAG,CAACC,KAAK,GAAG,IAAIpG;4BAChB,OAAOmG;wBACT,GAAG,CAAC,EAAE;oBACR;gBACF;gBAEA,2EAA2E;gBAC3E,IAAI,CAACvC,8BAA8B,CAACkC,oBAAoB7F,SAAS,CAAC,EAAE;oBAClE2D,8BAA8B,CAACkC,oBAAoB7F,SAAS,CAAC,GAAG,EAAE;gBACpE;gBACA2D,8BAA8B,CAACkC,oBAAoB7F,SAAS,CAAC,CAACO,IAAI,CAChEuF,QAAQ,CAAC,EAAE;gBAGbpC,gCAAgCnD,IAAI,CAACuF;YACvC;YAEA,IAAI,CAAClI,gBAAgB4B,OAAO;gBAC1B,sBAAsB;gBACtBkE,gCAAgCnD,IAAI,CAClC,IAAI,CAACwF,8BAA8B,CAAC;oBAClC7E;oBACAI;oBACAtB,WAAWR;oBACXwG,eAAe;wBAAE,GAAGhC,mCAAmC;oBAAC;oBACxD0B,YAAYjJ;gBACd;YAEJ;YAEA,IAAIwH,mBAAmBmC,IAAI,GAAG,GAAG;gBAC/B,IAAI,CAACvC,kBAAkB,CAACrE,KAAK,EAAE;oBAC7BqE,kBAAkB,CAACrE,KAAK,GAAG,IAAI0E;gBACjC;gBACAL,kBAAkB,CAACrE,KAAK,GAAG,IAAI0E,IAAI;uBAC9BL,kBAAkB,CAACrE,KAAK;uBACxByE;iBACJ;YACH;QACF;QAEA,KAAK,MAAM,CAACzE,MAAMyE,mBAAmB,IAAIrF,OAAOC,OAAO,CACrDgF,oBACC;YACDD,mBAAmBrD,IAAI,CACrB,IAAI,CAAC8F,iBAAiB,CAAC;gBACrBnF;gBACAI;gBACA6D,SAASlB;gBACTjE,WAAWR;gBACXkG,YAAYlG;gBACZsE;YACF;QAEJ;QAEA,qDAAqD;QACrD,MAAMwC,cAAcnK,eAAe+E,SAASqF,UAAU;QACtD,4DAA4D;QAC5D,IACED,eACA5C,gCAAgC8C,IAAI,CAClC,CAAC,CAACC,iBAAiB,GAAKA,qBAAqB,OAE/C;YACAH,YAAYI,UAAU,CAAC;gBAAC/J,eAAegK,MAAM;aAAC;QAChD;QAEA,4EAA4E;QAC5E,0EAA0E;QAC1E,sCAAsC;QACtC,MAAMC,QAAQC,GAAG,CACfnD,gCAAgCoD,OAAO,CAAC,CAACC,8BAAgC;gBACvEA,2BAA2B,CAAC,EAAE;gBAC9BA,2BAA2B,CAAC,EAAE;aAC/B;QAGH,uCAAuC;QACvC,MAAMH,QAAQC,GAAG,CAACjD;QAElB,MAAMoD,6BAA6C,EAAE;QACrD,MAAMC,2BAGF,CAAC;QAEL,mEAAmE;QACnE,gBAAgB;QAChB,yEAAyE;QACzE,KAAK,MAAM,CAACzH,MAAM0H,qBAAqB,IAAItI,OAAOC,OAAO,CACvD8E,gCACC;YACD,qEAAqE;YACrE,sBAAsB;YACtB,MAAMM,qBAAqB,IAAI,CAACkD,oCAAoC,CAAC;gBACnE7F;gBACA8F,cAAcF;YAChB;YAEA,IAAIjD,mBAAmBmC,IAAI,GAAG,GAAG;gBAC/B,IAAI,CAACa,wBAAwB,CAACzH,KAAK,EAAE;oBACnCyH,wBAAwB,CAACzH,KAAK,GAAG,IAAI0E;gBACvC;gBACA+C,wBAAwB,CAACzH,KAAK,GAAG,IAAI0E,IAAI;uBACpC+C,wBAAwB,CAACzH,KAAK;uBAC9ByE;iBACJ;YACH;QACF;QAEA,KAAK,MAAM,CAACjE,WAAWiE,mBAAmB,IAAIrF,OAAOC,OAAO,CAC1DoI,0BACC;YACD,uEAAuE;YACvE,+CAA+C;YAC/C,uEAAuE;YACvE,mBAAmB;YACnB,IAAII,iCAAiC;YACrC,MAAMC,8BAA8B,IAAIpD;YACxC,KAAK,MAAM,CAACgB,KAAKC,QAAQ,IAAIlB,mBAAoB;gBAC/C,MAAMsD,uBAAuB,EAAE;gBAC/B,KAAK,MAAMC,UAAUrC,QAAS;oBAC5B,IAAI,CAACrB,iBAAiB3D,GAAG,CAACH,YAAY,MAAMwH,OAAOC,EAAE,GAAG;wBACtDF,qBAAqBhH,IAAI,CAACiH;oBAC5B;gBACF;gBACA,IAAID,qBAAqBlI,MAAM,GAAG,GAAG;oBACnCiI,4BAA4BlC,GAAG,CAACF,KAAKqC;oBACrCF,iCAAiC;gBACnC;YACF;YAEA,IAAIA,gCAAgC;gBAClCL,2BAA2BzG,IAAI,CAC7B,IAAI,CAAC8F,iBAAiB,CAAC;oBACrBnF;oBACAI;oBACA6D,SAASmC;oBACTtH;oBACA0F,YAAY1F;oBACZ0H,YAAY;oBACZ5D;gBACF;YAEJ;QACF;QAEA,MAAM8C,QAAQC,GAAG,CAACG;IACpB;IAEAG,qCAAqC,EACnC7F,WAAW,EACX8F,YAAY,EAIb,EAAE;QACD,mCAAmC;QACnC,MAAMO,mBAAmB,IAAIzD;QAE7B,gFAAgF;QAChF,MAAM0D,gBAAgB,IAAI7H;QAC1B,MAAM8H,eAAe,IAAI9H;QAEzB,MAAM+H,iBAAiB,CAAC,EACtBzD,YAAY,EACZW,cAAc,EAIf;YACC,MAAM+C,sBAAsB,CAACnG;oBAUTlE;gBATlB,IAAI,CAACkE,KAAK;gBAEV,MAAMM,cAAc8F,kBAAkBpG;gBAEtC,IAAI,CAACM,aAAa;gBAElB,IAAI0F,cAAczH,GAAG,CAAC+B,cAAc;gBACpC0F,cAActH,GAAG,CAAC4B;gBAElB,MAAM+F,aAAYvK,0BAAAA,mBAAmBkE,KAAKsG,GAAG,qBAA3BxK,wBAA6BuK,SAAS;gBACxD,IAAIA,WAAW;oBACbN,iBAAiBvC,GAAG,CAClBlD,aACAtD,OAAOC,OAAO,CAACoJ,WAAWE,GAAG,CAAC,CAAC,CAACV,IAAIW,aAAa,GAAM,CAAA;4BACrDX;4BACAW;wBACF,CAAA;gBAEJ;gBAEA,8CAA8C;gBAC9C9K,2BAA2BsE,KAAKN,YAAYwB,WAAW,EAAEmC,OAAO,CAC9D,CAACb;oBACC2D,oBACE3D,WAAWY,cAAc;gBAE7B;YAEJ;YAEA,yEAAyE;YACzE,IACEX,gBACA,CAACA,aAAahE,QAAQ,CAAC,oCACvB;gBACA,2DAA2D;gBAC3D0H,oBAAoB/C;YACtB;QACF;QAEA,KAAK,MAAMqD,mBAAmBjB,aAAc;YAC1C,MAAMkB,iBACJhH,YAAYwB,WAAW,CAACyF,iBAAiB,CAACF;YAC5C,KAAK,MAAMjE,cAAc9G,2BACvBgL,gBACAhH,YAAYwB,WAAW,EACtB;gBACD,MAAM0F,YAAYpE,WAAWE,UAAU;gBACvC,MAAMC,UAAU,AAACiE,UAA8CjE,OAAO;gBAEtE,oEAAoE;gBACpE,oEAAoE;gBACpE,IAAIsD,aAAa1H,GAAG,CAACoE,UAAU;gBAC/BsD,aAAavH,GAAG,CAACiE;gBAEjBuD,eAAe;oBACbzD,cAAcE;oBACdS,gBAAgBZ,WAAWY,cAAc;gBAC3C;YACF;QACF;QAEA,OAAO2C;IACT;IAEA5C,8CAA8C,EAC5CV,YAAY,EACZ/C,WAAW,EACX0D,cAAc,EAKf,EAIC;QACA,gFAAgF;QAChF,MAAMyD,oCAAoC,IAAI1I;QAE9C,mBAAmB;QACnB,MAAM8E,yBAAiD,CAAC;QACxD,MAAMC,gBAAgD,EAAE;QACxD,MAAM4D,aAAa,IAAI3I;QAEvB,MAAM4I,yBAAyB,CAC7B/G,KACAgH;gBAqBkBlL;YAnBlB,IAAI,CAACkE,KAAK;YAEV,MAAMM,cAAc8F,kBAAkBpG;YAEtC,IAAI,CAACM,aAAa;YAClB,IAAIuG,kCAAkCtI,GAAG,CAAC+B,cAAc;gBACtD,IAAI2C,sBAAsB,CAAC3C,YAAY,EAAE;oBACvC2G,gBACEjH,KACAM,aACA2C,wBACA+D,qBACA;gBAEJ;gBACA;YACF;YACAH,kCAAkCnI,GAAG,CAAC4B;YAEtC,MAAM+F,aAAYvK,0BAAAA,mBAAmBkE,KAAKsG,GAAG,qBAA3BxK,wBAA6BuK,SAAS;YACxD,IAAIA,WAAW;gBACbnD,cAAcvE,IAAI,CAAC;oBACjB2B;oBACAtD,OAAOC,OAAO,CAACoJ,WAAWE,GAAG,CAAC,CAAC,CAACV,IAAIW,aAAa,GAAM,CAAA;4BACrDX;4BACAW;wBACF,CAAA;iBACD;YACH;YAEA,IAAInL,SAAS2E,MAAM;gBACjB,MAAMkH,iBACJlH,IAAImH,WAAW,IAAI,AAACnH,IAAImH,WAAW,CAASD,cAAc;gBAE5D,IAAIA,gBAAgB;oBAClB,MAAME,SAAS,CAAC1H,YAAYwB,WAAW,CACpCmG,cAAc,CAACrH,KACfsH,YAAY,CAAC,IAAI,CAAClI,cAAc;oBAEnC,IAAIgI,QAAQ;gBACd;gBAEAN,WAAWpI,GAAG,CAAC4B;YACjB,OAAO,IAAIlF,6BAA6B4E,MAAM;gBAC5C,IAAI,CAACiD,sBAAsB,CAAC3C,YAAY,EAAE;oBACxC2C,sBAAsB,CAAC3C,YAAY,GAAG,IAAInC;gBAC5C;gBACA8I,gBACEjH,KACAM,aACA2C,wBACA+D,qBACA;gBAGF;YACF;YAEAtL,2BAA2BsE,KAAKN,YAAYwB,WAAW,EAAEmC,OAAO,CAC9D,CAACb;oBAKKA;gBAJJ,IAAI+E,gBAA0B,EAAE;gBAEhC,mEAAmE;gBACnE,6CAA6C;gBAC7C,KAAI/E,yBAAAA,WAAWE,UAAU,qBAArBF,uBAAuBgF,GAAG,EAAE;oBAC9BD,cAAc5I,IAAI,IAAI6D,WAAWE,UAAU,CAAC8E,GAAG;gBACjD,OAAO;oBACLD,gBAAgB;wBAAC;qBAAI;gBACvB;gBAEAR,uBAAuBvE,WAAWY,cAAc,EAAEmE;YACpD;QAEJ;QAEA,2DAA2D;QAC3DR,uBAAuB3D,gBAAgB,EAAE;QAEzC,OAAO;YACLH;YACA5E,YAAYyI,WAAWtC,IAAI,GACvB;gBACE,CAAC/B,aAAa,EAAEgF,MAAMC,IAAI,CAACZ;YAC7B,IACA,CAAC;YACL5D;QACF;IACF;IAEAiB,+BAA+B,EAC7B7E,QAAQ,EACRI,WAAW,EACXtB,SAAS,EACTgG,aAAa,EACbN,UAAU,EACVE,gBAAgB,EAQjB,EAKC;QACA,IAAIa,mBAAmB;QAEvB,MAAM8C,UAAU3K,OAAO2G,IAAI,CAACS,eACzBlH,IAAI,CAAC,CAACC,GAAGC,IAAO9B,SAASsM,IAAI,CAACxK,KAAK,IAAID,EAAE0K,aAAa,CAACzK,IACvDmJ,GAAG,CAAC,CAACuB,mBAAsB,CAAA;gBAC1BnF,SAASmF;gBACTN,KAAK;uBAAIpD,aAAa,CAAC0D,iBAAiB;iBAAC;YAC3C,CAAA;QAEF,uEAAuE;QACvE,0EAA0E;QAC1E,gBAAgB;QAChB,MAAMC,sBAAsB,CAAC,gCAAgC,EAAE3N,UAAU;YACvEuN,SAAS,AAAC,CAAA,IAAI,CAAC1I,YAAY,GACvB0I,QAAQpB,GAAG,CAAC,CAAC,EAAE5D,OAAO,EAAE6E,GAAG,EAAE,GAAM,CAAA;oBACjC7E,SAASA,QAAQ7B,OAAO,CACtB,mCACA,cAAcA,OAAO,CAAC,OAAOzG,KAAK2N,GAAG;oBAEvCR;gBACF,CAAA,KACAG,OAAM,EACRpB,GAAG,CAAC,CAAC0B,IAAMC,KAAK9N,SAAS,CAAC6N;YAC5BE,QAAQ;QACV,GAAG,CAAC,CAAC;QAEL,MAAMC,qBAAqB,CAAC,gCAAgC,EAAEhO,UAAU;YACtEuN,SAASA,QAAQpB,GAAG,CAAC,CAAC0B,IAAMC,KAAK9N,SAAS,CAAC6N;YAC3CE,QAAQ;QACV,GAAG,CAAC,CAAC;QAEL,iCAAiC;QACjC,2CAA2C;QAC3C,IAAI,IAAI,CAACpJ,GAAG,EAAE;YACZ,MAAM9B,UAAUzC,WAAW8E,SAASqF,UAAU;YAC9C,MAAM0D,UAAU3N,YACdK,eAAegK,MAAM,EACrBlJ,WAAWyM,GAAG,EACdxE;YAGF,IAAI,CAAC7G,OAAO,CAACoL,QAAQ,EAAE;gBACrBpL,OAAO,CAACoL,QAAQ,GAAG;oBACjBE,MAAM9N,WAAW+N,WAAW;oBAC5BC,eAAe,IAAItK,IAAI;wBAACC;qBAAU;oBAClCsK,uBAAuB1E;oBACvBF;oBACAnB,SAASoF;oBACTY,SAAS;oBACTC,gBAAgBC,KAAKC,GAAG;gBAC1B;gBACAjE,mBAAmB;YACrB,OAAO;gBACL,MAAMkE,YAAY9L,OAAO,CAACoL,QAAQ;gBAClC,mCAAmC;gBACnC,IAAIU,UAAUpG,OAAO,KAAKoF,qBAAqB;oBAC7CgB,UAAUpG,OAAO,GAAGoF;oBACpBlD,mBAAmB;gBACrB;gBACA,IAAIkE,UAAUR,IAAI,KAAK9N,WAAW+N,WAAW,EAAE;oBAC7CO,UAAUN,aAAa,CAAC/J,GAAG,CAACN;gBAC9B;gBACA2K,UAAUJ,OAAO,GAAG;gBACpBI,UAAUH,cAAc,GAAGC,KAAKC,GAAG;YACrC;QACF,OAAO;YACL3M,YAAYS,qBAAqB,CAACkH,WAAW,GAAGiE;QAClD;QAEA,MAAMiB,6BAA6B9O,QAAQ+O,WAAW,CAACC,gBAAgB,CACrEd,oBACA;YAAExK,MAAMkG;QAAW;QAGrB,MAAMqF,6BAA6BjP,QAAQ+O,WAAW,CAACC,gBAAgB,CACrEd,oBACA;YAAExK,MAAMkG;QAAW;QAGrB,OAAO;YACLe;YACA,yEAAyE;YACzE,yEAAyE;YACzE,sBAAsB;YACtB,IAAI,CAACuE,QAAQ,CAAC1J,aAAaJ,SAASuB,OAAO,EAAEmI,4BAA4B;gBACvEpL,MAAMQ;gBACNqC,OAAO9F,eAAeyG,mBAAmB;YAC3C;YACA,IAAI,CAACgI,QAAQ,CAAC1J,aAAaJ,SAASuB,OAAO,EAAEsI,4BAA4B;gBACvEvL,MAAMQ;gBACNqC,OAAO9F,eAAe+F,qBAAqB;YAC7C;YACAsI;SACD;IACH;IAEAvE,kBAAkB,EAChBnF,QAAQ,EACRI,WAAW,EACX6D,OAAO,EACPnF,SAAS,EACT0F,UAAU,EACVgC,UAAU,EACV5D,gBAAgB,EASjB,EAAE;QACD,MAAMmH,eAAe5B,MAAMC,IAAI,CAACnE,QAAQtG,OAAO;QAC/C,KAAK,MAAM,GAAGqM,kBAAkB,IAAI/F,QAAS;YAC3C,KAAK,MAAM,EAAEsC,EAAE,EAAE,IAAIyD,kBAAmB;gBACtCpH,iBAAiBxD,GAAG,CAACN,YAAY,MAAMyH;YACzC;QACF;QAEA,IAAIwD,aAAa5L,MAAM,KAAK,GAAG;YAC7B,OAAOuH,QAAQuE,OAAO;QACxB;QAEA,MAAMC,eAAe,CAAC,gCAAgC,EAAEpP,UAAU;YAChEmJ,SAAS2E,KAAK9N,SAAS,CACrBiP;YAEFI,qBAAqB3D;QACvB,GAAG,CAAC,CAAC;QAEL,MAAM4D,+BAA+B,IAAI,CAACzK,YAAY,GAClD9C,YAAYE,iBAAiB,GAC7BF,YAAYC,aAAa;QAE7B,KAAK,MAAM,GAAGkN,kBAAkB,IAAID,aAAc;YAChD,KAAK,MAAM,EAAExD,EAAE,EAAE,IAAIyD,kBAAmB;gBACtC,IAAI,OAAOI,4BAA4B,CAAC7D,GAAG,KAAK,aAAa;oBAC3D6D,4BAA4B,CAAC7D,GAAG,GAAG;wBACjC8D,SAAS,CAAC;wBACVlJ,OAAO,CAAC;oBACV;gBACF;gBACAiJ,4BAA4B,CAAC7D,GAAG,CAAC8D,OAAO,CAAC7F,WAAW,GAAG;oBACrD9C,UAAU;oBACVC,OAAO;gBACT;gBAEAyI,4BAA4B,CAAC7D,GAAG,CAACpF,KAAK,CAACqD,WAAW,GAAGgC,aACjDnL,eAAeiP,aAAa,GAC5BjP,eAAe+F,qBAAqB;YAC1C;QACF;QAEA,0CAA0C;QAC1C,MAAMmJ,iBAAiB3P,QAAQ+O,WAAW,CAACC,gBAAgB,CAACM,cAAc;YACxE5L,MAAMkG;QACR;QAEA,OAAO,IAAI,CAACsF,QAAQ,CAClB1J,aACA,6BAA6B;QAC7BJ,SAASuB,OAAO,EAChBgJ,gBACA;YACEjM,MAAMQ;YACNqC,OAAOqF,aACHnL,eAAeiP,aAAa,GAC5BjP,eAAe+F,qBAAqB;QAC1C;IAEJ;IAEA0I,SACE1J,WAAgC,EAChCmB,OAAe,EACf6B,UAA8B,EAC9B5D,OAA6B,EACf,mBAAmB,GAAG;QACpC,OAAO,IAAIkG,QAAQ,CAACuE,SAASO;YAC3B,IAAI,YAAYpK,YAAYJ,QAAQ,EAAE;gBACpCI,YAAYqK,UAAU,CAAClJ,SAAS6B,YAAY5D,SAAS,CAACkL,KAAKC;oBACzD,IAAID,KAAK;wBACP,OAAOF,OAAOE;oBAChB;oBAEAtK,YAAYwB,WAAW,CACpBmG,cAAc,CAAC4C,QACfC,mBAAmB,CAClB,IAAI,CAACjL,YAAY,GAAGhE,uBAAuBD;oBAE/C,OAAOuO,QAAQU;gBACjB;YACF,OAAO;gBACL,MAAME,QAAQzK,YAAYzC,OAAO,CAACmN,GAAG,CAACtL,QAAQlB,IAAI;gBAClDuM,MAAME,mBAAmB,CAAC1L,IAAI,CAAC+D;gBAC/BhD,YAAYH,KAAK,CAAC6J,QAAQ,CAACkB,IAAI,CAACH,OAAcrL;gBAC9CY,YAAY6K,aAAa,CACvB;oBACE1J;oBACA6B;oBACA8H,aAAa;wBAAEC,aAAa3L,QAAQ2B,KAAK;oBAAC;gBAC5C,GACA,CAACuJ,KAAUC;oBACT,IAAID,KAAK;wBACPtK,YAAYH,KAAK,CAACmL,WAAW,CAACJ,IAAI,CAAC5H,YAAY5D,SAASkL;wBACxD,OAAOF,OAAOE;oBAChB;oBAEAtK,YAAYH,KAAK,CAACoL,YAAY,CAACL,IAAI,CAAC5H,YAAY5D,SAASmL;oBAEzDvK,YAAYwB,WAAW,CACpBmG,cAAc,CAAC4C,QACfC,mBAAmB,CAClB,IAAI,CAACjL,YAAY,GACbhE,uBACAD;oBAGR,OAAOuO,QAAQU;gBACjB;YAEJ;QACF;IACF;IAEA,MAAMpI,mBAAmBnC,WAAgC,EAAE;QACzD,MAAMtD,gBAAwC,CAAC;QAC/C,MAAMC,oBAA4C,CAAC;QAEnDd,gBAAgBmE,aAAa,CAACM,KAAKsB,QAAQsJ,YAAY7K;YACrD,yEAAyE;YACzE,IACE6K,WAAWhN,IAAI,IACfoC,IAAI2C,OAAO,IACX5C,SACA,kCAAkC6H,IAAI,CAAC5H,IAAI2C,OAAO,GAClD;gBACA,MAAMmD,aAAa,4BAA4B8B,IAAI,CAAC5H,IAAI2C,OAAO;gBAE/D,MAAMkI,UAAU,IAAI,CAAC5L,YAAY,GAC7B9C,YAAYI,uBAAuB,GACnCJ,YAAYG,mBAAmB;gBAEnC,IAAI,CAACuO,OAAO,CAACD,WAAWhN,IAAI,CAAC,EAAE;oBAC7BiN,OAAO,CAACD,WAAWhN,IAAI,CAAC,GAAG,CAAC;gBAC9B;gBACAiN,OAAO,CAACD,WAAWhN,IAAI,CAAC,CAACkI,aAAa,WAAW,SAAS,GAAG;oBAC3D9E,UAAUjB;oBACVkB,OAAOvB,YAAYwB,WAAW,CAACC,OAAO,CAACnB;gBACzC;YACF;QACF;QAEA,IAAK,IAAI6F,MAAM1J,YAAYC,aAAa,CAAE;YACxC,MAAMwJ,SAASzJ,YAAYC,aAAa,CAACyJ,GAAG;YAC5C,IAAK,IAAIjI,QAAQgI,OAAO+D,OAAO,CAAE;gBAC/B,MAAM5J,QACJ5D,YAAYG,mBAAmB,CAACsB,KAAK,CACnCgI,OAAOnF,KAAK,CAAC7C,KAAK,KAAKjD,eAAeiP,aAAa,GAC/C,WACA,SACL;gBACHhE,OAAO+D,OAAO,CAAC/L,KAAK,GAAGmC;YACzB;YACA3D,aAAa,CAACyJ,GAAG,GAAGD;QACtB;QAEA,IAAK,IAAIC,MAAM1J,YAAYE,iBAAiB,CAAE;YAC5C,MAAMuJ,SAASzJ,YAAYE,iBAAiB,CAACwJ,GAAG;YAChD,IAAK,IAAIjI,QAAQgI,OAAO+D,OAAO,CAAE;gBAC/B,MAAM5J,QACJ5D,YAAYI,uBAAuB,CAACqB,KAAK,CACvCgI,OAAOnF,KAAK,CAAC7C,KAAK,KAAKjD,eAAeiP,aAAa,GAC/C,WACA,SACL;gBACHhE,OAAO+D,OAAO,CAAC/L,KAAK,GAAGmC;YACzB;YACA1D,iBAAiB,CAACwJ,GAAG,GAAGD;QAC1B;QAEA,MAAMkF,iBAAiB;YACrBC,MAAM3O;YACN4O,MAAM3O;YACN8C,eAAe,IAAI,CAACA,aAAa;QACnC;QACA,MAAM8L,qBAAqB;YACzB,GAAGH,cAAc;YACjB3L,eAAe;QACjB;QAEA,MAAM+L,OAAOhD,KAAK9N,SAAS,CAAC0Q,gBAAgB,MAAM,IAAI,CAAC/L,GAAG,GAAG,IAAIoM;QACjE,MAAMC,WAAWlD,KAAK9N,SAAS,CAC7B6Q,oBACA,MACA,IAAI,CAAClM,GAAG,GAAG,IAAIoM;QAGjBzL,YAAY2L,SAAS,CACnB,GAAG,IAAI,CAACnM,WAAW,GAAGhE,0BAA0B,GAAG,CAAC,EACpD,IAAIZ,QAAQgR,SAAS,CACnB,CAAC,2BAA2B,EAAEpD,KAAK9N,SAAS,CAACgR,WAAW;QAG5D1L,YAAY2L,SAAS,CACnB,GAAG,IAAI,CAACnM,WAAW,GAAGhE,0BAA0B,KAAK,CAAC,EACtD,IAAIZ,QAAQgR,SAAS,CAACJ;IAE1B;AACF;AAEA,SAASjE,gBACPjH,GAAyB,EACzBuL,UAAkB,EAClBtI,sBAA8C,EAC9C+D,mBAA6B,EAC7BwE,kBAA2B;QAEH1P;IAAxB,MAAM2P,mBAAkB3P,0BAAAA,mBAAmBkE,KAAKsG,GAAG,qBAA3BxK,wBAA6B2P,eAAe;IACpE,MAAMC,cAAcD,oBAAoB;IACxC,MAAME,oBAAoB5P,qBACxBiE,KACA0L,cAAc,aAAa;IAG7B,MAAME,mBAAmB3I,sBAAsB,CAACsI,WAAW;IAE3D,IAAIvE,mBAAmB,CAAC,EAAE,KAAK,KAAK;QAClC,kEAAkE;QAClE,qDAAqD;QACrD,sCAAsC;QACtC,IAAI,CAACwE,sBAAsB;eAAII;SAAiB,CAAC,EAAE,KAAK,KAAK;YAC3D3I,sBAAsB,CAACsI,WAAW,GAAG,IAAIpN,IAAI;gBAAC;aAAI;QACpD;IACF,OAAO;QACL,MAAM0N,yBAAyBF,sBAAsB;QACrD,IAAIE,wBAAwB;YAC1B5I,sBAAsB,CAACsI,WAAW,GAAG,IAAIpN,IAAI;gBAAC;aAAI;QACpD,OAAO;YACL,gGAAgG;YAChG,oEAAoE;YACpE,KAAK,MAAMP,QAAQoJ,oBAAqB;gBACtC,mEAAmE;gBACnE,MAAM8E,qBAAqBJ,eAAe9N,SAAS;gBAEnD,kEAAkE;gBAClE,4DAA4D;gBAC5D,IAAIkO,oBAAoB;oBACtB7I,sBAAsB,CAACsI,WAAW,CAAC7M,GAAG,CAAC;gBACzC;gBAEAuE,sBAAsB,CAACsI,WAAW,CAAC7M,GAAG,CAACd;YACzC;QACF;IACF;AACF;AAEA,SAASwI,kBAAkBpG,GAAyB;QAC1BA,0BACPA,2BAebA;IAhBJ,MAAMC,UAAkBD,EAAAA,2BAAAA,IAAIG,mBAAmB,qBAAvBH,yBAAyB3F,IAAI,KAAI;IACzD,MAAM+F,WAAWJ,EAAAA,4BAAAA,IAAIG,mBAAmB,qBAAvBH,0BAAyBK,KAAK,KAAI;IACnD,mEAAmE;IACnE,yEAAyE;IACzE,0EAA0E;IAC1E,IAAIC,cAAsBL,UAAUG;IAEpC,6EAA6E;IAC7E,IAAIJ,IAAInB,WAAW,CAACjB,IAAI,KAAK,iBAAiB;QAC5C0C,cAAcN,IAAI+L,UAAU;IAC9B;IAEA,yEAAyE;IACzE,yEAAyE;IACzE,0EAA0E;IAC1E,wEAAwE;IACxE,KAAI/L,qBAAAA,IAAIE,aAAa,qBAAjBF,mBAAmBO,UAAU,CAACzF,6BAA6B;QAC7DwF,cAAcN,IAAIE,aAAa,GAAG,MAAMI;IAC1C;IAEA,IAAIN,IAAIQ,QAAQ,KAAK,CAAC,CAAC,EAAE5F,yBAAyBiI,aAAa,EAAE,EAAE;QACjE,OAAOG,yBAAyBhD,IAAIgM,UAAU,EAAElJ,QAAQ;IAC1D;IAEA,OAAOxC;AACT;AAEA,SAAS0C,yBAAyBL,OAAe;IAC/C,sHAAsH;IACtH,MAAMtC,QAAQsC,QAAQnF,KAAK,CAAC,IAAI,CAAC,EAAE,CAACA,KAAK,CAAC,8BAA8B,CAAC,EAAE;IAE3E,OAAOrD,MAAMkG;AACf"}