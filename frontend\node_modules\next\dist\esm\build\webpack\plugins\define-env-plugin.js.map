{"version": 3, "sources": ["../../../../src/build/webpack/plugins/define-env-plugin.ts"], "sourcesContent": ["import type {\n  I18NDoma<PERSON>,\n  NextConfigComplete,\n} from '../../../server/config-shared'\nimport type { MiddlewareMatcher } from '../../analysis/get-page-static-info'\nimport { webpack } from 'next/dist/compiled/webpack/webpack'\nimport { needsExperimentalReact } from '../../../lib/needs-experimental-react'\nimport { checkIsAppPPREnabled } from '../../../server/lib/experimental/ppr'\n\nfunction errorIfEnvConflicted(config: NextConfigComplete, key: string) {\n  const isPrivateKey = /^(?:NODE_.+)|^(?:__.+)$/i.test(key)\n  const hasNextRuntimeKey = key === 'NEXT_RUNTIME'\n\n  if (isPrivateKey || hasNextRuntimeKey) {\n    throw new Error(\n      `The key \"${key}\" under \"env\" in ${config.configFileName} is not allowed. https://nextjs.org/docs/messages/env-key-not-allowed`\n    )\n  }\n}\n\ntype BloomFilter = ReturnType<\n  import('../../../shared/lib/bloom-filter').BloomFilter['export']\n>\n\nexport interface DefineEnvPluginOptions {\n  isTurbopack: boolean\n  clientRouterFilters?: {\n    staticFilter: BloomFilter\n    dynamicFilter: BloomFilter\n  }\n  config: NextConfigComplete\n  dev: boolean\n  distDir: string\n  fetchCacheKeyPrefix: string | undefined\n  hasRewrites: boolean\n  isClient: boolean\n  isEdgeServer: boolean\n  isNodeOrEdgeCompilation: boolean\n  isNodeServer: boolean\n  middlewareMatchers: MiddlewareMatcher[] | undefined\n}\n\ninterface DefineEnv {\n  [key: string]:\n    | string\n    | string[]\n    | boolean\n    | MiddlewareMatcher[]\n    | BloomFilter\n    | Partial<NextConfigComplete['images']>\n    | I18NDomains\n}\n\ninterface SerializedDefineEnv {\n  [key: string]: string\n}\n\n/**\n * Collects all environment variables that are using the `NEXT_PUBLIC_` prefix.\n */\nexport function getNextPublicEnvironmentVariables(): DefineEnv {\n  const defineEnv: DefineEnv = {}\n  for (const key in process.env) {\n    if (key.startsWith('NEXT_PUBLIC_')) {\n      const value = process.env[key]\n      if (value != null) {\n        defineEnv[`process.env.${key}`] = value\n      }\n    }\n  }\n  return defineEnv\n}\n\n/**\n * Collects the `env` config value from the Next.js config.\n */\nexport function getNextConfigEnv(config: NextConfigComplete): DefineEnv {\n  // Refactored code below to use for-of\n  const defineEnv: DefineEnv = {}\n  const env = config.env\n  for (const key in env) {\n    const value = env[key]\n    if (value != null) {\n      errorIfEnvConflicted(config, key)\n      defineEnv[`process.env.${key}`] = value\n    }\n  }\n  return defineEnv\n}\n\n/**\n * Serializes the DefineEnv config so that it can be inserted into the code by Webpack/Turbopack, JSON stringifies each value.\n */\nfunction serializeDefineEnv(defineEnv: DefineEnv): SerializedDefineEnv {\n  const defineEnvStringified: SerializedDefineEnv = {}\n  for (const key in defineEnv) {\n    const value = defineEnv[key]\n    defineEnvStringified[key] = JSON.stringify(value)\n  }\n\n  return defineEnvStringified\n}\n\nfunction getImageConfig(\n  config: NextConfigComplete,\n  dev: boolean\n): { 'process.env.__NEXT_IMAGE_OPTS': Partial<NextConfigComplete['images']> } {\n  return {\n    'process.env.__NEXT_IMAGE_OPTS': {\n      deviceSizes: config.images.deviceSizes,\n      imageSizes: config.images.imageSizes,\n      qualities: config.images.qualities,\n      path: config.images.path,\n      loader: config.images.loader,\n      dangerouslyAllowSVG: config.images.dangerouslyAllowSVG,\n      unoptimized: config?.images?.unoptimized,\n      ...(dev\n        ? {\n            // additional config in dev to allow validating on the client\n            domains: config.images.domains,\n            remotePatterns: config.images?.remotePatterns,\n            localPatterns: config.images?.localPatterns,\n            output: config.output,\n          }\n        : {}),\n    },\n  }\n}\n\nexport function getDefineEnv({\n  isTurbopack,\n  clientRouterFilters,\n  config,\n  dev,\n  distDir,\n  fetchCacheKeyPrefix,\n  hasRewrites,\n  isClient,\n  isEdgeServer,\n  isNodeOrEdgeCompilation,\n  isNodeServer,\n  middlewareMatchers,\n}: DefineEnvPluginOptions): SerializedDefineEnv {\n  const nextPublicEnv = getNextPublicEnvironmentVariables()\n  const nextConfigEnv = getNextConfigEnv(config)\n\n  const isPPREnabled = checkIsAppPPREnabled(config.experimental.ppr)\n  const isDynamicIOEnabled = !!config.experimental.dynamicIO\n  const isUseCacheEnabled = !!config.experimental.useCache\n\n  const defineEnv: DefineEnv = {\n    // internal field to identify the plugin config\n    __NEXT_DEFINE_ENV: true,\n\n    ...nextPublicEnv,\n    ...nextConfigEnv,\n    ...(!isEdgeServer\n      ? {}\n      : {\n          EdgeRuntime:\n            /**\n             * Cloud providers can set this environment variable to allow users\n             * and library authors to have different implementations based on\n             * the runtime they are running with, if it's not using `edge-runtime`\n             */\n            process.env.NEXT_EDGE_RUNTIME_PROVIDER ?? 'edge-runtime',\n\n          // process should be only { env: {...} } for edge runtime.\n          // For ignore avoid warn on `process.emit` usage but directly omit it.\n          'process.emit': false,\n        }),\n    'process.turbopack': isTurbopack,\n    'process.env.TURBOPACK': isTurbopack,\n    // TODO: enforce `NODE_ENV` on `process.env`, and add a test:\n    'process.env.NODE_ENV':\n      dev || config.experimental.allowDevelopmentBuild\n        ? 'development'\n        : 'production',\n    'process.env.NEXT_RUNTIME': isEdgeServer\n      ? 'edge'\n      : isNodeServer\n        ? 'nodejs'\n        : '',\n    'process.env.NEXT_MINIMAL': '',\n    'process.env.__NEXT_APP_NAV_FAIL_HANDLING': Boolean(\n      config.experimental.appNavFailHandling\n    ),\n    'process.env.__NEXT_PPR': isPPREnabled,\n    'process.env.__NEXT_DYNAMIC_IO': isDynamicIOEnabled,\n    'process.env.__NEXT_USE_CACHE': isUseCacheEnabled,\n    'process.env.NEXT_DEPLOYMENT_ID': config.deploymentId || false,\n    // Propagates the `__NEXT_EXPERIMENTAL_STATIC_SHELL_DEBUGGING` environment\n    // variable to the client.\n    'process.env.__NEXT_EXPERIMENTAL_STATIC_SHELL_DEBUGGING':\n      process.env.__NEXT_EXPERIMENTAL_STATIC_SHELL_DEBUGGING || false,\n    'process.env.__NEXT_FETCH_CACHE_KEY_PREFIX': fetchCacheKeyPrefix ?? '',\n    ...(isTurbopack\n      ? {}\n      : {\n          'process.env.__NEXT_MIDDLEWARE_MATCHERS': middlewareMatchers ?? [],\n        }),\n    'process.env.__NEXT_MANUAL_CLIENT_BASE_PATH':\n      config.experimental.manualClientBasePath ?? false,\n    'process.env.__NEXT_CLIENT_ROUTER_DYNAMIC_STALETIME': JSON.stringify(\n      isNaN(Number(config.experimental.staleTimes?.dynamic))\n        ? 0\n        : config.experimental.staleTimes?.dynamic\n    ),\n    'process.env.__NEXT_CLIENT_ROUTER_STATIC_STALETIME': JSON.stringify(\n      isNaN(Number(config.experimental.staleTimes?.static))\n        ? 5 * 60 // 5 minutes\n        : config.experimental.staleTimes?.static\n    ),\n    'process.env.__NEXT_CLIENT_ROUTER_FILTER_ENABLED':\n      config.experimental.clientRouterFilter ?? true,\n    'process.env.__NEXT_CLIENT_ROUTER_S_FILTER':\n      clientRouterFilters?.staticFilter ?? false,\n    'process.env.__NEXT_CLIENT_ROUTER_D_FILTER':\n      clientRouterFilters?.dynamicFilter ?? false,\n    'process.env.__NEXT_CLIENT_SEGMENT_CACHE': Boolean(\n      config.experimental.clientSegmentCache\n    ),\n    'process.env.__NEXT_OPTIMISTIC_CLIENT_CACHE':\n      config.experimental.optimisticClientCache ?? true,\n    'process.env.__NEXT_MIDDLEWARE_PREFETCH':\n      config.experimental.middlewarePrefetch ?? 'flexible',\n    'process.env.__NEXT_CROSS_ORIGIN': config.crossOrigin,\n    'process.browser': isClient,\n    'process.env.__NEXT_TEST_MODE': process.env.__NEXT_TEST_MODE ?? false,\n    // This is used in client/dev-error-overlay/hot-dev-client.js to replace the dist directory\n    ...(dev && (isClient ?? isEdgeServer)\n      ? {\n          'process.env.__NEXT_DIST_DIR': distDir,\n        }\n      : {}),\n    'process.env.__NEXT_TRAILING_SLASH': config.trailingSlash,\n    'process.env.__NEXT_DEV_INDICATOR': config.devIndicators !== false,\n    'process.env.__NEXT_DEV_INDICATOR_POSITION':\n      config.devIndicators === false\n        ? 'bottom-left' // This will not be used as the indicator is disabled.\n        : config.devIndicators.position ?? 'bottom-left',\n    'process.env.__NEXT_STRICT_MODE':\n      config.reactStrictMode === null ? false : config.reactStrictMode,\n    'process.env.__NEXT_STRICT_MODE_APP':\n      // When next.config.js does not have reactStrictMode it's enabled by default.\n      config.reactStrictMode === null ? true : config.reactStrictMode,\n    'process.env.__NEXT_OPTIMIZE_CSS':\n      (config.experimental.optimizeCss && !dev) ?? false,\n    'process.env.__NEXT_SCRIPT_WORKERS':\n      (config.experimental.nextScriptWorkers && !dev) ?? false,\n    'process.env.__NEXT_SCROLL_RESTORATION':\n      config.experimental.scrollRestoration ?? false,\n    ...getImageConfig(config, dev),\n    'process.env.__NEXT_ROUTER_BASEPATH': config.basePath,\n    'process.env.__NEXT_STRICT_NEXT_HEAD':\n      config.experimental.strictNextHead ?? true,\n    'process.env.__NEXT_HAS_REWRITES': hasRewrites,\n    'process.env.__NEXT_CONFIG_OUTPUT': config.output,\n    'process.env.__NEXT_I18N_SUPPORT': !!config.i18n,\n    'process.env.__NEXT_I18N_DOMAINS': config.i18n?.domains ?? false,\n    'process.env.__NEXT_NO_MIDDLEWARE_URL_NORMALIZE':\n      config.skipMiddlewareUrlNormalize,\n    'process.env.__NEXT_EXTERNAL_MIDDLEWARE_REWRITE_RESOLVE':\n      config.experimental.externalMiddlewareRewritesResolve ?? false,\n    'process.env.__NEXT_MANUAL_TRAILING_SLASH':\n      config.skipTrailingSlashRedirect,\n    'process.env.__NEXT_HAS_WEB_VITALS_ATTRIBUTION':\n      (config.experimental.webVitalsAttribution &&\n        config.experimental.webVitalsAttribution.length > 0) ??\n      false,\n    'process.env.__NEXT_WEB_VITALS_ATTRIBUTION':\n      config.experimental.webVitalsAttribution ?? false,\n    'process.env.__NEXT_LINK_NO_TOUCH_START':\n      config.experimental.linkNoTouchStart ?? false,\n    'process.env.__NEXT_ASSET_PREFIX': config.assetPrefix,\n    'process.env.__NEXT_EXPERIMENTAL_AUTH_INTERRUPTS':\n      !!config.experimental.authInterrupts,\n    'process.env.__NEXT_TELEMETRY_DISABLED': Boolean(\n      process.env.NEXT_TELEMETRY_DISABLED\n    ),\n    ...(isNodeOrEdgeCompilation\n      ? {\n          // Fix bad-actors in the npm ecosystem (e.g. `node-formidable`)\n          // This is typically found in unmaintained modules from the\n          // pre-webpack era (common in server-side code)\n          'global.GENTLY': false,\n        }\n      : undefined),\n    ...(isNodeOrEdgeCompilation\n      ? {\n          'process.env.__NEXT_EXPERIMENTAL_REACT':\n            needsExperimentalReact(config),\n        }\n      : undefined),\n  }\n\n  const userDefines = config.compiler?.define ?? {}\n  for (const key in userDefines) {\n    if (defineEnv.hasOwnProperty(key)) {\n      throw new Error(\n        `The \\`compiler.define\\` option is configured to replace the \\`${key}\\` variable. This variable is either part of a Next.js built-in or is already configured via the \\`env\\` option.`\n      )\n    }\n    defineEnv[key] = userDefines[key]\n  }\n\n  return serializeDefineEnv(defineEnv)\n}\n\nexport function getDefineEnvPlugin(options: DefineEnvPluginOptions) {\n  return new webpack.DefinePlugin(getDefineEnv(options))\n}\n"], "names": ["webpack", "needsExperimentalReact", "checkIsAppPPREnabled", "errorIfEnvConflicted", "config", "key", "isPrivateKey", "test", "hasNextRuntimeKey", "Error", "configFileName", "getNextPublicEnvironmentVariables", "defineEnv", "process", "env", "startsWith", "value", "getNextConfigEnv", "serializeDefineEnv", "defineEnvStringified", "JSON", "stringify", "getImageConfig", "dev", "deviceSizes", "images", "imageSizes", "qualities", "path", "loader", "dangerouslyAllowSVG", "unoptimized", "domains", "remotePatterns", "localPatterns", "output", "getDefineEnv", "isTurbopack", "clientRouterFilters", "distDir", "fetchCacheKeyPrefix", "hasRewrites", "isClient", "isEdgeServer", "isNodeOrEdgeCompilation", "isNodeServer", "middlewareMatchers", "nextPublicEnv", "nextConfigEnv", "isPPREnabled", "experimental", "ppr", "isDynamicIOEnabled", "dynamicIO", "isUseCacheEnabled", "useCache", "__NEXT_DEFINE_ENV", "EdgeRuntime", "NEXT_EDGE_RUNTIME_PROVIDER", "allowDevelopmentBuild", "Boolean", "appNavFailHandling", "deploymentId", "__NEXT_EXPERIMENTAL_STATIC_SHELL_DEBUGGING", "manualClientBasePath", "isNaN", "Number", "staleTimes", "dynamic", "static", "clientRouterFilter", "staticFilter", "dynamicFilter", "clientSegmentCache", "optimisticClientCache", "middlewarePrefetch", "crossOrigin", "__NEXT_TEST_MODE", "trailingSlash", "devIndicators", "position", "reactStrictMode", "optimizeCss", "nextScriptWorkers", "scrollRestoration", "basePath", "strictNextHead", "i18n", "skipMiddlewareUrlNormalize", "externalMiddlewareRewritesResolve", "skipTrailingSlashRedirect", "webVitalsAttribution", "length", "linkNoTouchStart", "assetPrefix", "authInterrupts", "NEXT_TELEMETRY_DISABLED", "undefined", "userDefines", "compiler", "define", "hasOwnProperty", "getDefineEnvPlugin", "options", "DefinePlugin"], "mappings": "AAKA,SAASA,OAAO,QAAQ,qCAAoC;AAC5D,SAASC,sBAAsB,QAAQ,wCAAuC;AAC9E,SAASC,oBAAoB,QAAQ,uCAAsC;AAE3E,SAASC,qBAAqBC,MAA0B,EAAEC,GAAW;IACnE,MAAMC,eAAe,2BAA2BC,IAAI,CAACF;IACrD,MAAMG,oBAAoBH,QAAQ;IAElC,IAAIC,gBAAgBE,mBAAmB;QACrC,MAAM,qBAEL,CAFK,IAAIC,MACR,CAAC,SAAS,EAAEJ,IAAI,iBAAiB,EAAED,OAAOM,cAAc,CAAC,qEAAqE,CAAC,GAD3H,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;AACF;AAuCA;;CAEC,GACD,OAAO,SAASC;IACd,MAAMC,YAAuB,CAAC;IAC9B,IAAK,MAAMP,OAAOQ,QAAQC,GAAG,CAAE;QAC7B,IAAIT,IAAIU,UAAU,CAAC,iBAAiB;YAClC,MAAMC,QAAQH,QAAQC,GAAG,CAACT,IAAI;YAC9B,IAAIW,SAAS,MAAM;gBACjBJ,SAAS,CAAC,CAAC,YAAY,EAAEP,KAAK,CAAC,GAAGW;YACpC;QACF;IACF;IACA,OAAOJ;AACT;AAEA;;CAEC,GACD,OAAO,SAASK,iBAAiBb,MAA0B;IACzD,sCAAsC;IACtC,MAAMQ,YAAuB,CAAC;IAC9B,MAAME,MAAMV,OAAOU,GAAG;IACtB,IAAK,MAAMT,OAAOS,IAAK;QACrB,MAAME,QAAQF,GAAG,CAACT,IAAI;QACtB,IAAIW,SAAS,MAAM;YACjBb,qBAAqBC,QAAQC;YAC7BO,SAAS,CAAC,CAAC,YAAY,EAAEP,KAAK,CAAC,GAAGW;QACpC;IACF;IACA,OAAOJ;AACT;AAEA;;CAEC,GACD,SAASM,mBAAmBN,SAAoB;IAC9C,MAAMO,uBAA4C,CAAC;IACnD,IAAK,MAAMd,OAAOO,UAAW;QAC3B,MAAMI,QAAQJ,SAAS,CAACP,IAAI;QAC5Bc,oBAAoB,CAACd,IAAI,GAAGe,KAAKC,SAAS,CAACL;IAC7C;IAEA,OAAOG;AACT;AAEA,SAASG,eACPlB,MAA0B,EAC1BmB,GAAY;QAUKnB,gBAKSA,iBACDA;IAdzB,OAAO;QACL,iCAAiC;YAC/BoB,aAAapB,OAAOqB,MAAM,CAACD,WAAW;YACtCE,YAAYtB,OAAOqB,MAAM,CAACC,UAAU;YACpCC,WAAWvB,OAAOqB,MAAM,CAACE,SAAS;YAClCC,MAAMxB,OAAOqB,MAAM,CAACG,IAAI;YACxBC,QAAQzB,OAAOqB,MAAM,CAACI,MAAM;YAC5BC,qBAAqB1B,OAAOqB,MAAM,CAACK,mBAAmB;YACtDC,WAAW,EAAE3B,2BAAAA,iBAAAA,OAAQqB,MAAM,qBAAdrB,eAAgB2B,WAAW;YACxC,GAAIR,MACA;gBACE,6DAA6D;gBAC7DS,SAAS5B,OAAOqB,MAAM,CAACO,OAAO;gBAC9BC,cAAc,GAAE7B,kBAAAA,OAAOqB,MAAM,qBAAbrB,gBAAe6B,cAAc;gBAC7CC,aAAa,GAAE9B,kBAAAA,OAAOqB,MAAM,qBAAbrB,gBAAe8B,aAAa;gBAC3CC,QAAQ/B,OAAO+B,MAAM;YACvB,IACA,CAAC,CAAC;QACR;IACF;AACF;AAEA,OAAO,SAASC,aAAa,EAC3BC,WAAW,EACXC,mBAAmB,EACnBlC,MAAM,EACNmB,GAAG,EACHgB,OAAO,EACPC,mBAAmB,EACnBC,WAAW,EACXC,QAAQ,EACRC,YAAY,EACZC,uBAAuB,EACvBC,YAAY,EACZC,kBAAkB,EACK;QA8DN1C,iCAETA,kCAGSA,kCAETA,kCAgD6BA,cAqCjBA;IAzJpB,MAAM2C,gBAAgBpC;IACtB,MAAMqC,gBAAgB/B,iBAAiBb;IAEvC,MAAM6C,eAAe/C,qBAAqBE,OAAO8C,YAAY,CAACC,GAAG;IACjE,MAAMC,qBAAqB,CAAC,CAAChD,OAAO8C,YAAY,CAACG,SAAS;IAC1D,MAAMC,oBAAoB,CAAC,CAAClD,OAAO8C,YAAY,CAACK,QAAQ;IAExD,MAAM3C,YAAuB;QAC3B,+CAA+C;QAC/C4C,mBAAmB;QAEnB,GAAGT,aAAa;QAChB,GAAGC,aAAa;QAChB,GAAI,CAACL,eACD,CAAC,IACD;YACEc,aACE;;;;aAIC,GACD5C,QAAQC,GAAG,CAAC4C,0BAA0B,IAAI;YAE5C,0DAA0D;YAC1D,sEAAsE;YACtE,gBAAgB;QAClB,CAAC;QACL,qBAAqBrB;QACrB,yBAAyBA;QACzB,6DAA6D;QAC7D,wBACEd,OAAOnB,OAAO8C,YAAY,CAACS,qBAAqB,GAC5C,gBACA;QACN,4BAA4BhB,eACxB,SACAE,eACE,WACA;QACN,4BAA4B;QAC5B,4CAA4Ce,QAC1CxD,OAAO8C,YAAY,CAACW,kBAAkB;QAExC,0BAA0BZ;QAC1B,iCAAiCG;QACjC,gCAAgCE;QAChC,kCAAkClD,OAAO0D,YAAY,IAAI;QACzD,0EAA0E;QAC1E,0BAA0B;QAC1B,0DACEjD,QAAQC,GAAG,CAACiD,0CAA0C,IAAI;QAC5D,6CAA6CvB,uBAAuB;QACpE,GAAIH,cACA,CAAC,IACD;YACE,0CAA0CS,sBAAsB,EAAE;QACpE,CAAC;QACL,8CACE1C,OAAO8C,YAAY,CAACc,oBAAoB,IAAI;QAC9C,sDAAsD5C,KAAKC,SAAS,CAClE4C,MAAMC,QAAO9D,kCAAAA,OAAO8C,YAAY,CAACiB,UAAU,qBAA9B/D,gCAAgCgE,OAAO,KAChD,KACAhE,mCAAAA,OAAO8C,YAAY,CAACiB,UAAU,qBAA9B/D,iCAAgCgE,OAAO;QAE7C,qDAAqDhD,KAAKC,SAAS,CACjE4C,MAAMC,QAAO9D,mCAAAA,OAAO8C,YAAY,CAACiB,UAAU,qBAA9B/D,iCAAgCiE,MAAM,KAC/C,IAAI,GAAG,YAAY;YACnBjE,mCAAAA,OAAO8C,YAAY,CAACiB,UAAU,qBAA9B/D,iCAAgCiE,MAAM;QAE5C,mDACEjE,OAAO8C,YAAY,CAACoB,kBAAkB,IAAI;QAC5C,6CACEhC,CAAAA,uCAAAA,oBAAqBiC,YAAY,KAAI;QACvC,6CACEjC,CAAAA,uCAAAA,oBAAqBkC,aAAa,KAAI;QACxC,2CAA2CZ,QACzCxD,OAAO8C,YAAY,CAACuB,kBAAkB;QAExC,8CACErE,OAAO8C,YAAY,CAACwB,qBAAqB,IAAI;QAC/C,0CACEtE,OAAO8C,YAAY,CAACyB,kBAAkB,IAAI;QAC5C,mCAAmCvE,OAAOwE,WAAW;QACrD,mBAAmBlC;QACnB,gCAAgC7B,QAAQC,GAAG,CAAC+D,gBAAgB,IAAI;QAChE,2FAA2F;QAC3F,GAAItD,OAAQmB,CAAAA,YAAYC,YAAW,IAC/B;YACE,+BAA+BJ;QACjC,IACA,CAAC,CAAC;QACN,qCAAqCnC,OAAO0E,aAAa;QACzD,oCAAoC1E,OAAO2E,aAAa,KAAK;QAC7D,6CACE3E,OAAO2E,aAAa,KAAK,QACrB,cAAc,sDAAsD;WACpE3E,OAAO2E,aAAa,CAACC,QAAQ,IAAI;QACvC,kCACE5E,OAAO6E,eAAe,KAAK,OAAO,QAAQ7E,OAAO6E,eAAe;QAClE,sCACE,6EAA6E;QAC7E7E,OAAO6E,eAAe,KAAK,OAAO,OAAO7E,OAAO6E,eAAe;QACjE,mCACE,AAAC7E,CAAAA,OAAO8C,YAAY,CAACgC,WAAW,IAAI,CAAC3D,GAAE,KAAM;QAC/C,qCACE,AAACnB,CAAAA,OAAO8C,YAAY,CAACiC,iBAAiB,IAAI,CAAC5D,GAAE,KAAM;QACrD,yCACEnB,OAAO8C,YAAY,CAACkC,iBAAiB,IAAI;QAC3C,GAAG9D,eAAelB,QAAQmB,IAAI;QAC9B,sCAAsCnB,OAAOiF,QAAQ;QACrD,uCACEjF,OAAO8C,YAAY,CAACoC,cAAc,IAAI;QACxC,mCAAmC7C;QACnC,oCAAoCrC,OAAO+B,MAAM;QACjD,mCAAmC,CAAC,CAAC/B,OAAOmF,IAAI;QAChD,mCAAmCnF,EAAAA,eAAAA,OAAOmF,IAAI,qBAAXnF,aAAa4B,OAAO,KAAI;QAC3D,kDACE5B,OAAOoF,0BAA0B;QACnC,0DACEpF,OAAO8C,YAAY,CAACuC,iCAAiC,IAAI;QAC3D,4CACErF,OAAOsF,yBAAyB;QAClC,iDACE,AAACtF,CAAAA,OAAO8C,YAAY,CAACyC,oBAAoB,IACvCvF,OAAO8C,YAAY,CAACyC,oBAAoB,CAACC,MAAM,GAAG,CAAA,KACpD;QACF,6CACExF,OAAO8C,YAAY,CAACyC,oBAAoB,IAAI;QAC9C,0CACEvF,OAAO8C,YAAY,CAAC2C,gBAAgB,IAAI;QAC1C,mCAAmCzF,OAAO0F,WAAW;QACrD,mDACE,CAAC,CAAC1F,OAAO8C,YAAY,CAAC6C,cAAc;QACtC,yCAAyCnC,QACvC/C,QAAQC,GAAG,CAACkF,uBAAuB;QAErC,GAAIpD,0BACA;YACE,+DAA+D;YAC/D,2DAA2D;YAC3D,+CAA+C;YAC/C,iBAAiB;QACnB,IACAqD,SAAS;QACb,GAAIrD,0BACA;YACE,yCACE3C,uBAAuBG;QAC3B,IACA6F,SAAS;IACf;IAEA,MAAMC,cAAc9F,EAAAA,mBAAAA,OAAO+F,QAAQ,qBAAf/F,iBAAiBgG,MAAM,KAAI,CAAC;IAChD,IAAK,MAAM/F,OAAO6F,YAAa;QAC7B,IAAItF,UAAUyF,cAAc,CAAChG,MAAM;YACjC,MAAM,qBAEL,CAFK,IAAII,MACR,CAAC,8DAA8D,EAAEJ,IAAI,gHAAgH,CAAC,GADlL,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QACAO,SAAS,CAACP,IAAI,GAAG6F,WAAW,CAAC7F,IAAI;IACnC;IAEA,OAAOa,mBAAmBN;AAC5B;AAEA,OAAO,SAAS0F,mBAAmBC,OAA+B;IAChE,OAAO,IAAIvG,QAAQwG,YAAY,CAACpE,aAAamE;AAC/C"}