{"version": 3, "sources": ["../../../../../../../src/client/components/react-dev-overlay/ui/components/dialog/dialog-footer.tsx"], "sourcesContent": ["export type DialogFooterProps = {\n  children?: React.ReactNode\n  className?: string\n}\n\nexport function DialogFooter({ children, className }: DialogFooterProps) {\n  return (\n    <div data-nextjs-dialog-footer className={className}>\n      {children}\n    </div>\n  )\n}\n"], "names": ["<PERSON><PERSON><PERSON><PERSON>er", "children", "className", "div", "data-nextjs-dialog-footer"], "mappings": ";AAKA,OAAO,SAASA,aAAa,KAA0C;IAA1C,IAAA,EAAEC,QAAQ,EAAEC,SAAS,EAAqB,GAA1C;IAC3B,qBACE,KAACC;QAAIC,2BAAyB;QAACF,WAAWA;kBACvCD;;AAGP"}