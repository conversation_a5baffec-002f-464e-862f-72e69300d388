{"version": 3, "sources": ["../../../../src/client/components/segment-cache-impl/navigation.ts"], "sourcesContent": ["import type {\n  CacheNodeSeedData,\n  FlightRouterState,\n  FlightSegmentPath,\n} from '../../../server/app-render/types'\nimport type {\n  CacheNode,\n  HeadData,\n  LoadingModuleData,\n} from '../../../shared/lib/app-router-context.shared-runtime'\nimport type { NormalizedFlightData } from '../../flight-data-helpers'\nimport { fetchServerResponse } from '../router-reducer/fetch-server-response'\nimport {\n  startPPRNavigation,\n  listenForDynamicRequest,\n  type Task as PPRNavigationTask,\n} from '../router-reducer/ppr-navigations'\nimport { createHrefFromUrl as createCanonicalUrl } from '../router-reducer/create-href-from-url'\nimport {\n  EntryStatus,\n  readRouteCacheEntry,\n  readSegmentCacheEntry,\n  waitForSegmentCacheEntry,\n  type RouteTree,\n} from './cache'\nimport { createCacheKey, type RouteCacheKey } from './cache-key'\nimport {\n  addSearchParamsIfPageSegment,\n  PAGE_SEGMENT_KEY,\n} from '../../../shared/lib/segment'\nimport { NavigationResultTag } from '../segment-cache'\n\ntype MPANavigationResult = {\n  tag: NavigationResultTag.MPA\n  data: string\n}\n\ntype NoOpNavigationResult = {\n  tag: NavigationResultTag.NoOp\n  data: {\n    canonicalUrl: string\n    shouldScroll: boolean\n  }\n}\n\ntype SuccessfulNavigationResult = {\n  tag: NavigationResultTag.Success\n  data: {\n    flightRouterState: FlightRouterState\n    cacheNode: CacheNode\n    canonicalUrl: string\n    scrollableSegments: Array<FlightSegmentPath>\n    shouldScroll: boolean\n    hash: string\n  }\n}\n\ntype AsyncNavigationResult = {\n  tag: NavigationResultTag.Async\n  data: Promise<\n    MPANavigationResult | NoOpNavigationResult | SuccessfulNavigationResult\n  >\n}\n\nexport type NavigationResult =\n  | MPANavigationResult\n  | SuccessfulNavigationResult\n  | NoOpNavigationResult\n  | AsyncNavigationResult\n\n/**\n * Navigate to a new URL, using the Segment Cache to construct a response.\n *\n * To allow for synchronous navigations whenever possible, this is not an async\n * function. It returns a promise only if there's no matching prefetch in\n * the cache. Otherwise it returns an immediate result and uses Suspense/RSC to\n * stream in any missing data.\n */\nexport function navigate(\n  url: URL,\n  currentCacheNode: CacheNode,\n  currentFlightRouterState: FlightRouterState,\n  nextUrl: string | null,\n  shouldScroll: boolean\n): NavigationResult {\n  const now = Date.now()\n  const href = url.href\n\n  // We special case navigations to the exact same URL as the current location.\n  // It's a common UI pattern for apps to refresh when you click a link to the\n  // current page. So when this happens, we refresh the dynamic data in the page\n  // segments.\n  //\n  // Note that this does not apply if the any part of the hash or search query\n  // has changed. This might feel a bit weird but it makes more sense when you\n  // consider that the way to trigger this behavior is to click the same link\n  // multiple times.\n  //\n  // TODO: We should probably refresh the *entire* route when this case occurs,\n  // not just the page segments. Essentially treating it the same as a refresh()\n  // triggered by an action, which is the more explicit way of modeling the UI\n  // pattern described above.\n  //\n  // Also note that this only refreshes the dynamic data, not static/ cached\n  // data. If the page segment is fully static and prefetched, the request is\n  // skipped. (This is also how refresh() works.)\n  const isSamePageNavigation =\n    // TODO: This is not the only place we read from the location, but we should\n    // consider storing the current URL in the router state instead of reading\n    // from the location object. In practice I don't think this matters much\n    // since we keep them in sync anyway, but having two sources of truth can\n    // lead to subtle bugs and race conditions.\n    href === window.location.href\n\n  const cacheKey = createCacheKey(href, nextUrl)\n  const route = readRouteCacheEntry(now, cacheKey)\n  if (route !== null && route.status === EntryStatus.Fulfilled) {\n    // We have a matching prefetch.\n    const snapshot = readRenderSnapshotFromCache(now, cacheKey, route.tree)\n    const prefetchFlightRouterState = snapshot.flightRouterState\n    const prefetchSeedData = snapshot.seedData\n    const prefetchHead = route.head\n    const isPrefetchHeadPartial = route.isHeadPartial\n    const newCanonicalUrl = route.canonicalUrl\n    return navigateUsingPrefetchedRouteTree(\n      url,\n      nextUrl,\n      isSamePageNavigation,\n      currentCacheNode,\n      currentFlightRouterState,\n      prefetchFlightRouterState,\n      prefetchSeedData,\n      prefetchHead,\n      isPrefetchHeadPartial,\n      newCanonicalUrl,\n      shouldScroll,\n      url.hash\n    )\n  }\n  // There's no matching prefetch for this route in the cache.\n  return {\n    tag: NavigationResultTag.Async,\n    data: navigateDynamicallyWithNoPrefetch(\n      url,\n      nextUrl,\n      isSamePageNavigation,\n      currentCacheNode,\n      currentFlightRouterState,\n      shouldScroll,\n      url.hash\n    ),\n  }\n}\n\nfunction navigateUsingPrefetchedRouteTree(\n  url: URL,\n  nextUrl: string | null,\n  isSamePageNavigation: boolean,\n  currentCacheNode: CacheNode,\n  currentFlightRouterState: FlightRouterState,\n  prefetchFlightRouterState: FlightRouterState,\n  prefetchSeedData: CacheNodeSeedData | null,\n  prefetchHead: HeadData | null,\n  isPrefetchHeadPartial: boolean,\n  canonicalUrl: string,\n  shouldScroll: boolean,\n  hash: string\n): SuccessfulNavigationResult | NoOpNavigationResult | MPANavigationResult {\n  // Recursively construct a prefetch tree by reading from the Segment Cache. To\n  // maintain compatibility, we output the same data structures as the old\n  // prefetching implementation: FlightRouterState and CacheNodeSeedData.\n  // TODO: Eventually updateCacheNodeOnNavigation (or the equivalent) should\n  // read from the Segment Cache directly. It's only structured this way for now\n  // so we can share code with the old prefetching implementation.\n  const scrollableSegments: Array<FlightSegmentPath> = []\n  const task = startPPRNavigation(\n    currentCacheNode,\n    currentFlightRouterState,\n    prefetchFlightRouterState,\n    prefetchSeedData,\n    prefetchHead,\n    isPrefetchHeadPartial,\n    isSamePageNavigation,\n    scrollableSegments\n  )\n  if (task !== null) {\n    const dynamicRequestTree = task.dynamicRequestTree\n    if (dynamicRequestTree !== null) {\n      const promiseForDynamicServerResponse = fetchServerResponse(url, {\n        flightRouterState: dynamicRequestTree,\n        nextUrl,\n      })\n      listenForDynamicRequest(task, promiseForDynamicServerResponse)\n    } else {\n      // The prefetched tree does not contain dynamic holes — it's\n      // fully static. We can skip the dynamic request.\n    }\n    return navigationTaskToResult(\n      task,\n      currentCacheNode,\n      canonicalUrl,\n      scrollableSegments,\n      shouldScroll,\n      hash\n    )\n  }\n  // The server sent back an empty tree patch. There's nothing to update, except\n  // possibly the URL.\n  return {\n    tag: NavigationResultTag.NoOp,\n    data: {\n      canonicalUrl,\n      shouldScroll,\n    },\n  }\n}\n\nfunction navigationTaskToResult(\n  task: PPRNavigationTask,\n  currentCacheNode: CacheNode,\n  canonicalUrl: string,\n  scrollableSegments: Array<FlightSegmentPath>,\n  shouldScroll: boolean,\n  hash: string\n): SuccessfulNavigationResult | MPANavigationResult {\n  const flightRouterState = task.route\n  if (flightRouterState === null) {\n    // When no router state is provided, it signals that we should perform an\n    // MPA navigation.\n    return {\n      tag: NavigationResultTag.MPA,\n      data: canonicalUrl,\n    }\n  }\n  const newCacheNode = task.node\n  return {\n    tag: NavigationResultTag.Success,\n    data: {\n      flightRouterState,\n      cacheNode: newCacheNode !== null ? newCacheNode : currentCacheNode,\n      canonicalUrl,\n      scrollableSegments,\n      shouldScroll,\n      hash,\n    },\n  }\n}\n\nfunction readRenderSnapshotFromCache(\n  now: number,\n  routeCacheKey: RouteCacheKey,\n  tree: RouteTree\n): { flightRouterState: FlightRouterState; seedData: CacheNodeSeedData } {\n  let childRouterStates: { [parallelRouteKey: string]: FlightRouterState } = {}\n  let childSeedDatas: {\n    [parallelRouteKey: string]: CacheNodeSeedData | null\n  } = {}\n  const slots = tree.slots\n  if (slots !== null) {\n    for (const parallelRouteKey in slots) {\n      const childTree = slots[parallelRouteKey]\n      const childResult = readRenderSnapshotFromCache(\n        now,\n        routeCacheKey,\n        childTree\n      )\n      childRouterStates[parallelRouteKey] = childResult.flightRouterState\n      childSeedDatas[parallelRouteKey] = childResult.seedData\n    }\n  }\n\n  let rsc: React.ReactNode | null = null\n  let loading: LoadingModuleData | Promise<LoadingModuleData> = null\n  let isPartial: boolean = true\n\n  const segmentEntry = readSegmentCacheEntry(now, routeCacheKey, tree.key)\n  if (segmentEntry !== null) {\n    switch (segmentEntry.status) {\n      case EntryStatus.Fulfilled: {\n        // Happy path: a cache hit\n        rsc = segmentEntry.rsc\n        loading = segmentEntry.loading\n        isPartial = segmentEntry.isPartial\n        break\n      }\n      case EntryStatus.Pending: {\n        // We haven't received data for this segment yet, but there's already\n        // an in-progress request. Since it's extremely likely to arrive\n        // before the dynamic data response, we might as well use it.\n        const promiseForFulfilledEntry = waitForSegmentCacheEntry(segmentEntry)\n        rsc = promiseForFulfilledEntry.then((entry) =>\n          entry !== null ? entry.rsc : null\n        )\n        loading = promiseForFulfilledEntry.then((entry) =>\n          entry !== null ? entry.loading : null\n        )\n        // Since we don't know yet whether the segment is partial or fully\n        // static, we must assume it's partial; we can't skip the\n        // dynamic request.\n        isPartial = true\n        break\n      }\n      case EntryStatus.Empty:\n      case EntryStatus.Rejected:\n        break\n      default:\n        segmentEntry satisfies never\n    }\n  }\n\n  const segment =\n    tree.segment === PAGE_SEGMENT_KEY && routeCacheKey.search\n      ? // The navigation implementation expects the search params to be\n        // included in the segment. However, the Segment Cache tracks search\n        // params separately from the rest of the segment key. So we need to\n        // add them back here.\n        //\n        // See corresponding comment in convertFlightRouterStateToTree.\n        //\n        // TODO: What we should do instead is update the navigation diffing\n        // logic to compare search params explicitly. This is a temporary\n        // solution until more of the Segment Cache implementation has settled.\n        addSearchParamsIfPageSegment(\n          tree.segment,\n          Object.fromEntries(new URLSearchParams(routeCacheKey.search))\n        )\n      : tree.segment\n\n  return {\n    flightRouterState: [\n      segment,\n      childRouterStates,\n      null,\n      null,\n      tree.isRootLayout,\n    ],\n    seedData: [segment, rsc, childSeedDatas, loading, isPartial],\n  }\n}\n\nasync function navigateDynamicallyWithNoPrefetch(\n  url: URL,\n  nextUrl: string | null,\n  isSamePageNavigation: boolean,\n  currentCacheNode: CacheNode,\n  currentFlightRouterState: FlightRouterState,\n  shouldScroll: boolean,\n  hash: string\n): Promise<\n  MPANavigationResult | SuccessfulNavigationResult | NoOpNavigationResult\n> {\n  // Runs when a navigation happens but there's no cached prefetch we can use.\n  // Don't bother to wait for a prefetch response; go straight to a full\n  // navigation that contains both static and dynamic data in a single stream.\n  // (This is unlike the old navigation implementation, which instead blocks\n  // the dynamic request until a prefetch request is received.)\n  //\n  // To avoid duplication of logic, we're going to pretend that the tree\n  // returned by the dynamic request is, in fact, a prefetch tree. Then we can\n  // use the same server response to write the actual data into the CacheNode\n  // tree. So it's the same flow as the \"happy path\" (prefetch, then\n  // navigation), except we use a single server response for both stages.\n\n  const promiseForDynamicServerResponse = fetchServerResponse(url, {\n    flightRouterState: currentFlightRouterState,\n    nextUrl,\n  })\n  const { flightData, canonicalUrl: canonicalUrlOverride } =\n    await promiseForDynamicServerResponse\n\n  if (typeof flightData === 'string') {\n    // This is an MPA navigation.\n    const newUrl = flightData\n    return {\n      tag: NavigationResultTag.MPA,\n      data: newUrl,\n    }\n  }\n\n  // Since the response format of dynamic requests and prefetches is slightly\n  // different, we'll need to massage the data a bit. Create FlightRouterState\n  // tree that simulates what we'd receive as the result of a prefetch.\n  const prefetchFlightRouterState = simulatePrefetchTreeUsingDynamicTreePatch(\n    currentFlightRouterState,\n    flightData\n  )\n\n  // In our simulated prefetch payload, we pretend that there's no seed data\n  // nor a prefetch head.\n  const prefetchSeedData = null\n  const prefetchHead = null\n  const isPrefetchHeadPartial = true\n\n  const canonicalUrl = createCanonicalUrl(\n    canonicalUrlOverride ? canonicalUrlOverride : url\n  )\n\n  // Now we proceed exactly as we would for normal navigation.\n  const scrollableSegments: Array<FlightSegmentPath> = []\n  const task = startPPRNavigation(\n    currentCacheNode,\n    currentFlightRouterState,\n    prefetchFlightRouterState,\n    prefetchSeedData,\n    prefetchHead,\n    isPrefetchHeadPartial,\n    isSamePageNavigation,\n    scrollableSegments\n  )\n  if (task !== null) {\n    // In this case, we've already sent the dynamic request, so we don't\n    // actually use the request tree created by `startPPRNavigation`,\n    // except to check if it contains dynamic holes.\n    //\n    // This is almost always true, but it could be false if all the segment data\n    // was present in the cache, but the route tree was not. E.g. navigating\n    // to a URL that was not prefetched but rewrites to a different URL\n    // that was.\n    const hasDynamicHoles = task.dynamicRequestTree !== null\n    if (hasDynamicHoles) {\n      listenForDynamicRequest(task, promiseForDynamicServerResponse)\n    } else {\n      // The prefetched tree does not contain dynamic holes — it's\n      // fully static. We don't need to process the server response further.\n    }\n    return navigationTaskToResult(\n      task,\n      currentCacheNode,\n      canonicalUrl,\n      scrollableSegments,\n      shouldScroll,\n      hash\n    )\n  }\n  // The server sent back an empty tree patch. There's nothing to update, except\n  // possibly the URL.\n  return {\n    tag: NavigationResultTag.NoOp,\n    data: {\n      canonicalUrl,\n      shouldScroll,\n    },\n  }\n}\n\nfunction simulatePrefetchTreeUsingDynamicTreePatch(\n  currentTree: FlightRouterState,\n  flightData: Array<NormalizedFlightData>\n): FlightRouterState {\n  // Takes the current FlightRouterState and applies the router state patch\n  // received from the server, to create a full FlightRouterState tree that we\n  // can pretend was returned by a prefetch.\n  //\n  // (It sounds similar to what applyRouterStatePatch does, but it doesn't need\n  // to handle stuff like interception routes or diffing since that will be\n  // handled later.)\n  let baseTree = currentTree\n  for (const { segmentPath, tree: treePatch } of flightData) {\n    // If the server sends us multiple tree patches, we only need to clone the\n    // base tree when applying the first patch. After the first patch, we can\n    // apply the remaining patches in place without copying.\n    const canMutateInPlace = baseTree !== currentTree\n    baseTree = simulatePrefetchTreeUsingDynamicTreePatchImpl(\n      baseTree,\n      treePatch,\n      segmentPath,\n      canMutateInPlace,\n      0\n    )\n  }\n\n  return baseTree\n}\n\nfunction simulatePrefetchTreeUsingDynamicTreePatchImpl(\n  baseRouterState: FlightRouterState,\n  patch: FlightRouterState,\n  segmentPath: FlightSegmentPath,\n  canMutateInPlace: boolean,\n  index: number\n) {\n  if (index === segmentPath.length) {\n    // We reached the part of the tree that we need to patch.\n    return patch\n  }\n\n  // segmentPath represents the parent path of subtree. It's a repeating\n  // pattern of parallel route key and segment:\n  //\n  //   [string, Segment, string, Segment, string, Segment, ...]\n  //\n  // This path tells us which part of the base tree to apply the tree patch.\n  //\n  // NOTE: In the case of a fully dynamic request with no prefetch, we receive\n  // the FlightRouterState patch in the same request as the dynamic data.\n  // Therefore we don't need to worry about diffing the segment values; we can\n  // assume the server sent us a correct result.\n  const updatedParallelRouteKey: string = segmentPath[index]\n  // const segment: Segment = segmentPath[index + 1] <-- Not used, see note above\n\n  const baseChildren = baseRouterState[1]\n  const newChildren: { [parallelRouteKey: string]: FlightRouterState } = {}\n  for (const parallelRouteKey in baseChildren) {\n    if (parallelRouteKey === updatedParallelRouteKey) {\n      const childBaseRouterState = baseChildren[parallelRouteKey]\n      newChildren[parallelRouteKey] =\n        simulatePrefetchTreeUsingDynamicTreePatchImpl(\n          childBaseRouterState,\n          patch,\n          segmentPath,\n          canMutateInPlace,\n          // Advance the index by two and keep cloning until we reach\n          // the end of the segment path.\n          index + 2\n        )\n    } else {\n      // This child is not being patched. Copy it over as-is.\n      newChildren[parallelRouteKey] = baseChildren[parallelRouteKey]\n    }\n  }\n\n  if (canMutateInPlace) {\n    // We can mutate the base tree in place, because the base tree is already\n    // a clone.\n    baseRouterState[1] = newChildren\n    return baseRouterState\n  }\n\n  // Clone all the fields except the children.\n  //\n  // Based on equivalent logic in apply-router-state-patch-to-tree, but should\n  // confirm whether we need to copy all of these fields. Not sure the server\n  // ever sends, e.g. the refetch marker.\n  const clone: FlightRouterState = [baseRouterState[0], newChildren]\n  if (2 in baseRouterState) {\n    clone[2] = baseRouterState[2]\n  }\n  if (3 in baseRouterState) {\n    clone[3] = baseRouterState[3]\n  }\n  if (4 in baseRouterState) {\n    clone[4] = baseRouterState[4]\n  }\n  return clone\n}\n"], "names": ["navigate", "url", "currentCacheNode", "currentFlightRouterState", "nextUrl", "shouldScroll", "now", "Date", "href", "isSamePageNavigation", "window", "location", "cache<PERSON>ey", "createCacheKey", "route", "readRouteCacheEntry", "status", "EntryStatus", "Fulfilled", "snapshot", "readRenderSnapshotFromCache", "tree", "prefetchFlightRouterState", "flightRouterState", "prefetchSeedData", "seedData", "prefetchHead", "head", "isPrefetchHeadPartial", "isHeadPartial", "newCanonicalUrl", "canonicalUrl", "navigateUsingPrefetchedRouteTree", "hash", "tag", "NavigationResultTag", "Async", "data", "navigateDynamicallyWithNoPrefetch", "scrollableSegments", "task", "startPPRNavigation", "dynamicRequestTree", "promiseForDynamicServerResponse", "fetchServerResponse", "listenForDynamicRequest", "navigationTaskToResult", "NoOp", "MPA", "newCacheNode", "node", "Success", "cacheNode", "routeCacheKey", "childRouterStates", "childSeedDatas", "slots", "parallelRouteKey", "childTree", "childResult", "rsc", "loading", "isPartial", "segmentEntry", "readSegmentCacheEntry", "key", "Pending", "promiseForFulfilledEntry", "waitForSegmentCacheEntry", "then", "entry", "Empty", "Rejected", "segment", "PAGE_SEGMENT_KEY", "search", "addSearchParamsIfPageSegment", "Object", "fromEntries", "URLSearchParams", "isRootLayout", "flightData", "canonicalUrlOverride", "newUrl", "simulatePrefetchTreeUsingDynamicTreePatch", "createCanonicalUrl", "hasDynamicHoles", "currentTree", "baseTree", "segmentPath", "treePatch", "canMutateInPlace", "simulatePrefetchTreeUsingDynamicTreePatchImpl", "baseRouterState", "patch", "index", "length", "updatedParallelRouteKey", "baseChildren", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "childBaseRouterState", "clone"], "mappings": ";;;;+BA8EgBA;;;eAAAA;;;qCAnEoB;gCAK7B;mCACiD;uBAOjD;0BAC4C;yBAI5C;8BAC6B;AAgD7B,SAASA,SACdC,GAAQ,EACRC,gBAA2B,EAC3BC,wBAA2C,EAC3CC,OAAsB,EACtBC,YAAqB;IAErB,MAAMC,MAAMC,KAAKD,GAAG;IACpB,MAAME,OAAOP,IAAIO,IAAI;IAErB,6EAA6E;IAC7E,4EAA4E;IAC5E,8EAA8E;IAC9E,YAAY;IACZ,EAAE;IACF,4EAA4E;IAC5E,4EAA4E;IAC5E,2EAA2E;IAC3E,kBAAkB;IAClB,EAAE;IACF,6EAA6E;IAC7E,8EAA8E;IAC9E,4EAA4E;IAC5E,2BAA2B;IAC3B,EAAE;IACF,0EAA0E;IAC1E,2EAA2E;IAC3E,+CAA+C;IAC/C,MAAMC,uBACJ,4EAA4E;IAC5E,0EAA0E;IAC1E,wEAAwE;IACxE,yEAAyE;IACzE,2CAA2C;IAC3CD,SAASE,OAAOC,QAAQ,CAACH,IAAI;IAE/B,MAAMI,WAAWC,IAAAA,wBAAc,EAACL,MAAMJ;IACtC,MAAMU,QAAQC,IAAAA,0BAAmB,EAACT,KAAKM;IACvC,IAAIE,UAAU,QAAQA,MAAME,MAAM,KAAKC,kBAAW,CAACC,SAAS,EAAE;QAC5D,+BAA+B;QAC/B,MAAMC,WAAWC,4BAA4Bd,KAAKM,UAAUE,MAAMO,IAAI;QACtE,MAAMC,4BAA4BH,SAASI,iBAAiB;QAC5D,MAAMC,mBAAmBL,SAASM,QAAQ;QAC1C,MAAMC,eAAeZ,MAAMa,IAAI;QAC/B,MAAMC,wBAAwBd,MAAMe,aAAa;QACjD,MAAMC,kBAAkBhB,MAAMiB,YAAY;QAC1C,OAAOC,iCACL/B,KACAG,SACAK,sBACAP,kBACAC,0BACAmB,2BACAE,kBACAE,cACAE,uBACAE,iBACAzB,cACAJ,IAAIgC,IAAI;IAEZ;IACA,4DAA4D;IAC5D,OAAO;QACLC,KAAKC,iCAAmB,CAACC,KAAK;QAC9BC,MAAMC,kCACJrC,KACAG,SACAK,sBACAP,kBACAC,0BACAE,cACAJ,IAAIgC,IAAI;IAEZ;AACF;AAEA,SAASD,iCACP/B,GAAQ,EACRG,OAAsB,EACtBK,oBAA6B,EAC7BP,gBAA2B,EAC3BC,wBAA2C,EAC3CmB,yBAA4C,EAC5CE,gBAA0C,EAC1CE,YAA6B,EAC7BE,qBAA8B,EAC9BG,YAAoB,EACpB1B,YAAqB,EACrB4B,IAAY;IAEZ,8EAA8E;IAC9E,wEAAwE;IACxE,uEAAuE;IACvE,0EAA0E;IAC1E,8EAA8E;IAC9E,gEAAgE;IAChE,MAAMM,qBAA+C,EAAE;IACvD,MAAMC,OAAOC,IAAAA,kCAAkB,EAC7BvC,kBACAC,0BACAmB,2BACAE,kBACAE,cACAE,uBACAnB,sBACA8B;IAEF,IAAIC,SAAS,MAAM;QACjB,MAAME,qBAAqBF,KAAKE,kBAAkB;QAClD,IAAIA,uBAAuB,MAAM;YAC/B,MAAMC,kCAAkCC,IAAAA,wCAAmB,EAAC3C,KAAK;gBAC/DsB,mBAAmBmB;gBACnBtC;YACF;YACAyC,IAAAA,uCAAuB,EAACL,MAAMG;QAChC,OAAO;QACL,4DAA4D;QAC5D,iDAAiD;QACnD;QACA,OAAOG,uBACLN,MACAtC,kBACA6B,cACAQ,oBACAlC,cACA4B;IAEJ;IACA,8EAA8E;IAC9E,oBAAoB;IACpB,OAAO;QACLC,KAAKC,iCAAmB,CAACY,IAAI;QAC7BV,MAAM;YACJN;YACA1B;QACF;IACF;AACF;AAEA,SAASyC,uBACPN,IAAuB,EACvBtC,gBAA2B,EAC3B6B,YAAoB,EACpBQ,kBAA4C,EAC5ClC,YAAqB,EACrB4B,IAAY;IAEZ,MAAMV,oBAAoBiB,KAAK1B,KAAK;IACpC,IAAIS,sBAAsB,MAAM;QAC9B,yEAAyE;QACzE,kBAAkB;QAClB,OAAO;YACLW,KAAKC,iCAAmB,CAACa,GAAG;YAC5BX,MAAMN;QACR;IACF;IACA,MAAMkB,eAAeT,KAAKU,IAAI;IAC9B,OAAO;QACLhB,KAAKC,iCAAmB,CAACgB,OAAO;QAChCd,MAAM;YACJd;YACA6B,WAAWH,iBAAiB,OAAOA,eAAe/C;YAClD6B;YACAQ;YACAlC;YACA4B;QACF;IACF;AACF;AAEA,SAASb,4BACPd,GAAW,EACX+C,aAA4B,EAC5BhC,IAAe;IAEf,IAAIiC,oBAAuE,CAAC;IAC5E,IAAIC,iBAEA,CAAC;IACL,MAAMC,QAAQnC,KAAKmC,KAAK;IACxB,IAAIA,UAAU,MAAM;QAClB,IAAK,MAAMC,oBAAoBD,MAAO;YACpC,MAAME,YAAYF,KAAK,CAACC,iBAAiB;YACzC,MAAME,cAAcvC,4BAClBd,KACA+C,eACAK;YAEFJ,iBAAiB,CAACG,iBAAiB,GAAGE,YAAYpC,iBAAiB;YACnEgC,cAAc,CAACE,iBAAiB,GAAGE,YAAYlC,QAAQ;QACzD;IACF;IAEA,IAAImC,MAA8B;IAClC,IAAIC,UAA0D;IAC9D,IAAIC,YAAqB;IAEzB,MAAMC,eAAeC,IAAAA,4BAAqB,EAAC1D,KAAK+C,eAAehC,KAAK4C,GAAG;IACvE,IAAIF,iBAAiB,MAAM;QACzB,OAAQA,aAAa/C,MAAM;YACzB,KAAKC,kBAAW,CAACC,SAAS;gBAAE;oBAC1B,0BAA0B;oBAC1B0C,MAAMG,aAAaH,GAAG;oBACtBC,UAAUE,aAAaF,OAAO;oBAC9BC,YAAYC,aAAaD,SAAS;oBAClC;gBACF;YACA,KAAK7C,kBAAW,CAACiD,OAAO;gBAAE;oBACxB,qEAAqE;oBACrE,gEAAgE;oBAChE,6DAA6D;oBAC7D,MAAMC,2BAA2BC,IAAAA,+BAAwB,EAACL;oBAC1DH,MAAMO,yBAAyBE,IAAI,CAAC,CAACC,QACnCA,UAAU,OAAOA,MAAMV,GAAG,GAAG;oBAE/BC,UAAUM,yBAAyBE,IAAI,CAAC,CAACC,QACvCA,UAAU,OAAOA,MAAMT,OAAO,GAAG;oBAEnC,kEAAkE;oBAClE,yDAAyD;oBACzD,mBAAmB;oBACnBC,YAAY;oBACZ;gBACF;YACA,KAAK7C,kBAAW,CAACsD,KAAK;YACtB,KAAKtD,kBAAW,CAACuD,QAAQ;gBACvB;YACF;gBACET;QACJ;IACF;IAEA,MAAMU,UACJpD,KAAKoD,OAAO,KAAKC,yBAAgB,IAAIrB,cAAcsB,MAAM,GAErD,oEAAoE;IACpE,oEAAoE;IACpE,sBAAsB;IACtB,EAAE;IACF,+DAA+D;IAC/D,EAAE;IACF,mEAAmE;IACnE,iEAAiE;IACjE,uEAAuE;IACvEC,IAAAA,qCAA4B,EAC1BvD,KAAKoD,OAAO,EACZI,OAAOC,WAAW,CAAC,IAAIC,gBAAgB1B,cAAcsB,MAAM,MAE7DtD,KAAKoD,OAAO;IAElB,OAAO;QACLlD,mBAAmB;YACjBkD;YACAnB;YACA;YACA;YACAjC,KAAK2D,YAAY;SAClB;QACDvD,UAAU;YAACgD;YAASb;YAAKL;YAAgBM;YAASC;SAAU;IAC9D;AACF;AAEA,eAAexB,kCACbrC,GAAQ,EACRG,OAAsB,EACtBK,oBAA6B,EAC7BP,gBAA2B,EAC3BC,wBAA2C,EAC3CE,YAAqB,EACrB4B,IAAY;IAIZ,4EAA4E;IAC5E,sEAAsE;IACtE,4EAA4E;IAC5E,0EAA0E;IAC1E,6DAA6D;IAC7D,EAAE;IACF,sEAAsE;IACtE,4EAA4E;IAC5E,2EAA2E;IAC3E,kEAAkE;IAClE,uEAAuE;IAEvE,MAAMU,kCAAkCC,IAAAA,wCAAmB,EAAC3C,KAAK;QAC/DsB,mBAAmBpB;QACnBC;IACF;IACA,MAAM,EAAE6E,UAAU,EAAElD,cAAcmD,oBAAoB,EAAE,GACtD,MAAMvC;IAER,IAAI,OAAOsC,eAAe,UAAU;QAClC,6BAA6B;QAC7B,MAAME,SAASF;QACf,OAAO;YACL/C,KAAKC,iCAAmB,CAACa,GAAG;YAC5BX,MAAM8C;QACR;IACF;IAEA,2EAA2E;IAC3E,4EAA4E;IAC5E,qEAAqE;IACrE,MAAM7D,4BAA4B8D,0CAChCjF,0BACA8E;IAGF,0EAA0E;IAC1E,uBAAuB;IACvB,MAAMzD,mBAAmB;IACzB,MAAME,eAAe;IACrB,MAAME,wBAAwB;IAE9B,MAAMG,eAAesD,IAAAA,oCAAkB,EACrCH,uBAAuBA,uBAAuBjF;IAGhD,4DAA4D;IAC5D,MAAMsC,qBAA+C,EAAE;IACvD,MAAMC,OAAOC,IAAAA,kCAAkB,EAC7BvC,kBACAC,0BACAmB,2BACAE,kBACAE,cACAE,uBACAnB,sBACA8B;IAEF,IAAIC,SAAS,MAAM;QACjB,oEAAoE;QACpE,iEAAiE;QACjE,gDAAgD;QAChD,EAAE;QACF,4EAA4E;QAC5E,wEAAwE;QACxE,mEAAmE;QACnE,YAAY;QACZ,MAAM8C,kBAAkB9C,KAAKE,kBAAkB,KAAK;QACpD,IAAI4C,iBAAiB;YACnBzC,IAAAA,uCAAuB,EAACL,MAAMG;QAChC,OAAO;QACL,4DAA4D;QAC5D,sEAAsE;QACxE;QACA,OAAOG,uBACLN,MACAtC,kBACA6B,cACAQ,oBACAlC,cACA4B;IAEJ;IACA,8EAA8E;IAC9E,oBAAoB;IACpB,OAAO;QACLC,KAAKC,iCAAmB,CAACY,IAAI;QAC7BV,MAAM;YACJN;YACA1B;QACF;IACF;AACF;AAEA,SAAS+E,0CACPG,WAA8B,EAC9BN,UAAuC;IAEvC,yEAAyE;IACzE,4EAA4E;IAC5E,0CAA0C;IAC1C,EAAE;IACF,6EAA6E;IAC7E,yEAAyE;IACzE,kBAAkB;IAClB,IAAIO,WAAWD;IACf,KAAK,MAAM,EAAEE,WAAW,EAAEpE,MAAMqE,SAAS,EAAE,IAAIT,WAAY;QACzD,0EAA0E;QAC1E,yEAAyE;QACzE,wDAAwD;QACxD,MAAMU,mBAAmBH,aAAaD;QACtCC,WAAWI,8CACTJ,UACAE,WACAD,aACAE,kBACA;IAEJ;IAEA,OAAOH;AACT;AAEA,SAASI,8CACPC,eAAkC,EAClCC,KAAwB,EACxBL,WAA8B,EAC9BE,gBAAyB,EACzBI,KAAa;IAEb,IAAIA,UAAUN,YAAYO,MAAM,EAAE;QAChC,yDAAyD;QACzD,OAAOF;IACT;IAEA,sEAAsE;IACtE,6CAA6C;IAC7C,EAAE;IACF,6DAA6D;IAC7D,EAAE;IACF,0EAA0E;IAC1E,EAAE;IACF,4EAA4E;IAC5E,uEAAuE;IACvE,4EAA4E;IAC5E,8CAA8C;IAC9C,MAAMG,0BAAkCR,WAAW,CAACM,MAAM;IAC1D,+EAA+E;IAE/E,MAAMG,eAAeL,eAAe,CAAC,EAAE;IACvC,MAAMM,cAAiE,CAAC;IACxE,IAAK,MAAM1C,oBAAoByC,aAAc;QAC3C,IAAIzC,qBAAqBwC,yBAAyB;YAChD,MAAMG,uBAAuBF,YAAY,CAACzC,iBAAiB;YAC3D0C,WAAW,CAAC1C,iBAAiB,GAC3BmC,8CACEQ,sBACAN,OACAL,aACAE,kBACA,2DAA2D;YAC3D,+BAA+B;YAC/BI,QAAQ;QAEd,OAAO;YACL,uDAAuD;YACvDI,WAAW,CAAC1C,iBAAiB,GAAGyC,YAAY,CAACzC,iBAAiB;QAChE;IACF;IAEA,IAAIkC,kBAAkB;QACpB,yEAAyE;QACzE,WAAW;QACXE,eAAe,CAAC,EAAE,GAAGM;QACrB,OAAON;IACT;IAEA,4CAA4C;IAC5C,EAAE;IACF,4EAA4E;IAC5E,2EAA2E;IAC3E,uCAAuC;IACvC,MAAMQ,QAA2B;QAACR,eAAe,CAAC,EAAE;QAAEM;KAAY;IAClE,IAAI,KAAKN,iBAAiB;QACxBQ,KAAK,CAAC,EAAE,GAAGR,eAAe,CAAC,EAAE;IAC/B;IACA,IAAI,KAAKA,iBAAiB;QACxBQ,KAAK,CAAC,EAAE,GAAGR,eAAe,CAAC,EAAE;IAC/B;IACA,IAAI,KAAKA,iBAAiB;QACxBQ,KAAK,CAAC,EAAE,GAAGR,eAAe,CAAC,EAAE;IAC/B;IACA,OAAOQ;AACT"}