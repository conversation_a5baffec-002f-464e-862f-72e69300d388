{"version": 3, "sources": ["../../../src/lib/metadata/is-metadata-route.ts"], "sourcesContent": ["import type { PageExtensions } from '../../build/page-extensions-type'\nimport { normalizePathSep } from '../../shared/lib/page-path/normalize-path-sep'\n\nexport const STATIC_METADATA_IMAGES = {\n  icon: {\n    filename: 'icon',\n    extensions: ['ico', 'jpg', 'jpeg', 'png', 'svg'],\n  },\n  apple: {\n    filename: 'apple-icon',\n    extensions: ['jpg', 'jpeg', 'png'],\n  },\n  favicon: {\n    filename: 'favicon',\n    extensions: ['ico'],\n  },\n  openGraph: {\n    filename: 'opengraph-image',\n    extensions: ['jpg', 'jpeg', 'png', 'gif'],\n  },\n  twitter: {\n    filename: 'twitter-image',\n    extensions: ['jpg', 'jpeg', 'png', 'gif'],\n  },\n} as const\n\n// Match routes that are metadata routes, e.g. /sitemap.xml, /favicon.<ext>, /<icon>.<ext>, etc.\n// TODO-METADATA: support more metadata routes with more extensions\nconst defaultExtensions = ['js', 'jsx', 'ts', 'tsx']\n\n// Match the file extension with the dynamic multi-routes extensions\n// e.g. ([xml, js], null) -> can match `/sitemap.xml/route`, `sitemap.js/route`\n// e.g. ([png], [ts]) -> can match `/opengrapg-image.png/route`, `/opengraph-image.ts[]/route`\nexport const getExtensionRegexString = (\n  staticExtensions: readonly string[],\n  dynamicExtensions: readonly string[] | null\n) => {\n  // If there's no possible multi dynamic routes, will not match any <name>[].<ext> files\n  if (!dynamicExtensions) {\n    return `\\\\.(?:${staticExtensions.join('|')})`\n  }\n  return `(?:\\\\.(${staticExtensions.join('|')})|((\\\\[\\\\])?\\\\.(${dynamicExtensions.join('|')})))`\n}\n\n// When you only pass the file extension as `[]`, it will only match the static convention files\n// e.g. /robots.txt, /sitemap.xml, /favicon.ico, /manifest.json\n// When you pass the file extension as `['js', 'jsx', 'ts', 'tsx']`, it will also match the dynamic convention files\n// e.g. /robots.js, /sitemap.tsx, /favicon.jsx, /manifest.ts\n// When `withExtension` is false, it will match the static convention files without the extension, by default it's true\n// e.g. /robots, /sitemap, /favicon, /manifest, use to match dynamic API routes like app/robots.ts\nexport function isMetadataRouteFile(\n  appDirRelativePath: string,\n  pageExtensions: PageExtensions,\n  withExtension: boolean\n) {\n  const metadataRouteFilesRegex = [\n    new RegExp(\n      `^[\\\\\\\\/]robots${\n        withExtension\n          ? `${getExtensionRegexString(pageExtensions.concat('txt'), null)}$`\n          : ''\n      }`\n    ),\n    new RegExp(\n      `^[\\\\\\\\/]manifest${\n        withExtension\n          ? `${getExtensionRegexString(\n              pageExtensions.concat('webmanifest', 'json'),\n              null\n            )}$`\n          : ''\n      }`\n    ),\n    new RegExp(`^[\\\\\\\\/]favicon\\\\.ico$`),\n    new RegExp(\n      `[\\\\\\\\/]sitemap${\n        withExtension\n          ? `${getExtensionRegexString(['xml'], pageExtensions)}$`\n          : ''\n      }`\n    ),\n    new RegExp(\n      `[\\\\\\\\/]${STATIC_METADATA_IMAGES.icon.filename}\\\\d?${\n        withExtension\n          ? `${getExtensionRegexString(\n              STATIC_METADATA_IMAGES.icon.extensions,\n              pageExtensions\n            )}$`\n          : ''\n      }`\n    ),\n    new RegExp(\n      `[\\\\\\\\/]${STATIC_METADATA_IMAGES.apple.filename}\\\\d?${\n        withExtension\n          ? `${getExtensionRegexString(\n              STATIC_METADATA_IMAGES.apple.extensions,\n              pageExtensions\n            )}$`\n          : ''\n      }`\n    ),\n    new RegExp(\n      `[\\\\\\\\/]${STATIC_METADATA_IMAGES.openGraph.filename}\\\\d?${\n        withExtension\n          ? `${getExtensionRegexString(\n              STATIC_METADATA_IMAGES.openGraph.extensions,\n              pageExtensions\n            )}$`\n          : ''\n      }`\n    ),\n    new RegExp(\n      `[\\\\\\\\/]${STATIC_METADATA_IMAGES.twitter.filename}\\\\d?${\n        withExtension\n          ? `${getExtensionRegexString(\n              STATIC_METADATA_IMAGES.twitter.extensions,\n              pageExtensions\n            )}$`\n          : ''\n      }`\n    ),\n  ]\n\n  const normalizedAppDirRelativePath = normalizePathSep(appDirRelativePath)\n  return metadataRouteFilesRegex.some((r) =>\n    r.test(normalizedAppDirRelativePath)\n  )\n}\n\nexport function isStaticMetadataRouteFile(appDirRelativePath: string) {\n  return isMetadataRouteFile(appDirRelativePath, [], true)\n}\n\nexport function isStaticMetadataRoute(page: string) {\n  return (\n    page === '/robots' ||\n    page === '/manifest' ||\n    isStaticMetadataRouteFile(page)\n  )\n}\n\n/*\n * Remove the 'app' prefix or '/route' suffix, only check the route name since they're only allowed in root app directory\n * e.g.\n * /app/robots -> /robots\n * app/robots -> /robots\n * /robots -> /robots\n */\nexport function isMetadataRoute(route: string): boolean {\n  let page = route.replace(/^\\/?app\\//, '').replace(/\\/route$/, '')\n  if (page[0] !== '/') page = '/' + page\n\n  return (\n    !page.endsWith('/page') &&\n    isMetadataRouteFile(page, defaultExtensions, false)\n  )\n}\n"], "names": ["normalizePathSep", "STATIC_METADATA_IMAGES", "icon", "filename", "extensions", "apple", "favicon", "openGraph", "twitter", "defaultExtensions", "getExtensionRegexString", "staticExtensions", "dynamicExtensions", "join", "isMetadataRouteFile", "appDirRelativePath", "pageExtensions", "withExtension", "metadataRouteFilesRegex", "RegExp", "concat", "normalizedAppDirRelativePath", "some", "r", "test", "isStaticMetadataRouteFile", "isStaticMetadataRoute", "page", "isMetadataRoute", "route", "replace", "endsWith"], "mappings": "AACA,SAASA,gBAAgB,QAAQ,gDAA+C;AAEhF,OAAO,MAAMC,yBAAyB;IACpCC,MAAM;QACJC,UAAU;QACVC,YAAY;YAAC;YAAO;YAAO;YAAQ;YAAO;SAAM;IAClD;IACAC,OAAO;QACLF,UAAU;QACVC,YAAY;YAAC;YAAO;YAAQ;SAAM;IACpC;IACAE,SAAS;QACPH,UAAU;QACVC,YAAY;YAAC;SAAM;IACrB;IACAG,WAAW;QACTJ,UAAU;QACVC,YAAY;YAAC;YAAO;YAAQ;YAAO;SAAM;IAC3C;IACAI,SAAS;QACPL,UAAU;QACVC,YAAY;YAAC;YAAO;YAAQ;YAAO;SAAM;IAC3C;AACF,EAAU;AAEV,gGAAgG;AAChG,mEAAmE;AACnE,MAAMK,oBAAoB;IAAC;IAAM;IAAO;IAAM;CAAM;AAEpD,oEAAoE;AACpE,+EAA+E;AAC/E,8FAA8F;AAC9F,OAAO,MAAMC,0BAA0B,CACrCC,kBACAC;IAEA,uFAAuF;IACvF,IAAI,CAACA,mBAAmB;QACtB,OAAO,CAAC,MAAM,EAAED,iBAAiBE,IAAI,CAAC,KAAK,CAAC,CAAC;IAC/C;IACA,OAAO,CAAC,OAAO,EAAEF,iBAAiBE,IAAI,CAAC,KAAK,gBAAgB,EAAED,kBAAkBC,IAAI,CAAC,KAAK,GAAG,CAAC;AAChG,EAAC;AAED,gGAAgG;AAChG,+DAA+D;AAC/D,oHAAoH;AACpH,4DAA4D;AAC5D,uHAAuH;AACvH,kGAAkG;AAClG,OAAO,SAASC,oBACdC,kBAA0B,EAC1BC,cAA8B,EAC9BC,aAAsB;IAEtB,MAAMC,0BAA0B;QAC9B,IAAIC,OACF,CAAC,cAAc,EACbF,gBACI,GAAGP,wBAAwBM,eAAeI,MAAM,CAAC,QAAQ,MAAM,CAAC,CAAC,GACjE,IACJ;QAEJ,IAAID,OACF,CAAC,gBAAgB,EACfF,gBACI,GAAGP,wBACDM,eAAeI,MAAM,CAAC,eAAe,SACrC,MACA,CAAC,CAAC,GACJ,IACJ;QAEJ,IAAID,OAAO,CAAC,sBAAsB,CAAC;QACnC,IAAIA,OACF,CAAC,cAAc,EACbF,gBACI,GAAGP,wBAAwB;YAAC;SAAM,EAAEM,gBAAgB,CAAC,CAAC,GACtD,IACJ;QAEJ,IAAIG,OACF,CAAC,OAAO,EAAElB,uBAAuBC,IAAI,CAACC,QAAQ,CAAC,IAAI,EACjDc,gBACI,GAAGP,wBACDT,uBAAuBC,IAAI,CAACE,UAAU,EACtCY,gBACA,CAAC,CAAC,GACJ,IACJ;QAEJ,IAAIG,OACF,CAAC,OAAO,EAAElB,uBAAuBI,KAAK,CAACF,QAAQ,CAAC,IAAI,EAClDc,gBACI,GAAGP,wBACDT,uBAAuBI,KAAK,CAACD,UAAU,EACvCY,gBACA,CAAC,CAAC,GACJ,IACJ;QAEJ,IAAIG,OACF,CAAC,OAAO,EAAElB,uBAAuBM,SAAS,CAACJ,QAAQ,CAAC,IAAI,EACtDc,gBACI,GAAGP,wBACDT,uBAAuBM,SAAS,CAACH,UAAU,EAC3CY,gBACA,CAAC,CAAC,GACJ,IACJ;QAEJ,IAAIG,OACF,CAAC,OAAO,EAAElB,uBAAuBO,OAAO,CAACL,QAAQ,CAAC,IAAI,EACpDc,gBACI,GAAGP,wBACDT,uBAAuBO,OAAO,CAACJ,UAAU,EACzCY,gBACA,CAAC,CAAC,GACJ,IACJ;KAEL;IAED,MAAMK,+BAA+BrB,iBAAiBe;IACtD,OAAOG,wBAAwBI,IAAI,CAAC,CAACC,IACnCA,EAAEC,IAAI,CAACH;AAEX;AAEA,OAAO,SAASI,0BAA0BV,kBAA0B;IAClE,OAAOD,oBAAoBC,oBAAoB,EAAE,EAAE;AACrD;AAEA,OAAO,SAASW,sBAAsBC,IAAY;IAChD,OACEA,SAAS,aACTA,SAAS,eACTF,0BAA0BE;AAE9B;AAEA;;;;;;CAMC,GACD,OAAO,SAASC,gBAAgBC,KAAa;IAC3C,IAAIF,OAAOE,MAAMC,OAAO,CAAC,aAAa,IAAIA,OAAO,CAAC,YAAY;IAC9D,IAAIH,IAAI,CAAC,EAAE,KAAK,KAAKA,OAAO,MAAMA;IAElC,OACE,CAACA,KAAKI,QAAQ,CAAC,YACfjB,oBAAoBa,MAAMlB,mBAAmB;AAEjD"}