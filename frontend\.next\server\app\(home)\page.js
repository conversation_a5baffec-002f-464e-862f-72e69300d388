(()=>{var e={};e.id=813,e.ids=[813],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8710:(e,t,r)=>{"use strict";r.d(t,{f:()=>a,u:()=>s});var[s,a]=(0,r(40572).q)({name:"CardContext",strict:!0,errorMessage:"useCardContext: `context` is undefined. Seems you forgot to wrap component within <Card />"})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10974:(e,t,r)=>{"use strict";async function s(e,t,r="GET",a){let l={"Content-Type":"application/json"};a&&(l.Authorization=`Bearer ${a}`);let o={method:r,headers:l,next:{revalidate:60}};t&&"GET"!==r&&(o.body=JSON.stringify(t));try{let t=`http://localhost:8000${e}`;console.log(`Fetching: ${t}`);let r=await fetch(t,o);return r.ok||console.warn(`API request failed: ${t} returned status ${r.status}`),r}catch(e){throw console.error("API request failed:",e),e}}r.d(t,{G:()=>s})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20187:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var s=r(65239),a=r(48088),l=r(88170),o=r.n(l),n=r(30893),i={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>n[e]);r.d(t,i);let d={children:["",{children:["(home)",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,87163)),"G:\\Graduation project 2025\\app (2)\\app\\frontend\\src\\app\\(home)\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,89282)),"G:\\Graduation project 2025\\app (2)\\app\\frontend\\src\\app\\(home)\\layout.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"G:\\Graduation project 2025\\app (2)\\app\\frontend\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,54431)),"G:\\Graduation project 2025\\app (2)\\app\\frontend\\src\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"G:\\Graduation project 2025\\app (2)\\app\\frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["G:\\Graduation project 2025\\app (2)\\app\\frontend\\src\\app\\(home)\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/(home)/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},23650:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\CallUs\\\\index.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\Graduation project 2025\\app (2)\\app\\frontend\\src\\components\\specific\\CallUs\\index.tsx","default")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},47286:(e,t,r)=>{"use strict";r.d(t,{AlertSection:()=>$});var s=r(60687),a=r(4780),l=r(88920),o=r(11468),n=r(43210);let i=(0,n.createContext)(void 0),d=({children:e})=>{let[t,r]=(0,n.useState)(!1);return(0,s.jsx)(i.Provider,{value:{open:t,setOpen:r},children:e})},c=()=>{let e=(0,n.useContext)(i);if(!e)throw Error("useModal must be used within a ModalProvider");return e};function u({children:e}){return(0,s.jsx)(d,{children:e})}let p=({children:e,className:t})=>{let{setOpen:r}=c();return(0,s.jsx)("button",{className:(0,a.cn)("px-4 py-2 rounded-md text-black dark:text-white text-center relative overflow-hidden",t),onClick:()=>r(!0),children:e})},m=({children:e,className:t})=>{let{open:r}=c();(0,n.useEffect)(()=>{r?document.body.style.overflow="hidden":document.body.style.overflow="auto"},[r]);let i=(0,n.useRef)(null),{setOpen:d}=c();return f(i,()=>d(!1)),(0,s.jsx)(l.N,{children:r&&(0,s.jsxs)(o.P.div,{initial:{opacity:0},animate:{opacity:1,backdropFilter:"blur(10px)"},exit:{opacity:0,backdropFilter:"blur(0px)"},className:"fixed [perspective:800px] [transform-style:preserve-3d] inset-0 h-full w-full  flex items-center justify-center z-[99999]",children:[(0,s.jsx)(h,{}),(0,s.jsxs)(o.P.div,{ref:i,className:(0,a.cn)("min-h-[50%] max-h-[90%] md:max-w-[40%] bg-white dark:bg-neutral-950 border border-transparent dark:border-neutral-800 md:rounded-2xl relative z-50 flex flex-col flex-1 overflow-hidden",t),initial:{opacity:0,scale:.5,rotateX:40,y:40},animate:{opacity:1,scale:1,rotateX:0,y:0},exit:{opacity:0,scale:.8,rotateX:10},transition:{type:"spring",stiffness:260,damping:15},children:[(0,s.jsx)(x,{}),e]})]})})},h=({className:e})=>(0,s.jsx)(o.P.div,{initial:{opacity:0},animate:{opacity:1,backdropFilter:"blur(10px)"},exit:{opacity:0,backdropFilter:"blur(0px)"},className:`fixed inset-0 h-full w-full bg-black bg-opacity-50 z-50 ${e}`}),x=()=>{let{setOpen:e}=c();return(0,s.jsx)("button",{onClick:()=>e(!1),className:"absolute top-4 right-4 group",children:(0,s.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"text-black dark:text-white h-4 w-4 group-hover:scale-125 group-hover:rotate-3 transition duration-200",children:[(0,s.jsx)("path",{stroke:"none",d:"M0 0h24v24H0z",fill:"none"}),(0,s.jsx)("path",{d:"M18 6l-12 12"}),(0,s.jsx)("path",{d:"M6 6l12 12"})]})})},f=(e,t)=>{(0,n.useEffect)(()=>{let r=r=>{!(!e.current||e.current.contains(r.target))&&t(r)};return document.addEventListener("mousedown",r),document.addEventListener("touchstart",r),()=>{document.removeEventListener("mousedown",r),document.removeEventListener("touchstart",r)}},[e,t])};var b=r(79293),g=r(63257),v=r(86760),w=r(62688);let y=(0,w.A)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]),j=(0,w.A)("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]]);var k=r(85814),N=r.n(k),P=r(55150),A=r(26109),E=(0,r(72926).tv)({slots:{wrapper:"relative shadow-black/5",zoomedWrapper:"relative overflow-hidden rounded-inherit",img:"relative z-10 opacity-0 shadow-black/5 data-[loaded=true]:opacity-100",blurredImg:["absolute","z-0","inset-0","w-full","h-full","object-cover","filter","blur-lg","scale-105","saturate-150","opacity-30","translate-y-1"]},variants:{radius:{none:{},sm:{},md:{},lg:{},full:{}},shadow:{none:{wrapper:"shadow-none",img:"shadow-none"},sm:{wrapper:"shadow-small",img:"shadow-small"},md:{wrapper:"shadow-medium",img:"shadow-medium"},lg:{wrapper:"shadow-large",img:"shadow-large"}},isZoomed:{true:{img:["object-cover","transform","hover:scale-125"]}},showSkeleton:{true:{wrapper:["group","relative","overflow-hidden","bg-content3 dark:bg-content2"],img:"opacity-0"}},disableAnimation:{true:{img:"transition-none"},false:{img:"transition-transform-opacity motion-reduce:transition-none !duration-300"}}},defaultVariants:{radius:"lg",shadow:"none",isZoomed:!1,isBlurred:!1,showSkeleton:!1},compoundVariants:[{showSkeleton:!0,disableAnimation:!1,class:{wrapper:["before:opacity-100","before:absolute","before:inset-0","before:-translate-x-full","before:animate-[shimmer_2s_infinite]","before:border-t","before:border-content4/30","before:bg-gradient-to-r","before:from-transparent","before:via-content4","dark:before:via-default-700/10","before:to-transparent","after:opacity-100","after:absolute","after:inset-0","after:-z-10","after:bg-content3","dark:after:bg-content2"]}}],compoundSlots:[{slots:["wrapper","img","blurredImg","zoomedWrapper"],radius:"none",class:"rounded-none"},{slots:["wrapper","img","blurredImg","zoomedWrapper"],radius:"full",class:"rounded-full"},{slots:["wrapper","img","blurredImg","zoomedWrapper"],radius:"sm",class:"rounded-small"},{slots:["wrapper","img","blurredImg","zoomedWrapper"],radius:"md",class:"rounded-md"},{slots:["wrapper","img","blurredImg","zoomedWrapper"],radius:"lg",class:"rounded-large"}]}),C=r(54514),_=r(82432),z=r(16060),S=r(1172),G=r(78607),I=(0,A.Rf)((e,t)=>{let{Component:r,domRef:a,slots:l,classNames:o,isBlurred:i,isZoomed:d,fallbackSrc:c,removeWrapper:u,disableSkeleton:p,getImgProps:m,getWrapperProps:h,getBlurredImgProps:x}=function(e){var t,r;let s=(0,P.o)(),[a,l]=(0,A.rE)(e,E.variantKeys),{ref:o,as:i,src:d,className:c,classNames:u,loading:p,isBlurred:m,fallbackSrc:h,isLoading:x,disableSkeleton:f=!!h,removeWrapper:b=!1,onError:g,onLoad:v,srcSet:w,sizes:y,crossOrigin:j,...k}=a,N=function(e={}){let{onLoad:t,onError:r,ignoreFallback:s}=e,a=n.useSyncExternalStore(()=>()=>{},()=>!0,()=>!1),l=(0,n.useRef)(a?new Image:null),[o,i]=(0,n.useState)("pending");(0,n.useEffect)(()=>{l.current&&(l.current.onload=e=>{d(),i("loaded"),null==t||t(e)},l.current.onerror=e=>{d(),i("failed"),null==r||r(e)})},[l.current]);let d=()=>{l.current&&(l.current.onload=null,l.current.onerror=null,l.current=null)};return(0,G.U)(()=>{a&&i(function(e,t){let{loading:r,src:s,srcSet:a,crossOrigin:l,sizes:o,ignoreFallback:n}=e;if(!s)return"pending";if(n)return"loaded";let i=new Image;return(i.src=s,l&&(i.crossOrigin=l),a&&(i.srcset=a),o&&(i.sizes=o),r&&(i.loading=r),t.current=i,i.complete&&i.naturalWidth)?"loaded":"loading"}(e,l))},[a]),s?"loaded":o}({src:d,loading:p,onError:g,onLoad:v,ignoreFallback:!1,srcSet:w,sizes:y,crossOrigin:j}),I=null!=(r=null!=(t=e.disableAnimation)?t:null==s?void 0:s.disableAnimation)&&r,M="loaded"===N&&!x,$="loading"===N||x,W=e.isZoomed,q=(0,C.zD)(o),{w:D,h:F}=(0,n.useMemo)(()=>({w:a.width?"number"==typeof a.width?`${a.width}px`:a.width:"fit-content",h:a.height?"number"==typeof a.height?`${a.height}px`:a.height:"auto"}),[null==a?void 0:a.width,null==a?void 0:a.height]),R=(!d||!M)&&!!h,L=$&&!f,B=(0,n.useMemo)(()=>E({...l,disableAnimation:I,showSkeleton:L}),[(0,_.t6)(l),I,L]),H=(0,z.$)(c,null==u?void 0:u.img),U=(0,n.useCallback)(()=>{let e=R?{backgroundImage:`url(${h})`}:{};return{className:B.wrapper({class:null==u?void 0:u.wrapper}),style:{...e,maxWidth:D}}},[B,R,h,null==u?void 0:u.wrapper,D]),O=(0,n.useCallback)(()=>({src:d,"aria-hidden":(0,S.sE)(!0),className:B.blurredImg({class:null==u?void 0:u.blurredImg})}),[B,d,null==u?void 0:u.blurredImg]);return{Component:i||"img",domRef:q,slots:B,classNames:u,isBlurred:m,disableSkeleton:f,fallbackSrc:h,removeWrapper:b,isZoomed:W,isLoading:$,getImgProps:(e={})=>{let t=(0,z.$)(H,null==e?void 0:e.className);return{src:d,ref:q,"data-loaded":(0,S.sE)(M),className:B.img({class:t}),loading:p,srcSet:w,sizes:y,crossOrigin:j,...k,style:{...(null==k?void 0:k.height)&&{height:F},...e.style,...k.style}}},getWrapperProps:U,getBlurredImgProps:O}}({...e,ref:t}),f=(0,s.jsx)(r,{ref:a,...m()});if(u)return f;let b=(0,s.jsx)("div",{className:l.zoomedWrapper({class:null==o?void 0:o.zoomedWrapper}),children:f});return i?(0,s.jsxs)("div",{...h(),children:[d?b:f,(0,n.cloneElement)(f,x())]}):d||!p||c?(0,s.jsxs)("div",{...h(),children:[" ",d?b:f]}):f});function M({id:e}){let[t,r]=(0,n.useState)(!0),[a,l]=(0,n.useState)(null);return(0,s.jsx)("div",{className:"container mx-auto px-4 py-6 space-y-6",dir:"rtl",children:t?(0,s.jsx)("p",{children:"جارى التحميل..."}):a?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("h1",{className:"text-xl font-bold text-right mt-4",children:a.location}),(0,s.jsx)("p",{className:"text-sm text-gray-600 leading-relaxed text-right",children:a.description}),(0,s.jsxs)("div",{className:"text-right",children:[(0,s.jsx)("h2",{className:"text-sm font-semibold mb-3",children:"الصور المرفقة"}),(0,s.jsx)("div",{className:"flex flex-row-reverse flex-wrap gap-4 overflow-x-auto pb-4",dir:"ltr",children:a.images.map(({id:e,src:t})=>(0,s.jsx)(I,{src:t||"/placeholder.svg?height=240&width=240",alt:`صورة ${e+1}`,width:240,height:240,className:"object-cover rounded-lg"},e))})]})]}):(0,s.jsx)("p",{children:"حدث خطأ أثناء تحميل الطلب برجاء اعادة المحاولة لاحقا"})})}function $({data:e=[],heading:t}){let r=Array.isArray(e)?e:[];return(0,s.jsxs)("div",{className:"w-full max-w-[1200px] mx-auto px-4 py-6",children:[(0,s.jsx)("div",{className:"flex items-center justify-between mb-4",children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(N(),{href:"/previous",className:"text-blue-500 hover:text-blue-600",children:(0,s.jsx)(y,{className:"w-5 h-5"})}),(0,s.jsx)("h2",{className:"text-xl font-bold",children:t})]})}),0===r.length?(0,s.jsx)("div",{className:"flex items-center justify-center w-full h-32",children:(0,s.jsx)("p",{className:"text-gray-500",children:"لا توجد تنبيهات حالياً"})}):(0,s.jsx)(b.H,{orientation:"horizontal",className:"flex gap-4 w-full overflow-x-auto pb-4",children:r.map(e=>(0,s.jsx)(g.Z,{className:"flex-none w-[300px] border border-gray-200",children:(0,s.jsxs)(v.U,{className:"gap-4",children:[(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsxs)("div",{className:"space-y-1",children:[(0,s.jsx)("h3",{className:"font-semibold text-sm text-start",children:e.user_first_name+" "+e.user_last_name}),(0,s.jsx)("p",{className:"text-xs text-gray-500",children:e.location})]}),(0,s.jsx)(j,{className:"w-5 h-5 text-blue-500 mt-1 flex-shrink-0"})]}),(0,s.jsx)("div",{className:"flex justify-end gap-2 border-t-1 pt-4",children:(0,s.jsxs)(u,{children:[(0,s.jsx)(p,{className:"bg-blue-600 text-sm text-white hover:opacity-75 transition",children:"عرض التفاصيل"}),(0,s.jsx)(m,{children:(0,s.jsx)(M,{id:e.id})})]},`modal-${e.id}`)})]})},e.id))})]})}I.displayName="NextUI.Image"},58694:(e,t,r)=>{Promise.resolve().then(r.bind(r,10529)),Promise.resolve().then(r.t.bind(r,85814,23)),Promise.resolve().then(r.bind(r,47286)),Promise.resolve().then(r.bind(r,73271))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63257:(e,t,r)=>{"use strict";r.d(t,{Z:()=>N});var s=r(8710),a=r(72926),l=r(65146),o=(0,a.tv)({slots:{base:["flex","flex-col","relative","overflow-hidden","h-auto","outline-none","text-foreground","box-border","bg-content1",...l.zb],header:["flex","p-3","z-10","w-full","justify-start","items-center","shrink-0","overflow-inherit","color-inherit","subpixel-antialiased"],body:["relative","flex","flex-1","w-full","p-3","flex-auto","flex-col","place-content-inherit","align-items-inherit","h-auto","break-words","text-left","overflow-y-auto","subpixel-antialiased"],footer:["p-3","h-auto","flex","w-full","items-center","overflow-hidden","color-inherit","subpixel-antialiased"]},variants:{shadow:{none:{base:"shadow-none"},sm:{base:"shadow-small"},md:{base:"shadow-medium"},lg:{base:"shadow-large"}},radius:{none:{base:"rounded-none",header:"rounded-none",footer:"rounded-none"},sm:{base:"rounded-small",header:"rounded-t-small",footer:"rounded-b-small"},md:{base:"rounded-medium",header:"rounded-t-medium",footer:"rounded-b-medium"},lg:{base:"rounded-large",header:"rounded-t-large",footer:"rounded-b-large"}},fullWidth:{true:{base:"w-full"}},isHoverable:{true:{base:"data-[hover=true]:bg-content2 dark:data-[hover=true]:bg-content2"}},isPressable:{true:{base:"cursor-pointer"}},isBlurred:{true:{base:["bg-background/80","dark:bg-background/20","backdrop-blur-md","backdrop-saturate-150"]}},isFooterBlurred:{true:{footer:["bg-background/10","backdrop-blur","backdrop-saturate-150"]}},isDisabled:{true:{base:"opacity-disabled cursor-not-allowed"}},disableAnimation:{true:"",false:{base:"transition-transform-background motion-reduce:transition-none"}}},compoundVariants:[{isPressable:!0,class:"data-[pressed=true]:scale-[0.97] tap-highlight-transparent"}],defaultVariants:{radius:"lg",shadow:"md",fullWidth:!1,isHoverable:!1,isPressable:!1,isDisabled:!1,isFooterBlurred:!1}}),n=r(43210),i=r(72406),d=r(25381),c=r(6409),u=r(40182),p=r(39217),m=r(55150),h=r(26109),x=r(16060),f=r(82432),b=r(1172),g=r(73094),v=r(54514),w=r(86925),y=r(81730),j=r(60687),k=(0,h.Rf)((e,t)=>{let{children:r,context:a,Component:l,isPressable:k,disableAnimation:N,disableRipple:P,getCardProps:A,getRippleProps:E}=function(e){var t,r,s,a;let l=(0,m.o)(),[y,j]=(0,h.rE)(e,o.variantKeys),{ref:k,as:N,children:P,onClick:A,onPress:E,autoFocus:C,className:_,classNames:z,allowTextSelectionOnPress:S=!0,...G}=y,I=(0,v.zD)(k),M=N||(e.isPressable?"button":"div"),$="string"==typeof M,W=null!=(r=null!=(t=e.disableAnimation)?t:null==l?void 0:l.disableAnimation)&&r,q=null!=(a=null!=(s=e.disableRipple)?s:null==l?void 0:l.disableRipple)&&a,D=(0,x.$)(null==z?void 0:z.base,_),{onClear:F,onPress:R,ripples:L}=(0,w.k)(),B=(0,n.useCallback)(e=>{q||W||!I.current||R(e)},[q,W,I,R]),{buttonProps:H,isPressed:U}=(0,p.l)({onPress:(0,i.c)(E,B),elementType:N,isDisabled:!e.isPressable,onClick:A,allowTextSelectionOnPress:S,...G},I),{hoverProps:O,isHovered:Z}=(0,u.M)({isDisabled:!e.isHoverable,...G}),{isFocusVisible:T,isFocused:V,focusProps:X}=(0,c.o)({autoFocus:C}),J=(0,n.useMemo)(()=>o({...j,disableAnimation:W}),[(0,f.t6)(j),W]),K=(0,n.useMemo)(()=>({slots:J,classNames:z,disableAnimation:W,isDisabled:e.isDisabled,isFooterBlurred:e.isFooterBlurred,fullWidth:e.fullWidth}),[J,z,e.isDisabled,e.isFooterBlurred,W,e.fullWidth]),Q=(0,n.useCallback)((t={})=>({ref:I,className:J.base({class:D}),tabIndex:e.isPressable?0:-1,"data-hover":(0,b.sE)(Z),"data-pressed":(0,b.sE)(U),"data-focus":(0,b.sE)(V),"data-focus-visible":(0,b.sE)(T),"data-disabled":(0,b.sE)(e.isDisabled),...(0,d.v)(e.isPressable?{...H,...X,role:"button"}:{},e.isHoverable?O:{},(0,g.$)(G,{enabled:$}),(0,g.$)(t))}),[I,J,D,$,e.isPressable,e.isHoverable,e.isDisabled,Z,U,T,H,X,O,G]),Y=(0,n.useCallback)(()=>({ripples:L,onClear:F}),[L,F]);return{context:K,domRef:I,Component:M,classNames:z,children:P,isHovered:Z,isPressed:U,disableAnimation:W,isPressable:e.isPressable,isHoverable:e.isHoverable,disableRipple:q,handlePress:B,isFocusVisible:T,getCardProps:Q,getRippleProps:Y}}({...e,ref:t});return(0,j.jsxs)(l,{...A(),children:[(0,j.jsx)(s.u,{value:a,children:r}),k&&!N&&!P&&(0,j.jsx)(y.j,{...E()})]})});k.displayName="NextUI.Card";var N=k},66942:(e,t,r)=>{Promise.resolve().then(r.bind(r,11075)),Promise.resolve().then(r.t.bind(r,4536,23)),Promise.resolve().then(r.bind(r,72873)),Promise.resolve().then(r.bind(r,23650))},72873:(e,t,r)=>{"use strict";r.d(t,{AlertSection:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call AlertSection() from the server but AlertSection is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\Graduation project 2025\\app (2)\\app\\frontend\\src\\components\\specific\\AlertSection\\index.tsx","AlertSection")},73271:(e,t,r)=>{"use strict";r.d(t,{default:()=>c});var s=r(60687),a=r(63257),l=r(86760),o=r(62688);let n=(0,o.A)("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]]),i=(0,o.A)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]]);var d=r(97992);function c({}){return(0,s.jsx)("section",{className:"py-12 bg-white",id:"call-us",children:(0,s.jsxs)("div",{className:"container mx-auto px-4",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold text-center mb-8",children:"اتصل بنا"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,s.jsx)(a.Z,{children:(0,s.jsxs)(l.U,{className:"flex flex-col items-center text-center gap-4",children:[(0,s.jsx)(n,{className:"w-6 h-6 text-blue-500"}),(0,s.jsx)("h3",{className:"font-bold",children:"الهاتف"}),(0,s.jsxs)("div",{className:"space-y-1 text-sm text-default-500",children:[(0,s.jsx)("p",{dir:"ltr",children:"+970 2384501 / +970 2384501"}),(0,s.jsx)("p",{dir:"ltr",children:"+9702384501 / +970 2384501"})]})]})}),(0,s.jsx)(a.Z,{children:(0,s.jsxs)(l.U,{className:"flex flex-col items-center text-center gap-4",children:[(0,s.jsx)(i,{className:"w-6 h-6 text-blue-500"}),(0,s.jsx)("h3",{className:"font-bold",children:"البريد الكتروني"}),(0,s.jsxs)("div",{className:"space-y-1 text-sm text-default-500",children:[(0,s.jsx)("p",{children:"AssistanceFormat.Com.Eg"}),(0,s.jsx)("p",{children:"AssistanceFormat.Com.Eg"})]})]})}),(0,s.jsx)(a.Z,{children:(0,s.jsxs)(l.U,{className:"flex flex-col items-center text-center gap-4",children:[(0,s.jsx)(d.A,{className:"w-6 h-6 text-blue-500"}),(0,s.jsx)("h3",{className:"font-bold",children:"العنوان الرئيسي"}),(0,s.jsxs)("p",{className:"text-sm text-default-500",children:["القدس - شارع مدينة",(0,s.jsx)("br",{}),"العربية - فلسطين"]})]})})]})]})})}},79551:e=>{"use strict";e.exports=require("url")},86760:(e,t,r)=>{"use strict";r.d(t,{U:()=>d});var s=r(8710),a=r(26109),l=r(54514),o=r(16060),n=r(60687),i=(0,a.Rf)((e,t)=>{var r;let{as:a,className:i,children:d,...c}=e,u=(0,l.zD)(t),{slots:p,classNames:m}=(0,s.f)(),h=(0,o.$)(null==m?void 0:m.body,i);return(0,n.jsx)(a||"div",{ref:u,className:null==(r=p.body)?void 0:r.call(p,{class:h}),...c,children:d})});i.displayName="NextUI.CardBody";var d=i},87163:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>g});var s=r(37413),a=r(72873),l=r(23650),o=r(10974),n=r(11075),i=r(61120);let d=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),c=(...e)=>e.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim();var u={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let p=(0,i.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:r=2,absoluteStrokeWidth:s,className:a="",children:l,iconNode:o,...n},d)=>(0,i.createElement)("svg",{ref:d,...u,width:t,height:t,stroke:e,strokeWidth:s?24*Number(r)/Number(t):r,className:c("lucide",a),...n},[...o.map(([e,t])=>(0,i.createElement)(e,t)),...Array.isArray(l)?l:[l]])),m=(e,t)=>{let r=(0,i.forwardRef)(({className:r,...s},a)=>(0,i.createElement)(p,{ref:a,iconNode:t,className:c(`lucide-${d(e)}`,r),...s}));return r.displayName=`${e}`,r},h=m("Siren",[["path",{d:"M7 18v-6a5 5 0 1 1 10 0v6",key:"pcx96s"}],["path",{d:"M5 21a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-1a2 2 0 0 0-2-2H7a2 2 0 0 0-2 2z",key:"1b4s83"}],["path",{d:"M21 12h1",key:"jtio3y"}],["path",{d:"M18.5 4.5 18 5",key:"g5sp9y"}],["path",{d:"M2 12h1",key:"1uaihz"}],["path",{d:"M12 2v1",key:"11qlp1"}],["path",{d:"m4.929 4.929.707.707",key:"1i51kw"}],["path",{d:"M12 12v6",key:"3ahymv"}]]),x=m("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]);var f=r(4536),b=r.n(f);async function g(e){let t=[{id:"1",user_first_name:"أحمد",user_last_name:"السيد",location:"القدس - عين علي، شارع مدرسة الثورية - فلسطين",created_at:new Date().toISOString(),image:"/placeholder.svg?height=200&width=200"},{id:"2",user_first_name:"محمد",user_last_name:"إبراهيم",location:"رام الله - شارع الإرسال - فلسطين",created_at:new Date().toISOString(),image:"/placeholder.svg?height=200&width=200"},{id:"3",user_first_name:"سارة",user_last_name:"خالد",location:"بيت لحم - شارع المهد - فلسطين",created_at:new Date().toISOString(),image:"/placeholder.svg?height=200&width=200"}],r={results:[]},i={results:[]},d={results:[]};try{let e=async e=>{try{let t=await (0,o.G)(e,null,"GET");if(!t.ok)throw Error(`API request failed with status ${t.status}`);let r=await t.text();try{return r?JSON.parse(r):{results:[]}}catch(e){return console.error("Invalid JSON response:",r.substring(0,100)),{results:[]}}}catch(t){return console.error(`Error fetching ${e}:`,t),{results:[]}}},[t,s,a]=await Promise.all([e("/emergency/?emergency_type=O"),e("/emergency/?emergency_type=M"),e("/emergency/?emergency_type=D")]);r=t,i=s,d=a}catch(e){console.error("Failed to fetch emergency requests:",e),r={results:t},i={results:t},d={results:t}}return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"absolute w-full h-[75vh] top-0 pt-[35vh] bg-[url(/image_6.png)]",children:(0,s.jsxs)("div",{id:"send-emergency",className:"max-w-[30rem] drop-shadow-[#008524] bg-[#EEEEEE] mx-auto mb-24 py-8 px-6 rounded-xl flex flex-col items-center gap-3 relative bg-cover bg-center",children:[(0,s.jsx)(h,{className:"absolute top-3 left-3"}),(0,s.jsx)("h3",{children:"أرسل تنبيهًا للطوارئ"}),(0,s.jsx)("p",{children:"حدد إشعار الطوارئ ثم قم بملئ الحقول المطلوبة ومن ثم أرسل الطلب مباشرة وسيتم التوجة الى موقعكم في أسرع وقت ممكن."}),(0,s.jsx)(n.Button,{as:b(),href:"/add-application",endContent:(0,s.jsx)(x,{}),className:"mx-auto",color:"danger",children:"أرسل إشعار للطوارئ"})]})}),(0,s.jsxs)("section",{id:"recieved-emergency",className:"mt-[65vh]",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold text-center",children:"إشعارات الطوارئ المستلمة"}),(0,s.jsx)(a.AlertSection,{data:r.results,heading:"طلبات التنبيه حول الإغاثة"}),(0,s.jsx)(a.AlertSection,{data:i.results,heading:"طلبات التنبيه حول الصحة"}),(0,s.jsx)(a.AlertSection,{data:d.results,heading:"طلبات التنبيه حول الخطر"})]}),(0,s.jsx)(l.default,{})]})}}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,172,443,966,416,513,200],()=>r(20187));module.exports=s})();