(()=>{var e={};e.id=302,e.ids=[302],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12535:(e,t,a)=>{"use strict";a.d(t,{EmergencyForm:()=>i});let i=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call EmergencyForm() from the server but EmergencyForm is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\Graduation project 2025\\app (2)\\app\\frontend\\src\\components\\specific\\EmergencyForm\\index.tsx","EmergencyForm")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27406:(e,t)=>{"use strict";t.__esModule=!0,t.default=function(e,t){if(e&&t){var a=Array.isArray(t)?t:t.split(",");if(0===a.length)return!0;var i=e.name||"",n=(e.type||"").toLowerCase(),o=n.replace(/\/.*$/,"");return a.some(function(e){var t=e.trim().toLowerCase();return"."===t.charAt(0)?i.toLowerCase().endsWith(t):t.endsWith("/*")?o===t.replace(/\/.*$/,""):n===t})}return!0}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34452:e=>{"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},57607:(e,t,a)=>{"use strict";let i,n;a.d(t,{EmergencyForm:()=>ng});var o=a(60687),l=a(27605),r=a(63442),s=a(97992),c=a(43210),p=a.n(c),d=a(55150),u=a(26109),m=a(72926),f=a(65146),g=(0,m.tv)({slots:{base:["group inline-flex flex-col relative"],label:["block","absolute","z-10","origin-top-left","rtl:origin-top-right","subpixel-antialiased","text-small","text-foreground-500","pointer-events-none"],mainWrapper:"w-full flex flex-col",trigger:"relative px-3 gap-3 w-full inline-flex flex-row items-center shadow-sm outline-none tap-highlight-transparent",innerWrapper:"inline-flex h-full w-[calc(100%_-_theme(spacing.6))] min-h-4 items-center gap-1.5 box-border",selectorIcon:"absolute end-3 w-4 h-4",spinner:"absolute end-3",value:["text-foreground-500","font-normal","w-full","text-start"],listboxWrapper:"scroll-py-6 w-full",listbox:"",popoverContent:"w-full p-1 overflow-hidden",helperWrapper:"p-1 flex relative flex-col gap-1.5",description:"text-tiny text-foreground-400",errorMessage:"text-tiny text-danger"},variants:{variant:{flat:{trigger:["bg-default-100","data-[hover=true]:bg-default-200","group-data-[focus=true]:bg-default-200"]},faded:{trigger:["bg-default-100","border-medium","border-default-200","data-[hover=true]:border-default-400 data-[focus=true]:border-default-400 data-[open=true]:border-default-400"],value:"group-data-[has-value=true]:text-default-foreground"},bordered:{trigger:["border-medium","border-default-200","data-[hover=true]:border-default-400","data-[open=true]:border-default-foreground","data-[focus=true]:border-default-foreground"],value:"group-data-[has-value=true]:text-default-foreground"},underlined:{trigger:["!px-1","!pb-0","!gap-0","relative","box-border","border-b-medium","shadow-[0_1px_0px_0_rgba(0,0,0,0.05)]","border-default-200","!rounded-none","hover:border-default-300","after:content-['']","after:w-0","after:origin-center","after:bg-default-foreground","after:absolute","after:left-1/2","after:-translate-x-1/2","after:-bottom-[2px]","after:h-[2px]","data-[open=true]:after:w-full","data-[focus=true]:after:w-full"],value:"group-data-[has-value=true]:text-default-foreground"}},color:{default:{},primary:{selectorIcon:"text-primary"},secondary:{selectorIcon:"text-secondary"},success:{selectorIcon:"text-success"},warning:{selectorIcon:"text-warning"},danger:{selectorIcon:"text-danger"}},size:{sm:{label:"text-tiny",trigger:"h-8 min-h-8 px-2 rounded-small",value:"text-small"},md:{trigger:"h-10 min-h-10 rounded-medium",value:"text-small"},lg:{trigger:"h-12 min-h-12 rounded-large",value:"text-medium"}},radius:{none:{trigger:"rounded-none"},sm:{trigger:"rounded-small"},md:{trigger:"rounded-medium"},lg:{trigger:"rounded-large"},full:{trigger:"rounded-full"}},labelPlacement:{outside:{base:"flex flex-col"},"outside-left":{base:"flex-row items-center flex-nowrap items-start",label:"relative pe-2 text-foreground"},inside:{label:"text-tiny cursor-pointer",trigger:"flex-col items-start justify-center gap-0"}},fullWidth:{true:{base:"w-full"},false:{base:"min-w-40"}},isDisabled:{true:{base:"opacity-disabled pointer-events-none",trigger:"pointer-events-none"}},isInvalid:{true:{label:"!text-danger",value:"!text-danger",selectorIcon:"text-danger"}},isRequired:{true:{label:"after:content-['*'] after:text-danger after:ms-0.5"}},isMultiline:{true:{label:"relative",trigger:"!h-auto"},false:{value:"truncate"}},disableAnimation:{true:{trigger:"after:transition-none",base:"transition-none",label:"transition-none",selectorIcon:"transition-none"},false:{base:"transition-background motion-reduce:transition-none !duration-150",label:["will-change-auto","origin-top-left","rtl:origin-top-right","!duration-200","!ease-out","transition-[transform,color,left,opacity]","motion-reduce:transition-none"],selectorIcon:"transition-transform duration-150 ease motion-reduce:transition-none"}},disableSelectorIconRotation:{true:{},false:{selectorIcon:"data-[open=true]:rotate-180"}}},defaultVariants:{variant:"flat",color:"default",size:"md",labelPlacement:"inside",fullWidth:!0,isDisabled:!1,isMultiline:!1,disableSelectorIconRotation:!1},compoundVariants:[{variant:"flat",color:"default",class:{value:"group-data-[has-value=true]:text-default-foreground",trigger:["bg-default-100","data-[hover=true]:bg-default-200"]}},{variant:"flat",color:"primary",class:{trigger:["bg-primary-100","text-primary","data-[hover=true]:bg-primary-50","group-data-[focus=true]:bg-primary-50"],value:"text-primary",label:"text-primary"}},{variant:"flat",color:"secondary",class:{trigger:["bg-secondary-100","text-secondary","data-[hover=true]:bg-secondary-50","group-data-[focus=true]:bg-secondary-50"],value:"text-secondary",label:"text-secondary"}},{variant:"flat",color:"success",class:{trigger:["bg-success-100","text-success-600","dark:text-success","data-[hover=true]:bg-success-50","group-data-[focus=true]:bg-success-50"],value:"text-success-600 dark:text-success",label:"text-success-600 dark:text-success"}},{variant:"flat",color:"warning",class:{trigger:["bg-warning-100","text-warning-600","dark:text-warning","data-[hover=true]:bg-warning-50","group-data-[focus=true]:bg-warning-50"],value:"text-warning-600 dark:text-warning",label:"text-warning-600 dark:text-warning"}},{variant:"flat",color:"danger",class:{trigger:["bg-danger-100","text-danger","dark:text-danger-500","data-[hover=true]:bg-danger-50","group-data-[focus=true]:bg-danger-50"],value:"text-danger dark:text-danger-500",label:"text-danger dark:text-danger-500"}},{variant:"faded",color:"primary",class:{trigger:"data-[hover=true]:border-primary data-[focus=true]:border-primary data-[open=true]:border-primary",label:"text-primary"}},{variant:"faded",color:"secondary",class:{trigger:"data-[hover=true]:border-secondary data-[focus=true]:border-secondary data-[open=true]:border-secondary",label:"text-secondary"}},{variant:"faded",color:"success",class:{trigger:"data-[hover=true]:border-success data-[focus=true]:border-success data-[open=true]:border-success",label:"text-success"}},{variant:"faded",color:"warning",class:{trigger:"data-[hover=true]:border-warning data-[focus=true]:border-warning data-[open=true]:border-warning",label:"text-warning"}},{variant:"faded",color:"danger",class:{trigger:"data-[hover=true]:border-danger data-[focus=true]:border-danger data-[open=true]:border-danger",label:"text-danger"}},{variant:"underlined",color:"default",class:{value:"group-data-[has-value=true]:text-foreground"}},{variant:"underlined",color:"primary",class:{trigger:"after:bg-primary",label:"text-primary"}},{variant:"underlined",color:"secondary",class:{trigger:"after:bg-secondary",label:"text-secondary"}},{variant:"underlined",color:"success",class:{trigger:"after:bg-success",label:"text-success"}},{variant:"underlined",color:"warning",class:{trigger:"after:bg-warning",label:"text-warning"}},{variant:"underlined",color:"danger",class:{trigger:"after:bg-danger",label:"text-danger"}},{variant:"bordered",color:"primary",class:{trigger:["data-[open=true]:border-primary","data-[focus=true]:border-primary"],label:"text-primary"}},{variant:"bordered",color:"secondary",class:{trigger:["data-[open=true]:border-secondary","data-[focus=true]:border-secondary"],label:"text-secondary"}},{variant:"bordered",color:"success",class:{trigger:["data-[open=true]:border-success","data-[focus=true]:border-success"],label:"text-success"}},{variant:"bordered",color:"warning",class:{trigger:["data-[open=true]:border-warning","data-[focus=true]:border-warning"],label:"text-warning"}},{variant:"bordered",color:"danger",class:{trigger:["data-[open=true]:border-danger","data-[focus=true]:border-danger"],label:"text-danger"}},{labelPlacement:"inside",color:"default",class:{label:"group-data-[filled=true]:text-default-600"}},{labelPlacement:"outside",color:"default",class:{label:"group-data-[filled=true]:text-foreground"}},{radius:"full",size:["sm"],class:{trigger:"px-3"}},{radius:"full",size:"md",class:{trigger:"px-4"}},{radius:"full",size:"lg",class:{trigger:"px-5"}},{disableAnimation:!1,variant:["faded","bordered"],class:{trigger:"transition-colors motion-reduce:transition-none"}},{disableAnimation:!1,variant:"underlined",class:{trigger:"after:transition-width motion-reduce:after:transition-none"}},{variant:["flat","faded"],class:{trigger:[...f.zb]}},{isInvalid:!0,variant:"flat",class:{trigger:["bg-danger-50","data-[hover=true]:bg-danger-100","group-data-[focus=true]:bg-danger-50"]}},{isInvalid:!0,variant:"bordered",class:{trigger:"!border-danger group-data-[focus=true]:border-danger"}},{isInvalid:!0,variant:"underlined",class:{trigger:"after:bg-danger"}},{labelPlacement:"inside",size:"sm",class:{trigger:"h-12 min-h-12 py-1.5 px-3"}},{labelPlacement:"inside",size:"md",class:{trigger:"h-14 min-h-14 py-2"}},{labelPlacement:"inside",size:"lg",class:{label:"text-medium",trigger:"h-16 min-h-16 py-2.5 gap-0"}},{labelPlacement:"outside",isMultiline:!1,class:{base:"group relative justify-end",label:["pb-0","z-20","top-1/2","-translate-y-1/2","group-data-[filled=true]:start-0"]}},{labelPlacement:["inside"],class:{label:"group-data-[filled=true]:scale-85"}},{labelPlacement:"inside",size:["sm","md"],class:{label:"text-small"}},{labelPlacement:"inside",isMultiline:!1,size:"sm",class:{label:["group-data-[filled=true]:-translate-y-[calc(50%_+_theme(fontSize.tiny)/2_-_8px)]"],innerWrapper:"group-data-[has-label=true]:pt-4"}},{labelPlacement:"inside",isMultiline:!1,size:"md",class:{label:["group-data-[filled=true]:-translate-y-[calc(50%_+_theme(fontSize.small)/2_-_6px)]"],innerWrapper:"group-data-[has-label=true]:pt-4"}},{labelPlacement:"inside",isMultiline:!1,size:"lg",class:{label:["text-medium","group-data-[filled=true]:-translate-y-[calc(50%_+_theme(fontSize.small)/2_-_8px)]"],innerWrapper:"group-data-[has-label=true]:pt-5"}},{labelPlacement:"inside",variant:["faded","bordered"],isMultiline:!1,size:"sm",class:{label:["group-data-[filled=true]:-translate-y-[calc(50%_+_theme(fontSize.tiny)/2_-_8px_-_theme(borderWidth.medium))]"]}},{labelPlacement:"inside",variant:["faded","bordered"],isMultiline:!1,size:"md",class:{label:["group-data-[filled=true]:-translate-y-[calc(50%_+_theme(fontSize.small)/2_-_6px_-_theme(borderWidth.medium))]"]}},{labelPlacement:"inside",variant:["faded","bordered"],isMultiline:!1,size:"lg",class:{label:["text-medium","group-data-[filled=true]:-translate-y-[calc(50%_+_theme(fontSize.small)/2_-_8px_-_theme(borderWidth.medium))]"]}},{labelPlacement:"inside",variant:"underlined",isMultiline:!1,size:"sm",class:{label:["group-data-[filled=true]:-translate-y-[calc(50%_+_theme(fontSize.tiny)/2_-_5px)]"]}},{labelPlacement:"inside",variant:"underlined",isMultiline:!1,size:"md",class:{label:["group-data-[filled=true]:-translate-y-[calc(50%_+_theme(fontSize.small)/2_-_3.5px)]"]}},{labelPlacement:"inside",variant:"underlined",isMultiline:!1,size:"lg",class:{label:["text-medium","group-data-[filled=true]:-translate-y-[calc(50%_+_theme(fontSize.small)/2_-_4px)]"]}},{labelPlacement:"outside",size:"sm",isMultiline:!1,class:{label:["start-2","text-tiny","group-data-[filled=true]:-translate-y-[calc(100%_+_theme(fontSize.tiny)/2_+_16px)]"],base:"data-[has-label=true]:mt-[calc(theme(fontSize.small)_+_8px)]"}},{labelPlacement:"outside",isMultiline:!1,size:"md",class:{label:["start-3","text-small","group-data-[filled=true]:-translate-y-[calc(100%_+_theme(fontSize.small)/2_+_20px)]"],base:"data-[has-label=true]:mt-[calc(theme(fontSize.small)_+_10px)]"}},{labelPlacement:"outside",isMultiline:!1,size:"lg",class:{label:["start-3","text-medium","group-data-[filled=true]:-translate-y-[calc(100%_+_theme(fontSize.small)/2_+_24px)]"],base:"data-[has-label=true]:mt-[calc(theme(fontSize.small)_+_12px)]"}},{labelPlacement:"outside",isMultiline:!0,class:{label:"pb-1.5"}},{labelPlacement:["inside","outside"],class:{label:["pe-2","max-w-full","text-ellipsis","overflow-hidden"]}}]}),v=a(54514),h=a(73094),x=a(39217),b=a(6409),y=a(16060),w=a(82432),k=a(1172),j=a(25381),E=a(40182),z=a(73469),D=a(32168);function P(e){let[t,a]=(0,D.P)(e.isOpen,e.defaultOpen||!1,e.onOpenChange),i=(0,c.useCallback)(()=>{a(!0)},[a]),n=(0,c.useCallback)(()=>{a(!1)},[a]),o=(0,c.useCallback)(()=>{a(!t)},[a,t]);return{isOpen:t,setOpen:a,open:i,close:n,toggle:o}}var C=a(8916),S=a(30900);let O=new Map;function M(e){let{locale:t}=(0,S.Y)(),a=t+(e?Object.entries(e).sort((e,t)=>e[0]<t[0]?-1:1).join():"");if(O.has(a))return O.get(a);let i=new Intl.Collator(t,e);return O.set(a,i),i}var A=a(89130),I=a(60677),F={},R={},B={},N={},T={},_={},L={},K={},W={},q={},H={},$={},U={},V={},G={},Y={},X={},Z={},J={},Q={},ee={},et={},ea={},ei={},en={},eo={},el={},er={},es={},ec={},ep={},ed={},eu={},em={},ef={};ef={"ar-AE":{longPressMessage:`\u{627}\u{636}\u{63A}\u{637} \u{645}\u{637}\u{648}\u{644}\u{627}\u{64B} \u{623}\u{648} \u{627}\u{636}\u{63A}\u{637} \u{639}\u{644}\u{649} Alt + \u{627}\u{644}\u{633}\u{647}\u{645} \u{644}\u{623}\u{633}\u{641}\u{644} \u{644}\u{641}\u{62A}\u{62D} \u{627}\u{644}\u{642}\u{627}\u{626}\u{645}\u{629}`},"bg-BG":{longPressMessage:`\u{41D}\u{430}\u{442}\u{438}\u{441}\u{43D}\u{435}\u{442}\u{435} \u{43F}\u{440}\u{43E}\u{434}\u{44A}\u{43B}\u{436}\u{438}\u{442}\u{435}\u{43B}\u{43D}\u{43E} \u{438}\u{43B}\u{438} \u{43D}\u{430}\u{442}\u{438}\u{441}\u{43D}\u{435}\u{442}\u{435} Alt+ \u{441}\u{442}\u{440}\u{435}\u{43B}\u{43A}\u{430} \u{43D}\u{430}\u{434}\u{43E}\u{43B}\u{443}, \u{437}\u{430} \u{434}\u{430} \u{43E}\u{442}\u{432}\u{43E}\u{440}\u{438}\u{442}\u{435} \u{43C}\u{435}\u{43D}\u{44E}\u{442}\u{43E}`},"cs-CZ":{longPressMessage:`Dlouh\xfdm stiskem nebo stisknut\xedm kl\xe1ves Alt + \u{161}ipka dol\u{16F} otev\u{159}ete nab\xeddku`},"da-DK":{longPressMessage:`Langt tryk eller tryk p\xe5 Alt + pil ned for at \xe5bne menuen`},"de-DE":{longPressMessage:`Dr\xfccken Sie lange oder dr\xfccken Sie Alt + Nach-unten, um das Men\xfc zu \xf6ffnen`},"el-GR":{longPressMessage:`\u{3A0}\u{3B9}\u{3AD}\u{3C3}\u{3C4}\u{3B5} \u{3C0}\u{3B1}\u{3C1}\u{3B1}\u{3C4}\u{3B5}\u{3C4}\u{3B1}\u{3BC}\u{3AD}\u{3BD}\u{3B1} \u{3AE} \u{3C0}\u{3B1}\u{3C4}\u{3AE}\u{3C3}\u{3C4}\u{3B5} Alt + \u{3BA}\u{3AC}\u{3C4}\u{3C9} \u{3B2}\u{3AD}\u{3BB}\u{3BF}\u{3C2} \u{3B3}\u{3B9}\u{3B1} \u{3BD}\u{3B1} \u{3B1}\u{3BD}\u{3BF}\u{3AF}\u{3BE}\u{3B5}\u{3C4}\u{3B5} \u{3C4}\u{3BF} \u{3BC}\u{3B5}\u{3BD}\u{3BF}\u{3CD}`},"en-US":{longPressMessage:"Long press or press Alt + ArrowDown to open menu"},"es-ES":{longPressMessage:`Mantenga pulsado o pulse Alt + flecha abajo para abrir el men\xfa`},"et-EE":{longPressMessage:`Men\xfc\xfc avamiseks vajutage pikalt v\xf5i vajutage klahve Alt + allanool`},"fi-FI":{longPressMessage:`Avaa valikko painamalla pohjassa tai n\xe4pp\xe4inyhdistelm\xe4ll\xe4 Alt + Alanuoli`},"fr-FR":{longPressMessage:`Appuyez de mani\xe8re prolong\xe9e ou appuyez sur Alt\xa0+\xa0Fl\xe8che vers le bas pour ouvrir le menu.`},"he-IL":{longPressMessage:`\u{5DC}\u{5D7}\u{5E5} \u{5DC}\u{5D7}\u{5D9}\u{5E6}\u{5D4} \u{5D0}\u{5E8}\u{5D5}\u{5DB}\u{5D4} \u{5D0}\u{5D5} \u{5D4}\u{5E7}\u{5E9} Alt + ArrowDown \u{5DB}\u{5D3}\u{5D9} \u{5DC}\u{5E4}\u{5EA}\u{5D5}\u{5D7} \u{5D0}\u{5EA} \u{5D4}\u{5EA}\u{5E4}\u{5E8}\u{5D9}\u{5D8}`},"hr-HR":{longPressMessage:"Dugo pritisnite ili pritisnite Alt + strelicu prema dolje za otvaranje izbornika"},"hu-HU":{longPressMessage:`Nyomja meg hosszan, vagy nyomja meg az Alt + lefele ny\xedl gombot a men\xfc megnyit\xe1s\xe1hoz`},"it-IT":{longPressMessage:`Premere a lungo o premere Alt + Freccia gi\xf9 per aprire il menu`},"ja-JP":{longPressMessage:`\u{9577}\u{62BC}\u{3057}\u{307E}\u{305F}\u{306F} Alt+\u{4E0B}\u{77E2}\u{5370}\u{30AD}\u{30FC}\u{3067}\u{30E1}\u{30CB}\u{30E5}\u{30FC}\u{3092}\u{958B}\u{304F}`},"ko-KR":{longPressMessage:`\u{AE38}\u{AC8C} \u{B204}\u{B974}\u{AC70}\u{B098} Alt + \u{C544}\u{B798}\u{CABD} \u{D654}\u{C0B4}\u{D45C}\u{B97C} \u{B20C}\u{B7EC} \u{BA54}\u{B274} \u{C5F4}\u{AE30}`},"lt-LT":{longPressMessage:`Nor\u{117}dami atidaryti meniu, nuspaud\u{119} palaikykite arba paspauskite \u{201E}Alt + ArrowDown\u{201C}.`},"lv-LV":{longPressMessage:`Lai atv\u{113}rtu izv\u{113}lni, turiet nospiestu vai nospiediet tausti\u{146}u kombin\u{101}ciju Alt + lejupv\u{113}rst\u{101} bulti\u{146}a`},"nb-NO":{longPressMessage:`Langt trykk eller trykk Alt + PilNed for \xe5 \xe5pne menyen`},"nl-NL":{longPressMessage:"Druk lang op Alt + pijl-omlaag of druk op Alt om het menu te openen"},"pl-PL":{longPressMessage:`Naci\u{15B}nij i przytrzymaj lub naci\u{15B}nij klawisze Alt + Strza\u{142}ka w d\xf3\u{142}, aby otworzy\u{107} menu`},"pt-BR":{longPressMessage:"Pressione e segure ou pressione Alt + Seta para baixo para abrir o menu"},"pt-PT":{longPressMessage:"Prima continuamente ou prima Alt + Seta Para Baixo para abrir o menu"},"ro-RO":{longPressMessage:`Ap\u{103}sa\u{21B}i lung sau ap\u{103}sa\u{21B}i pe Alt + s\u{103}geat\u{103} \xeen jos pentru a deschide meniul`},"ru-RU":{longPressMessage:`\u{41D}\u{430}\u{436}\u{43C}\u{438}\u{442}\u{435} \u{438} \u{443}\u{434}\u{435}\u{440}\u{436}\u{438}\u{432}\u{430}\u{439}\u{442}\u{435} \u{438}\u{43B}\u{438} \u{43D}\u{430}\u{436}\u{43C}\u{438}\u{442}\u{435} Alt + \u{421}\u{442}\u{440}\u{435}\u{43B}\u{43A}\u{430} \u{432}\u{43D}\u{438}\u{437}, \u{447}\u{442}\u{43E}\u{431}\u{44B} \u{43E}\u{442}\u{43A}\u{440}\u{44B}\u{442}\u{44C} \u{43C}\u{435}\u{43D}\u{44E}`},"sk-SK":{longPressMessage:`Ponuku otvor\xedte dlh\xfdm stla\u{10D}en\xedm alebo stla\u{10D}en\xedm kl\xe1vesu Alt + kl\xe1vesu so \u{161}\xedpkou nadol`},"sl-SI":{longPressMessage:`Za odprtje menija pritisnite in dr\u{17E}ite gumb ali pritisnite Alt+pu\u{161}\u{10D}ica navzdol`},"sr-SP":{longPressMessage:"Dugo pritisnite ili pritisnite Alt + strelicu prema dole da otvorite meni"},"sv-SE":{longPressMessage:`H\xe5ll nedtryckt eller tryck p\xe5 Alt + pil ned\xe5t f\xf6r att \xf6ppna menyn`},"tr-TR":{longPressMessage:`Men\xfcy\xfc a\xe7mak i\xe7in uzun bas\u{131}n veya Alt + A\u{15F}a\u{11F}\u{131} Ok tu\u{15F}una bas\u{131}n`},"uk-UA":{longPressMessage:`\u{414}\u{43E}\u{432}\u{433}\u{43E} \u{430}\u{431}\u{43E} \u{437}\u{432}\u{438}\u{447}\u{430}\u{439}\u{43D}\u{43E} \u{43D}\u{430}\u{442}\u{438}\u{441}\u{43D}\u{456}\u{442}\u{44C} \u{43A}\u{43E}\u{43C}\u{431}\u{456}\u{43D}\u{430}\u{446}\u{456}\u{44E} \u{43A}\u{43B}\u{430}\u{432}\u{456}\u{448} Alt \u{456} \u{441}\u{442}\u{440}\u{456}\u{43B}\u{43A}\u{430} \u{432}\u{43D}\u{438}\u{437}, \u{449}\u{43E}\u{431} \u{432}\u{456}\u{434}\u{43A}\u{440}\u{438}\u{442}\u{438} \u{43C}\u{435}\u{43D}\u{44E}`},"zh-CN":{longPressMessage:`\u{957F}\u{6309}\u{6216}\u{6309} Alt + \u{5411}\u{4E0B}\u{65B9}\u{5411}\u{952E}\u{4EE5}\u{6253}\u{5F00}\u{83DC}\u{5355}`},"zh-TW":{longPressMessage:`\u{9577}\u{6309}\u{6216}\u{6309} Alt+\u{5411}\u{4E0B}\u{9375}\u{4EE5}\u{958B}\u{555F}\u{529F}\u{80FD}\u{8868}`}};var eg=a(58463);let ev=Symbol.for("react-aria.i18n.locale"),eh=Symbol.for("react-aria.i18n.strings");class ex{getStringForLocale(e,t){let a=this.getStringsForLocale(t)[e];if(!a)throw Error(`Could not find intl message ${e} in ${t} locale`);return a}getStringsForLocale(e){let t=this.strings[e];return t||(t=function(e,t,a="en-US"){var i;if(t[e])return t[e];let n=(i=e,Intl.Locale?new Intl.Locale(i).language:i.split("-")[0]);if(t[n])return t[n];for(let e in t)if(e.startsWith(n+"-"))return t[e];return t[a]}(e,this.strings,this.defaultLocale),this.strings[e]=t),t}static getGlobalDictionaryForPackage(e){if("undefined"==typeof window)return null;let t=window[ev];if(void 0===n){let e=window[eh];if(!e)return null;for(let a in n={},e)n[a]=new ex({[t]:e[a]},t)}let a=null==n?void 0:n[e];if(!a)throw Error(`Strings for package "${e}" were not included by LocalizedStringProvider. Please add it to the list passed to createLocalizedStringDictionary.`);return a}constructor(e,t="en-US"){this.strings=Object.fromEntries(Object.entries(e).filter(([,e])=>e)),this.defaultLocale=t}}let eb=new Map,ey=new Map;class ew{format(e,t){let a=this.strings.getStringForLocale(e,this.locale);return"function"==typeof a?a(t,this):a}plural(e,t,a="cardinal"){let i=t["="+e];if(i)return"function"==typeof i?i():i;let n=this.locale+":"+a,o=eb.get(n);return o||(o=new Intl.PluralRules(this.locale,{type:a}),eb.set(n,o)),"function"==typeof(i=t[o.select(e)]||t.other)?i():i}number(e){let t=ey.get(this.locale);return t||(t=new Intl.NumberFormat(this.locale),ey.set(this.locale,t)),t.format(e)}select(e,t){let a=e[t]||e.other;return"function"==typeof a?a():a}constructor(e,t){this.locale=e,this.strings=t}}let ek=new WeakMap;function ej(e,t){let a,{locale:i}=(0,S.Y)(),n=t&&ex.getGlobalDictionaryForPackage(t)||((a=ek.get(e))||(a=new ex(e),ek.set(e,a)),a);return(0,c.useMemo)(()=>new ew(i,n),[i,n])}var eE=a(89121);let ez=new WeakMap;function eD(e,t,a){let i,{type:n}=e,{isOpen:o}=t;(0,c.useEffect)(()=>{a&&a.current&&ez.set(a.current,t.close)}),"menu"===n?i=!0:"listbox"===n&&(i="listbox");let l=(0,eg.Bi)();return{triggerProps:{"aria-haspopup":i,"aria-expanded":o,"aria-controls":o?l:void 0,onPress:t.toggle},overlayProps:{id:l}}}class eP{getItemRect(e){let t=this.ref.current;if(!t)return null;let a=null!=e?t.querySelector(`[data-key="${CSS.escape(e.toString())}"]`):null;if(!a)return null;let i=t.getBoundingClientRect(),n=a.getBoundingClientRect();return{x:n.left-i.left+t.scrollLeft,y:n.top-i.top+t.scrollTop,width:n.width,height:n.height}}getContentSize(){var e,t;let a=this.ref.current;return{width:null!==(e=null==a?void 0:a.scrollWidth)&&void 0!==e?e:0,height:null!==(t=null==a?void 0:a.scrollHeight)&&void 0!==t?t:0}}getVisibleRect(){var e,t,a,i;let n=this.ref.current;return{x:null!==(e=null==n?void 0:n.scrollLeft)&&void 0!==e?e:0,y:null!==(t=null==n?void 0:n.scrollTop)&&void 0!==t?t:0,width:null!==(a=null==n?void 0:n.offsetWidth)&&void 0!==a?a:0,height:null!==(i=null==n?void 0:n.offsetHeight)&&void 0!==i?i:0}}constructor(e){this.ref=e}}var eC=a(35356);class eS{isDisabled(e){var t;return"all"===this.disabledBehavior&&((null===(t=e.props)||void 0===t?void 0:t.isDisabled)||this.disabledKeys.has(e.key))}findNextNonDisabled(e,t){let a=e;for(;null!=a;){let e=this.collection.getItem(a);if((null==e?void 0:e.type)==="item"&&!this.isDisabled(e))return a;a=t(a)}return null}getNextKey(e){let t=e;return t=this.collection.getKeyAfter(t),this.findNextNonDisabled(t,e=>this.collection.getKeyAfter(e))}getPreviousKey(e){let t=e;return t=this.collection.getKeyBefore(t),this.findNextNonDisabled(t,e=>this.collection.getKeyBefore(e))}findKey(e,t,a){let i=e,n=this.layoutDelegate.getItemRect(i);if(!n||null==i)return null;let o=n;do{if(null==(i=t(i)))break;n=this.layoutDelegate.getItemRect(i)}while(n&&a(o,n)&&null!=i);return i}isSameRow(e,t){return e.y===t.y||e.x!==t.x}isSameColumn(e,t){return e.x===t.x||e.y!==t.y}getKeyBelow(e){return"grid"===this.layout&&"vertical"===this.orientation?this.findKey(e,e=>this.getNextKey(e),this.isSameRow):this.getNextKey(e)}getKeyAbove(e){return"grid"===this.layout&&"vertical"===this.orientation?this.findKey(e,e=>this.getPreviousKey(e),this.isSameRow):this.getPreviousKey(e)}getNextColumn(e,t){return t?this.getPreviousKey(e):this.getNextKey(e)}getKeyRightOf(e){let t="ltr"===this.direction?"getKeyRightOf":"getKeyLeftOf";return this.layoutDelegate[t]?(e=this.layoutDelegate[t](e),this.findNextNonDisabled(e,e=>this.layoutDelegate[t](e))):"grid"===this.layout?"vertical"===this.orientation?this.getNextColumn(e,"rtl"===this.direction):this.findKey(e,e=>this.getNextColumn(e,"rtl"===this.direction),this.isSameColumn):"horizontal"===this.orientation?this.getNextColumn(e,"rtl"===this.direction):null}getKeyLeftOf(e){let t="ltr"===this.direction?"getKeyLeftOf":"getKeyRightOf";return this.layoutDelegate[t]?(e=this.layoutDelegate[t](e),this.findNextNonDisabled(e,e=>this.layoutDelegate[t](e))):"grid"===this.layout?"vertical"===this.orientation?this.getNextColumn(e,"ltr"===this.direction):this.findKey(e,e=>this.getNextColumn(e,"ltr"===this.direction),this.isSameColumn):"horizontal"===this.orientation?this.getNextColumn(e,"ltr"===this.direction):null}getFirstKey(){let e=this.collection.getFirstKey();return this.findNextNonDisabled(e,e=>this.collection.getKeyAfter(e))}getLastKey(){let e=this.collection.getLastKey();return this.findNextNonDisabled(e,e=>this.collection.getKeyBefore(e))}getKeyPageAbove(e){let t=this.ref.current,a=this.layoutDelegate.getItemRect(e);if(!a)return null;if(t&&!(0,eC.o)(t))return this.getFirstKey();let i=e;if("horizontal"===this.orientation){let e=Math.max(0,a.x+a.width-this.layoutDelegate.getVisibleRect().width);for(;a&&a.x>e&&null!=i;)a=null==(i=this.getKeyAbove(i))?null:this.layoutDelegate.getItemRect(i)}else{let e=Math.max(0,a.y+a.height-this.layoutDelegate.getVisibleRect().height);for(;a&&a.y>e&&null!=i;)a=null==(i=this.getKeyAbove(i))?null:this.layoutDelegate.getItemRect(i)}return null!=i?i:this.getFirstKey()}getKeyPageBelow(e){let t=this.ref.current,a=this.layoutDelegate.getItemRect(e);if(!a)return null;if(t&&!(0,eC.o)(t))return this.getLastKey();let i=e;if("horizontal"===this.orientation){let e=Math.min(this.layoutDelegate.getContentSize().width,a.y-a.width+this.layoutDelegate.getVisibleRect().width);for(;a&&a.x<e&&null!=i;)a=null==(i=this.getKeyBelow(i))?null:this.layoutDelegate.getItemRect(i)}else{let e=Math.min(this.layoutDelegate.getContentSize().height,a.y-a.height+this.layoutDelegate.getVisibleRect().height);for(;a&&a.y<e&&null!=i;)a=null==(i=this.getKeyBelow(i))?null:this.layoutDelegate.getItemRect(i)}return null!=i?i:this.getLastKey()}getKeyForSearch(e,t){if(!this.collator)return null;let a=this.collection,i=t||this.getFirstKey();for(;null!=i;){let t=a.getItem(i);if(!t)break;let n=t.textValue.slice(0,e.length);if(t.textValue&&0===this.collator.compare(n,e))return i;i=this.getNextKey(i)}return null}constructor(...e){if(1===e.length){let t=e[0];this.collection=t.collection,this.ref=t.ref,this.collator=t.collator,this.disabledKeys=t.disabledKeys||new Set,this.disabledBehavior=t.disabledBehavior||"all",this.orientation=t.orientation||"vertical",this.direction=t.direction,this.layout=t.layout||"stack",this.layoutDelegate=t.layoutDelegate||new eP(t.ref)}else this.collection=e[0],this.disabledKeys=e[1],this.ref=e[2],this.collator=e[3],this.layout="stack",this.orientation="vertical",this.disabledBehavior="all",this.layoutDelegate=new eP(this.ref);"stack"===this.layout&&"vertical"===this.orientation&&(this.getKeyLeftOf=void 0,this.getKeyRightOf=void 0)}}var eO=a(38663),eM=a(45427),eA=a(72406),eI=a(78607),eF=(e,t,a)=>{let i=null==t?void 0:t.current;if(!i||!i.contains(e)){let e=document.querySelectorAll("body > span[data-focus-scope-start]"),t=[];if(e.forEach(e=>{t.push(e.nextElementSibling)}),1===t.length)return a.close(),!1}return!i||!i.contains(e)},eR=a(94690),eB=a(60292),eN=new WeakMap,eT=a(9311),e_=a(28017);let eL={border:0,clip:"rect(0 0 0 0)",clipPath:"inset(50%)",height:"1px",margin:"-1px",overflow:"hidden",padding:0,position:"absolute",width:"1px",whiteSpace:"nowrap"};function eK(e={}){let{style:t,isFocusable:a}=e,[i,n]=(0,c.useState)(!1),{focusWithinProps:o}=(0,e_.R)({isDisabled:!a,onFocusWithinChange:e=>n(e)}),l=(0,c.useMemo)(()=>i?t:t?{...eL,...t}:eL,[i]);return{visuallyHiddenProps:{...o,style:l}}}function eW(e){let{children:t,elementType:a="div",isFocusable:i,style:n,...o}=e,{visuallyHiddenProps:l}=eK(e);return c.createElement(a,(0,j.v)(o,l),t)}var eq=a(14192);function eH(e){var t;let{state:a,triggerRef:i,selectRef:n,label:l,name:r,isDisabled:s}=e,{containerProps:c,selectProps:p}=function(e,t,a){var i;let n=eN.get(t)||{},{autoComplete:o,name:l=n.name,isDisabled:r=n.isDisabled,selectionMode:s,onChange:c}=e,{validationBehavior:p,isRequired:d,isInvalid:u}=n,{visuallyHiddenProps:m}=eK();return(0,eT.F)(e.selectRef,t.selectedKeys,t.setSelectedKeys),(0,eq.X)({validationBehavior:p,focus:()=>{var e;return null==(e=a.current)?void 0:e.focus()}},t,e.selectRef),{containerProps:{...m,"aria-hidden":!0,"data-a11y-ignore":"aria-hidden-focus"},inputProps:{style:{display:"none"}},selectProps:{autoComplete:o,disabled:r,"aria-invalid":u||void 0,"aria-required":d&&"aria"===p||void 0,required:d&&"native"===p,name:l,tabIndex:-1,value:"multiple"===s?[...t.selectedKeys].map(e=>String(e)):null!=(i=[...t.selectedKeys][0])?i:"",multiple:"multiple"===s,onChange:e=>{t.setSelectedKeys(e.target.value),null==c||c(e)}}}}({...e,selectRef:n},a,i);return a.collection.size<=300?(0,o.jsx)("div",{...c,"data-testid":"hidden-select-container",children:(0,o.jsxs)("label",{children:[l,(0,o.jsxs)("select",{...p,ref:n,children:[(0,o.jsx)("option",{}),[...a.collection.getKeys()].map(e=>{let t=a.collection.getItem(e);if((null==t?void 0:t.type)==="item")return(0,o.jsx)("option",{value:t.key,children:t.textValue},t.key)})]})]})}):r?(0,o.jsx)("input",{autoComplete:p.autoComplete,disabled:s,name:r,type:"hidden",value:null!=(t=[...a.selectedKeys].join(","))?t:""}):null}let e$=new WeakMap;var eU=a(46039),eV=a(92e3),eG=(0,m.tv)({slots:{base:"w-full relative flex flex-col gap-1 p-1 overflow-clip",list:"w-full flex flex-col gap-0.5 outline-none",emptyContent:["h-10","px-2","py-1.5","w-full","h-full","text-foreground-400","text-start"]}}),eY=(0,m.tv)({slots:{base:["flex","group","gap-2","items-center","justify-between","relative","px-2","py-1.5","w-full","h-full","box-border","rounded-small","subpixel-antialiased","outline-none","cursor-pointer","tap-highlight-transparent",...f.zb,"data-[focus-visible=true]:dark:ring-offset-background-content1"],wrapper:"w-full flex flex-col items-start justify-center",title:"flex-1 text-small font-normal",description:["w-full","text-tiny","text-foreground-500","group-hover:text-current"],selectedIcon:["text-inherit","w-3","h-3","flex-shrink-0"],shortcut:["px-1","py-0.5","rounded","font-sans","text-foreground-500","text-tiny","border-small","border-default-300","group-hover:border-current"]},variants:{variant:{solid:{base:""},bordered:{base:"border-medium border-transparent bg-transparent"},light:{base:"bg-transparent"},faded:{base:["border-small border-transparent hover:border-default data-[hover=true]:bg-default-100","data-[selectable=true]:focus:border-default data-[selectable=true]:focus:bg-default-100"]},flat:{base:""},shadow:{base:"data-[hover=true]:shadow-lg"}},color:{default:{},primary:{},secondary:{},success:{},warning:{},danger:{}},showDivider:{true:{base:["mb-1.5","after:content-['']","after:absolute","after:-bottom-1","after:left-0","after:right-0","after:h-divider","after:bg-divider"]},false:{}},isDisabled:{true:{base:"opacity-disabled pointer-events-none"}},disableAnimation:{true:{},false:{base:"data-[hover=true]:transition-colors"}},hasTitleTextChild:{true:{title:"truncate"}},hasDescriptionTextChild:{true:{description:"truncate"}}},defaultVariants:{variant:"solid",color:"default",showDivider:!1},compoundVariants:[{variant:"solid",color:"default",class:{base:["data-[hover=true]:bg-default","data-[hover=true]:text-default-foreground","data-[selectable=true]:focus:bg-default","data-[selectable=true]:focus:text-default-foreground"]}},{variant:"solid",color:"primary",class:{base:["data-[hover=true]:bg-primary data-[hover=true]:text-primary-foreground","data-[selectable=true]:focus:bg-primary data-[selectable=true]:focus:text-primary-foreground"]}},{variant:"solid",color:"secondary",class:{base:["data-[hover=true]:bg-secondary data-[hover=true]:text-secondary-foreground","data-[selectable=true]:focus:bg-secondary data-[selectable=true]:focus:text-secondary-foreground"]}},{variant:"solid",color:"success",class:{base:["data-[hover=true]:bg-success data-[hover=true]:text-success-foreground","data-[selectable=true]:focus:bg-success data-[selectable=true]:focus:text-success-foreground"]}},{variant:"solid",color:"warning",class:{base:["data-[hover=true]:bg-warning data-[hover=true]:text-warning-foreground","data-[selectable=true]:focus:bg-warning data-[selectable=true]:focus:text-warning-foreground"]}},{variant:"solid",color:"danger",class:{base:["data-[hover=true]:bg-danger data-[hover=true]:text-danger-foreground","data-[selectable=true]:focus:bg-danger data-[selectable=true]:focus:text-danger-foreground"]}},{variant:"shadow",color:"default",class:{base:["data-[hover=true]:shadow-default/50 data-[hover=true]:bg-default data-[hover=true]:text-default-foreground","data-[selectable=true]:focus:shadow-default/50 data-[selectable=true]:focus:bg-default data-[selectable=true]:focus:text-default-foreground"]}},{variant:"shadow",color:"primary",class:{base:["data-[hover=true]:shadow-primary/30 data-[hover=true]:bg-primary data-[hover=true]:text-primary-foreground","data-[selectable=true]:focus:shadow-primary/30 data-[selectable=true]:focus:bg-primary data-[selectable=true]:focus:text-primary-foreground"]}},{variant:"shadow",color:"secondary",class:{base:["data-[hover=true]:shadow-secondary/30 data-[hover=true]:bg-secondary data-[hover=true]:text-secondary-foreground","data-[selectable=true]:focus:shadow-secondary/30 data-[selectable=true]:focus:bg-secondary data-[selectable=true]:focus:text-secondary-foreground"]}},{variant:"shadow",color:"success",class:{base:["data-[hover=true]:shadow-success/30 data-[hover=true]:bg-success data-[hover=true]:text-success-foreground","data-[selectable=true]:focus:shadow-success/30 data-[selectable=true]:focus:bg-success data-[selectable=true]:focus:text-success-foreground"]}},{variant:"shadow",color:"warning",class:{base:["data-[hover=true]:shadow-warning/30 data-[hover=true]:bg-warning data-[hover=true]:text-warning-foreground","data-[selectable=true]:focus:shadow-warning/30 data-[selectable=true]:focus:bg-warning data-[selectable=true]:focus:text-warning-foreground"]}},{variant:"shadow",color:"danger",class:{base:["data-[hover=true]:shadow-danger/30 data-[hover=true]:bg-danger data-[hover=true]:text-danger-foreground","data-[selectable=true]:focus:shadow-danger/30 data-[selectable=true]:focus:bg-danger data-[selectable=true]:focus:text-danger-foreground"]}},{variant:"bordered",color:"default",class:{base:["data-[hover=true]:border-default","data-[selectable=true]:focus:border-default"]}},{variant:"bordered",color:"primary",class:{base:["data-[hover=true]:border-primary data-[hover=true]:text-primary","data-[selectable=true]:focus:border-primary data-[selectable=true]:focus:text-primary"]}},{variant:"bordered",color:"secondary",class:{base:["data-[hover=true]:border-secondary data-[hover=true]:text-secondary","data-[selectable=true]:focus:border-secondary data-[selectable=true]:focus:text-secondary"]}},{variant:"bordered",color:"success",class:{base:["data-[hover=true]:border-success data-[hover=true]:text-success","data-[selectable=true]:focus:border-success data-[selectable=true]:focus:text-success"]}},{variant:"bordered",color:"warning",class:{base:["data-[hover=true]:border-warning data-[hover=true]:text-warning","data-[selectable=true]:focus:border-warning data-[selectable=true]:focus:text-warning"]}},{variant:"bordered",color:"danger",class:{base:["data-[hover=true]:border-danger data-[hover=true]:text-danger","data-[selectable=true]:focus:border-danger data-[selectable=true]:focus:text-danger"]}},{variant:"flat",color:"default",class:{base:["data-[hover=true]:bg-default/40","data-[hover=true]:text-default-foreground","data-[selectable=true]:focus:bg-default/40","data-[selectable=true]:focus:text-default-foreground"]}},{variant:"flat",color:"primary",class:{base:["data-[hover=true]:bg-primary/20 data-[hover=true]:text-primary","data-[selectable=true]:focus:bg-primary/20 data-[selectable=true]:focus:text-primary"]}},{variant:"flat",color:"secondary",class:{base:["data-[hover=true]:bg-secondary/20 data-[hover=true]:text-secondary","data-[selectable=true]:focus:bg-secondary/20 data-[selectable=true]:focus:text-secondary"]}},{variant:"flat",color:"success",class:{base:["data-[hover=true]:bg-success/20 data-[hover=true]:text-success","data-[selectable=true]:focus:bg-success/20 data-[selectable=true]:focus:text-success"]}},{variant:"flat",color:"warning",class:{base:["data-[hover=true]:bg-warning/20 data-[hover=true]:text-warning","data-[selectable=true]:focus:bg-warning/20 data-[selectable=true]:focus:text-warning"]}},{variant:"flat",color:"danger",class:{base:["data-[hover=true]:bg-danger/20 data-[hover=true]:text-danger","data-[selectable=true]:focus:bg-danger/20 data-[selectable=true]:focus:text-danger"]}},{variant:"faded",color:"default",class:{base:["data-[hover=true]:text-default-foreground","data-[selectable=true]:focus:text-default-foreground"]}},{variant:"faded",color:"primary",class:{base:["data-[hover=true]:text-primary","data-[selectable=true]:focus:text-primary"]}},{variant:"faded",color:"secondary",class:{base:["data-[hover=true]:text-secondary","data-[selectable=true]:focus:text-secondary"]}},{variant:"faded",color:"success",class:{base:["data-[hover=true]:text-success","data-[selectable=true]:focus:text-success"]}},{variant:"faded",color:"warning",class:{base:["data-[hover=true]:text-warning","data-[selectable=true]:focus:text-warning"]}},{variant:"faded",color:"danger",class:{base:["data-[hover=true]:text-danger","data-[selectable=true]:focus:text-danger"]}},{variant:"light",color:"default",class:{base:["data-[hover=true]:text-default-500","data-[selectable=true]:focus:text-default-500"]}},{variant:"light",color:"primary",class:{base:["data-[hover=true]:text-primary","data-[selectable=true]:focus:text-primary"]}},{variant:"light",color:"secondary",class:{base:["data-[hover=true]:text-secondary","data-[selectable=true]:focus:text-secondary"]}},{variant:"light",color:"success",class:{base:["data-[hover=true]:text-success","data-[selectable=true]:focus:text-success"]}},{variant:"light",color:"warning",class:{base:["data-[hover=true]:text-warning","data-[selectable=true]:focus:text-warning"]}},{variant:"light",color:"danger",class:{base:["data-[hover=true]:text-danger","data-[selectable=true]:focus:text-danger"]}}]}),eX=(0,m.tv)({slots:{base:"relative mb-2",heading:"pl-1 text-tiny text-foreground-500",group:"data-[has-title=true]:pt-1",divider:"mt-2"}});function eZ(e){let{isSelected:t,disableAnimation:a,...i}=e;return(0,o.jsx)("svg",{"aria-hidden":"true","data-selected":t,role:"presentation",viewBox:"0 0 17 18",...i,children:(0,o.jsx)("polyline",{fill:"none",points:"1 9 7 14 15 4",stroke:"currentColor",strokeDasharray:22,strokeDashoffset:t?44:66,strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,style:a?{}:{transition:"stroke-dashoffset 200ms ease"}})})}var eJ=a(61316),eQ=a(53570),e0=a(66775);let e1=new WeakMap;var e4=a(31294),e3=a(58285),e2=a(33143),e5=e=>{let{Component:t,rendered:a,description:i,isSelectable:n,isSelected:l,isDisabled:r,selectedIcon:s,startContent:p,endContent:m,hideSelectedIcon:f,disableAnimation:g,getItemProps:v,getLabelProps:x,getWrapperProps:z,getDescriptionProps:D,getSelectedIconProps:P}=function(e){var t,a;let i=(0,d.o)(),[n,o]=(0,u.rE)(e,eY.variantKeys),{as:l,item:r,state:s,description:p,startContent:m,endContent:f,isVirtualized:g,selectedIcon:v,className:x,classNames:z,autoFocus:D,onPress:P,onClick:C,shouldHighlightOnFocus:S,hideSelectedIcon:O=!1,isReadOnly:M=!1,...I}=n,F=null!=(a=null!=(t=e.disableAnimation)?t:null==i?void 0:i.disableAnimation)&&a,R=(0,c.useRef)(null),B=l||(e.href?"a":"li"),N="string"==typeof B,{rendered:T,key:_}=r,L=s.disabledKeys.has(_)||e.isDisabled,K="none"!==s.selectionManager.selectionMode,W=!(0,e2.wR)()&&"undefined"!=typeof window&&window.screen.width<=700;C&&"function"==typeof C&&(0,eJ.R)("onClick is deprecated, please use onPress instead. See: https://github.com/nextui-org/nextui/issues/4292","ListboxItem");let{pressProps:q,isPressed:H}=(0,e3.d)({ref:R,isDisabled:L,onPress:P}),{isHovered:$,hoverProps:U}=(0,E.M)({isDisabled:L}),{isFocusVisible:V,focusProps:G}=(0,b.o)({autoFocus:D}),{isFocused:Y,isSelected:X,optionProps:Z,labelProps:J,descriptionProps:Q}=function(e,t,a){var i,n,o,l,r,s,c,p;let{key:d}=e,u=e$.get(t),m=null!==(o=e.isDisabled)&&void 0!==o?o:t.selectionManager.isDisabled(d),f=null!==(l=e.isSelected)&&void 0!==l?l:t.selectionManager.isSelected(d),g=null!==(r=e.shouldSelectOnPressUp)&&void 0!==r?r:null==u?void 0:u.shouldSelectOnPressUp,v=null!==(s=e.shouldFocusOnHover)&&void 0!==s?s:null==u?void 0:u.shouldFocusOnHover,h=null!==(c=e.shouldUseVirtualFocus)&&void 0!==c?c:null==u?void 0:u.shouldUseVirtualFocus,x=null!==(p=e.isVirtualized)&&void 0!==p?p:null==u?void 0:u.isVirtualized,b=(0,eg.X1)(),y=(0,eg.X1)(),w={role:"option","aria-disabled":m||void 0,"aria-selected":"none"!==t.selectionManager.selectionMode?f:void 0};(0,eQ.cX)()&&(0,eQ.Tc)()||(w["aria-label"]=e["aria-label"],w["aria-labelledby"]=b,w["aria-describedby"]=y);let k=t.collection.getItem(d);if(x){let e=Number(null==k?void 0:k.index);w["aria-posinset"]=Number.isNaN(e)?void 0:e+1,w["aria-setsize"]=function(e){let t=e1.get(e);if(null!=t)return t;let a=0,i=t=>{for(let n of t)if("section"===n.type)i("function"==typeof e.getChildren?e.getChildren(n.key):n.childNodes);else a++};return i(e),e1.set(e,a),a}(t.collection)}let z=(null==u?void 0:u.onAction)?()=>{var e;return null==u?void 0:null===(e=u.onAction)||void 0===e?void 0:e.call(u,d)}:void 0,{itemProps:D,isPressed:P,isFocused:C,hasAction:S,allowsSelection:O}=(0,e4.p)({selectionManager:t.selectionManager,key:d,ref:a,shouldSelectOnPressUp:g,allowsDifferentPressOrigin:g&&v,isVirtualized:x,shouldUseVirtualFocus:h,isDisabled:m,onAction:z||(null==k?void 0:null===(i=k.props)||void 0===i?void 0:i.onAction)?(0,eA.c)(null==k?void 0:null===(n=k.props)||void 0===n?void 0:n.onAction,z):void 0,linkBehavior:null==u?void 0:u.linkBehavior}),{hoverProps:M}=(0,E.M)({isDisabled:m||!v,onHoverStart(){(0,A.pP)()||(t.selectionManager.setFocused(!0),t.selectionManager.setFocusedKey(d))}}),I=(0,eM.$)(null==k?void 0:k.props);delete I.id;let F=(0,e0._h)(null==k?void 0:k.props);return{optionProps:{...w,...(0,j.v)(I,D,M,F),id:function(e,t){let a=e$.get(e);if(!a)throw Error("Unknown list");return`${a.id}-option-${"string"==typeof t?t.replace(/\s*/g,""):""+t}`}(t,d)},labelProps:{id:b},descriptionProps:{id:y},isFocused:C,isFocusVisible:C&&(0,A.pP)(),isSelected:f,isDisabled:m,isPressed:P,allowsSelection:O,hasAction:S}}({key:_,isDisabled:L,"aria-label":n["aria-label"],isVirtualized:g},s,R),ee=Z,et=(0,c.useMemo)(()=>eY({...o,isDisabled:L,disableAnimation:F,hasTitleTextChild:"string"==typeof T,hasDescriptionTextChild:"string"==typeof p}),[(0,w.t6)(o),L,F,T,p]),ea=(0,y.$)(null==z?void 0:z.base,x);M&&(ee=(0,w.GU)(ee));let ei=S&&Y||(W?$||H:$||Y&&!V),en=(0,c.useCallback)((e={})=>({"aria-hidden":(0,k.sE)(!0),"data-disabled":(0,k.sE)(L),className:et.selectedIcon({class:null==z?void 0:z.selectedIcon}),...e}),[L,et,z]);return{Component:B,domRef:R,slots:et,classNames:z,isSelectable:K,isSelected:X,isDisabled:L,rendered:T,description:p,startContent:m,endContent:f,selectedIcon:v,hideSelectedIcon:O,disableAnimation:F,getItemProps:(e={})=>({ref:R,...(0,j.v)({onClick:C},ee,M?{}:(0,j.v)(G,q),U,(0,h.$)(I,{enabled:N}),e),"data-selectable":(0,k.sE)(K),"data-focus":(0,k.sE)(Y),"data-hover":(0,k.sE)(ei),"data-disabled":(0,k.sE)(L),"data-selected":(0,k.sE)(X),"data-pressed":(0,k.sE)(H),"data-focus-visible":(0,k.sE)(V),className:et.base({class:(0,y.$)(ea,e.className)})}),getLabelProps:(e={})=>({...(0,j.v)(J,e),"data-label":(0,k.sE)(!0),className:et.title({class:null==z?void 0:z.title})}),getWrapperProps:(e={})=>({...(0,j.v)(e),className:et.wrapper({class:null==z?void 0:z.wrapper})}),getDescriptionProps:(e={})=>({...(0,j.v)(Q,e),className:et.description({class:null==z?void 0:z.description})}),getSelectedIconProps:en}}(e),C=(0,c.useMemo)(()=>{let e=(0,o.jsx)(eZ,{disableAnimation:g,isSelected:l});return"function"==typeof s?s({icon:e,isSelected:l,isDisabled:r}):s||e},[s,l,r,g]);return(0,o.jsxs)(t,{...v(),children:[p,i?(0,o.jsxs)("div",{...z(),children:[(0,o.jsx)("span",{...x(),children:a}),(0,o.jsx)("span",{...D(),children:i})]}):(0,o.jsx)("span",{...x(),children:a}),n&&!f&&(0,o.jsx)("span",{...P(),children:C}),m]})};e5.displayName="NextUI.ListboxItem";var e6=(0,m.tv)({base:"shrink-0 bg-divider border-none",variants:{orientation:{horizontal:"w-full h-divider",vertical:"h-full w-divider"}},defaultVariants:{orientation:"horizontal"}}),e8=(0,u.Rf)((e,t)=>{let{Component:a,getDividerProps:i}=function(e){var t;let a,i;let{as:n,className:o,orientation:l,...r}=e,s=n||"hr";"hr"===s&&"vertical"===l&&(s="div");let{separatorProps:p}=(t={elementType:"string"==typeof s?s:"hr",orientation:l},i=(0,h.$)(t,{enabled:"string"==typeof t.elementType}),("vertical"===t.orientation&&(a="vertical"),"hr"!==t.elementType)?{separatorProps:{...i,role:"separator","aria-orientation":a}}:{separatorProps:i}),d=(0,c.useMemo)(()=>e6({orientation:l,className:o}),[l,o]);return{Component:s,getDividerProps:(0,c.useCallback)((e={})=>({className:d,role:"separator","data-orientation":l,...p,...r,...e}),[d,l,p,r])}}({...e});return(0,o.jsx)(a,{ref:t,...i()})});e8.displayName="NextUI.Divider";var e7=(0,u.Rf)(({item:e,state:t,as:a,variant:i,color:n,disableAnimation:l,className:r,classNames:s,hideSelectedIcon:p,showDivider:d=!1,dividerProps:u={},itemClasses:m,title:f,items:g,...v},h)=>{let x=(0,c.useMemo)(()=>eX(),[]),b=(0,y.$)(null==s?void 0:s.base,r),w=(0,y.$)(null==s?void 0:s.divider,null==u?void 0:u.className),{itemProps:k,headingProps:E,groupProps:z}=function(e){let{heading:t,"aria-label":a}=e,i=(0,eg.Bi)();return{itemProps:{role:"presentation"},headingProps:t?{id:i,role:"presentation"}:{},groupProps:{role:"group","aria-label":a,"aria-labelledby":t?i:void 0}}}({heading:e.rendered,"aria-label":e["aria-label"]});return(0,o.jsxs)(a||"li",{"data-slot":"base",...(0,j.v)(k,v),className:x.base({class:b}),children:[e.rendered&&(0,o.jsx)("span",{...E,className:x.heading({class:null==s?void 0:s.heading}),"data-slot":"heading",children:e.rendered}),(0,o.jsxs)("ul",{...z,className:x.group({class:null==s?void 0:s.group}),"data-has-title":!!e.rendered,"data-slot":"group",children:[[...e.childNodes].map(e=>{let{key:a,props:r}=e,s=(0,o.jsx)(e5,{classNames:m,color:n,disableAnimation:l,hideSelectedIcon:p,item:e,state:t,variant:i,...r},a);return e.wrapper&&(s=e.wrapper(s)),s}),d&&(0,o.jsx)(e8,{as:"li",className:x.divider({class:w}),...u})]})]},e.key)});e7.displayName="NextUI.ListboxSection";var e9=a(51215);function te(e,t,a){let i,n=a.initialDeps??[];return()=>{var o,l,r,s;let c,p;a.key&&(null==(o=a.debug)?void 0:o.call(a))&&(c=Date.now());let d=e();if(!(d.length!==n.length||d.some((e,t)=>n[t]!==e)))return i;if(n=d,a.key&&(null==(l=a.debug)?void 0:l.call(a))&&(p=Date.now()),i=t(...d),a.key&&(null==(r=a.debug)?void 0:r.call(a))){let e=Math.round((Date.now()-c)*100)/100,t=Math.round((Date.now()-p)*100)/100,i=t/16,n=(e,t)=>{for(e=String(e);e.length<t;)e=" "+e;return e};console.info(`%c⏱ ${n(t,5)} /${n(e,5)} ms`,`
            font-size: .6rem;
            font-weight: bold;
            color: hsl(${Math.max(0,Math.min(120-120*i,120))}deg 100% 31%);`,null==a?void 0:a.key)}return null==(s=null==a?void 0:a.onChange)||s.call(a,i),i}}function tt(e,t){if(void 0!==e)return e;throw Error(`Unexpected undefined${t?`: ${t}`:""}`)}let ta=(e,t)=>1>Math.abs(e-t),ti=(e,t,a)=>{let i;return function(...n){e.clearTimeout(i),i=e.setTimeout(()=>t.apply(this,n),a)}},tn=e=>e,to=e=>{let t=Math.max(e.startIndex-e.overscan,0),a=Math.min(e.endIndex+e.overscan,e.count-1),i=[];for(let e=t;e<=a;e++)i.push(e);return i},tl=(e,t)=>{let a=e.scrollElement;if(!a)return;let i=e.targetWindow;if(!i)return;let n=e=>{let{width:a,height:i}=e;t({width:Math.round(a),height:Math.round(i)})};if(n(a.getBoundingClientRect()),!i.ResizeObserver)return()=>{};let o=new i.ResizeObserver(e=>{let t=e[0];if(null==t?void 0:t.borderBoxSize){let e=t.borderBoxSize[0];if(e){n({width:e.inlineSize,height:e.blockSize});return}}n(a.getBoundingClientRect())});return o.observe(a,{box:"border-box"}),()=>{o.unobserve(a)}},tr={passive:!0},ts="undefined"==typeof window||"onscrollend"in window,tc=(e,t)=>{let a=e.scrollElement;if(!a)return;let i=e.targetWindow;if(!i)return;let n=0,o=e.options.useScrollendEvent&&ts?()=>void 0:ti(i,()=>{t(n,!1)},e.options.isScrollingResetDelay),l=i=>()=>{let{horizontal:l,isRtl:r}=e.options;n=l?a.scrollLeft*(r&&-1||1):a.scrollTop,o(),t(n,i)},r=l(!0),s=l(!1);return s(),a.addEventListener("scroll",r,tr),a.addEventListener("scrollend",s,tr),()=>{a.removeEventListener("scroll",r),a.removeEventListener("scrollend",s)}},tp=(e,t,a)=>{if(null==t?void 0:t.borderBoxSize){let e=t.borderBoxSize[0];if(e)return Math.round(e[a.options.horizontal?"inlineSize":"blockSize"])}return Math.round(e.getBoundingClientRect()[a.options.horizontal?"width":"height"])},td=(e,{adjustments:t=0,behavior:a},i)=>{var n,o;null==(o=null==(n=i.scrollElement)?void 0:n.scrollTo)||o.call(n,{[i.options.horizontal?"left":"top"]:e+t,behavior:a})};class tu{constructor(e){this.unsubs=[],this.scrollElement=null,this.targetWindow=null,this.isScrolling=!1,this.scrollToIndexTimeoutId=null,this.measurementsCache=[],this.itemSizeCache=new Map,this.pendingMeasuredCacheIndexes=[],this.scrollRect=null,this.scrollOffset=null,this.scrollDirection=null,this.scrollAdjustments=0,this.elementsCache=new Map,this.observer=(()=>{let e=null,t=()=>e||(this.targetWindow&&this.targetWindow.ResizeObserver?e=new this.targetWindow.ResizeObserver(e=>{e.forEach(e=>{this._measureElement(e.target,e)})}):null);return{disconnect:()=>{var a;null==(a=t())||a.disconnect(),e=null},observe:e=>{var a;return null==(a=t())?void 0:a.observe(e,{box:"border-box"})},unobserve:e=>{var a;return null==(a=t())?void 0:a.unobserve(e)}}})(),this.range=null,this.setOptions=e=>{Object.entries(e).forEach(([t,a])=>{void 0===a&&delete e[t]}),this.options={debug:!1,initialOffset:0,overscan:1,paddingStart:0,paddingEnd:0,scrollPaddingStart:0,scrollPaddingEnd:0,horizontal:!1,getItemKey:tn,rangeExtractor:to,onChange:()=>{},measureElement:tp,initialRect:{width:0,height:0},scrollMargin:0,gap:0,indexAttribute:"data-index",initialMeasurementsCache:[],lanes:1,isScrollingResetDelay:150,enabled:!0,isRtl:!1,useScrollendEvent:!0,...e}},this.notify=e=>{var t,a;null==(a=(t=this.options).onChange)||a.call(t,this,e)},this.maybeNotify=te(()=>(this.calculateRange(),[this.isScrolling,this.range?this.range.startIndex:null,this.range?this.range.endIndex:null]),e=>{this.notify(e)},{key:!1,debug:()=>this.options.debug,initialDeps:[this.isScrolling,this.range?this.range.startIndex:null,this.range?this.range.endIndex:null]}),this.cleanup=()=>{this.unsubs.filter(Boolean).forEach(e=>e()),this.unsubs=[],this.observer.disconnect(),this.scrollElement=null,this.targetWindow=null},this._didMount=()=>()=>{this.cleanup()},this._willUpdate=()=>{var e;let t=this.options.enabled?this.options.getScrollElement():null;if(this.scrollElement!==t){if(this.cleanup(),!t){this.maybeNotify();return}this.scrollElement=t,this.scrollElement&&"ownerDocument"in this.scrollElement?this.targetWindow=this.scrollElement.ownerDocument.defaultView:this.targetWindow=(null==(e=this.scrollElement)?void 0:e.window)??null,this.elementsCache.forEach(e=>{this.observer.observe(e)}),this._scrollToOffset(this.getScrollOffset(),{adjustments:void 0,behavior:void 0}),this.unsubs.push(this.options.observeElementRect(this,e=>{this.scrollRect=e,this.maybeNotify()})),this.unsubs.push(this.options.observeElementOffset(this,(e,t)=>{this.scrollAdjustments=0,this.scrollDirection=t?this.getScrollOffset()<e?"forward":"backward":null,this.scrollOffset=e,this.isScrolling=t,this.maybeNotify()}))}},this.getSize=()=>this.options.enabled?(this.scrollRect=this.scrollRect??this.options.initialRect,this.scrollRect[this.options.horizontal?"width":"height"]):(this.scrollRect=null,0),this.getScrollOffset=()=>this.options.enabled?(this.scrollOffset=this.scrollOffset??("function"==typeof this.options.initialOffset?this.options.initialOffset():this.options.initialOffset),this.scrollOffset):(this.scrollOffset=null,0),this.getFurthestMeasurement=(e,t)=>{let a=new Map,i=new Map;for(let n=t-1;n>=0;n--){let t=e[n];if(a.has(t.lane))continue;let o=i.get(t.lane);if(null==o||t.end>o.end?i.set(t.lane,t):t.end<o.end&&a.set(t.lane,!0),a.size===this.options.lanes)break}return i.size===this.options.lanes?Array.from(i.values()).sort((e,t)=>e.end===t.end?e.index-t.index:e.end-t.end)[0]:void 0},this.getMeasurementOptions=te(()=>[this.options.count,this.options.paddingStart,this.options.scrollMargin,this.options.getItemKey,this.options.enabled],(e,t,a,i,n)=>(this.pendingMeasuredCacheIndexes=[],{count:e,paddingStart:t,scrollMargin:a,getItemKey:i,enabled:n}),{key:!1}),this.getMeasurements=te(()=>[this.getMeasurementOptions(),this.itemSizeCache],({count:e,paddingStart:t,scrollMargin:a,getItemKey:i,enabled:n},o)=>{if(!n)return this.measurementsCache=[],this.itemSizeCache.clear(),[];0===this.measurementsCache.length&&(this.measurementsCache=this.options.initialMeasurementsCache,this.measurementsCache.forEach(e=>{this.itemSizeCache.set(e.key,e.size)}));let l=this.pendingMeasuredCacheIndexes.length>0?Math.min(...this.pendingMeasuredCacheIndexes):0;this.pendingMeasuredCacheIndexes=[];let r=this.measurementsCache.slice(0,l);for(let n=l;n<e;n++){let e=i(n),l=1===this.options.lanes?r[n-1]:this.getFurthestMeasurement(r,n),s=l?l.end+this.options.gap:t+a,c=o.get(e),p="number"==typeof c?c:this.options.estimateSize(n),d=s+p,u=l?l.lane:n%this.options.lanes;r[n]={index:n,start:s,size:p,end:d,key:e,lane:u}}return this.measurementsCache=r,r},{key:!1,debug:()=>this.options.debug}),this.calculateRange=te(()=>[this.getMeasurements(),this.getSize(),this.getScrollOffset()],(e,t,a)=>this.range=e.length>0&&t>0?function({measurements:e,outerSize:t,scrollOffset:a}){let i=e.length-1,n=tm(0,i,t=>e[t].start,a),o=n;for(;o<i&&e[o].end<a+t;)o++;return{startIndex:n,endIndex:o}}({measurements:e,outerSize:t,scrollOffset:a}):null,{key:!1,debug:()=>this.options.debug}),this.getIndexes=te(()=>[this.options.rangeExtractor,this.calculateRange(),this.options.overscan,this.options.count],(e,t,a,i)=>null===t?[]:e({startIndex:t.startIndex,endIndex:t.endIndex,overscan:a,count:i}),{key:!1,debug:()=>this.options.debug}),this.indexFromElement=e=>{let t=this.options.indexAttribute,a=e.getAttribute(t);return a?parseInt(a,10):(console.warn(`Missing attribute name '${t}={index}' on measured element.`),-1)},this._measureElement=(e,t)=>{let a=this.indexFromElement(e),i=this.measurementsCache[a];if(!i)return;let n=i.key,o=this.elementsCache.get(n);o!==e&&(o&&this.observer.unobserve(o),this.observer.observe(e),this.elementsCache.set(n,e)),e.isConnected&&this.resizeItem(a,this.options.measureElement(e,t,this))},this.resizeItem=(e,t)=>{let a=this.measurementsCache[e];if(!a)return;let i=t-(this.itemSizeCache.get(a.key)??a.size);0!==i&&((void 0!==this.shouldAdjustScrollPositionOnItemSizeChange?this.shouldAdjustScrollPositionOnItemSizeChange(a,i,this):a.start<this.getScrollOffset()+this.scrollAdjustments)&&this._scrollToOffset(this.getScrollOffset(),{adjustments:this.scrollAdjustments+=i,behavior:void 0}),this.pendingMeasuredCacheIndexes.push(a.index),this.itemSizeCache=new Map(this.itemSizeCache.set(a.key,t)),this.notify(!1))},this.measureElement=e=>{if(!e){this.elementsCache.forEach((e,t)=>{e.isConnected||(this.observer.unobserve(e),this.elementsCache.delete(t))});return}this._measureElement(e,void 0)},this.getVirtualItems=te(()=>[this.getIndexes(),this.getMeasurements()],(e,t)=>{let a=[];for(let i=0,n=e.length;i<n;i++){let n=t[e[i]];a.push(n)}return a},{key:!1,debug:()=>this.options.debug}),this.getVirtualItemForOffset=e=>{let t=this.getMeasurements();if(0!==t.length)return tt(t[tm(0,t.length-1,e=>tt(t[e]).start,e)])},this.getOffsetForAlignment=(e,t)=>{let a=this.getSize(),i=this.getScrollOffset();"auto"===t&&e>=i+a&&(t="end"),"end"===t&&(e-=a);let n=this.options.horizontal?"scrollWidth":"scrollHeight";return Math.max(Math.min((this.scrollElement?"document"in this.scrollElement?this.scrollElement.document.documentElement[n]:this.scrollElement[n]:0)-a,e),0)},this.getOffsetForIndex=(e,t="auto")=>{e=Math.max(0,Math.min(e,this.options.count-1));let a=this.measurementsCache[e];if(!a)return;let i=this.getSize(),n=this.getScrollOffset();if("auto"===t){if(a.end>=n+i-this.options.scrollPaddingEnd)t="end";else{if(!(a.start<=n+this.options.scrollPaddingStart))return[n,t];t="start"}}let o=a.start-this.options.scrollPaddingStart+(a.size-i)/2;switch(t){case"center":return[this.getOffsetForAlignment(o,t),t];case"end":return[this.getOffsetForAlignment(a.end+this.options.scrollPaddingEnd,t),t];default:return[this.getOffsetForAlignment(a.start-this.options.scrollPaddingStart,t),t]}},this.isDynamicMode=()=>this.elementsCache.size>0,this.cancelScrollToIndex=()=>{null!==this.scrollToIndexTimeoutId&&this.targetWindow&&(this.targetWindow.clearTimeout(this.scrollToIndexTimeoutId),this.scrollToIndexTimeoutId=null)},this.scrollToOffset=(e,{align:t="start",behavior:a}={})=>{this.cancelScrollToIndex(),"smooth"===a&&this.isDynamicMode()&&console.warn("The `smooth` scroll behavior is not fully supported with dynamic size."),this._scrollToOffset(this.getOffsetForAlignment(e,t),{adjustments:void 0,behavior:a})},this.scrollToIndex=(e,{align:t="auto",behavior:a}={})=>{e=Math.max(0,Math.min(e,this.options.count-1)),this.cancelScrollToIndex(),"smooth"===a&&this.isDynamicMode()&&console.warn("The `smooth` scroll behavior is not fully supported with dynamic size.");let i=this.getOffsetForIndex(e,t);if(!i)return;let[n,o]=i;this._scrollToOffset(n,{adjustments:void 0,behavior:a}),"smooth"!==a&&this.isDynamicMode()&&this.targetWindow&&(this.scrollToIndexTimeoutId=this.targetWindow.setTimeout(()=>{if(this.scrollToIndexTimeoutId=null,this.elementsCache.has(this.options.getItemKey(e))){let[t]=tt(this.getOffsetForIndex(e,o));ta(t,this.getScrollOffset())||this.scrollToIndex(e,{align:o,behavior:a})}else this.scrollToIndex(e,{align:o,behavior:a})}))},this.scrollBy=(e,{behavior:t}={})=>{this.cancelScrollToIndex(),"smooth"===t&&this.isDynamicMode()&&console.warn("The `smooth` scroll behavior is not fully supported with dynamic size."),this._scrollToOffset(this.getScrollOffset()+e,{adjustments:void 0,behavior:t})},this.getTotalSize=()=>{var e;let t;let a=this.getMeasurements();return Math.max((0===a.length?this.options.paddingStart:1===this.options.lanes?(null==(e=a[a.length-1])?void 0:e.end)??0:Math.max(...a.slice(-this.options.lanes).map(e=>e.end)))-this.options.scrollMargin+this.options.paddingEnd,0)},this._scrollToOffset=(e,{adjustments:t,behavior:a})=>{this.options.scrollToFn(e,{behavior:a,adjustments:t},this)},this.measure=()=>{this.itemSizeCache=new Map,this.notify(!1)},this.setOptions(e)}}let tm=(e,t,a,i)=>{for(;e<=t;){let n=(e+t)/2|0,o=a(n);if(o<i)e=n+1;else{if(!(o>i))return n;t=n-1}}return e>0?e-1:0},tf="undefined"!=typeof document?c.useLayoutEffect:c.useEffect;var tg=a(41566),tv=(e,t)=>{let a=[];for(let i of e)"section"===i.type?a.push(([...i.childNodes].length+1)*t):a.push(t);return a},th=e=>{if(!e||void 0===e.scrollTop||void 0===e.clientHeight||void 0===e.scrollHeight)return{isTop:!1,isBottom:!1,isMiddle:!1};let t=0===e.scrollTop,a=Math.ceil(e.scrollTop+e.clientHeight)>=e.scrollHeight;return{isTop:t,isBottom:a,isMiddle:!t&&!a}},tx=e=>{let{Component:t,state:a,color:i,variant:n,itemClasses:l,getBaseProps:r,topContent:s,bottomContent:p,hideEmptyContent:d,hideSelectedIcon:m,shouldHighlightOnFocus:f,disableAnimation:g,getEmptyContentProps:x,getListProps:b,scrollShadowProps:y}=e,{virtualization:E}=e;if(!E||!(0,k.Im)(E)&&!E.maxListboxHeight&&!E.itemHeight)throw Error("You are using a virtualized listbox. VirtualizedListbox requires 'virtualization' props with 'maxListboxHeight' and 'itemHeight' properties. This error might have originated from autocomplete components that use VirtualizedListbox. Please provide these props to use the virtualized listbox.");let{maxListboxHeight:z,itemHeight:D}=E,P=Math.min(z,D*a.collection.size),C=(0,c.useRef)(null),S=(0,c.useMemo)(()=>tv([...a.collection],D),[a.collection,D]),O=function(e){let t=c.useReducer(()=>({}),{})[1],a={...e,onChange:(a,i)=>{var n;i?(0,e9.flushSync)(t):t(),null==(n=e.onChange)||n.call(e,a,i)}},[i]=c.useState(()=>new tu(a));return i.setOptions(a),tf(()=>i._didMount(),[]),tf(()=>i._willUpdate()),i}({observeElementRect:tl,observeElementOffset:tc,scrollToFn:td,count:[...a.collection].length,getScrollElement:()=>C.current,estimateSize:e=>S[e]}),M=O.getVirtualItems(),{getBaseProps:A}=function(e){var t;let[a,i]=(0,u.rE)(e,tg.Q.variantKeys),{ref:n,as:o,children:l,className:r,style:s,size:p=40,offset:d=0,visibility:m="auto",isEnabled:f=!0,onVisibilityChange:g,...h}=a,x=(0,v.zD)(n);!function(e={}){let{domRef:t,isEnabled:a=!0,overflowCheck:i="vertical",visibility:n="auto",offset:o=0,onVisibilityChange:l,updateDeps:r=[]}=e;(0,c.useRef)(n)}({domRef:x,offset:d,visibility:m,isEnabled:f,onVisibilityChange:g,updateDeps:[l],overflowCheck:null!=(t=e.orientation)?t:"vertical"});let b=(0,c.useMemo)(()=>(0,tg.Q)({...i,className:r}),[(0,w.t6)(i),r]);return{Component:o||"div",styles:b,domRef:x,children:l,getBaseProps:(t={})=>{var a;return{ref:x,className:b,"data-orientation":null!=(a=e.orientation)?a:"vertical",style:{"--scroll-shadow-size":`${p}px`,...s,...t.style},...h,...t}}}}({...y}),I=e=>{var t;let r=[...a.collection][e.index];if(!r)return null;let s={color:i,item:r,state:a,variant:n,disableAnimation:g,hideSelectedIcon:m,...r.props},c={position:"absolute",top:0,left:0,width:"100%",height:`${e.size}px`,transform:`translateY(${e.start}px)`};if("section"===r.type)return(0,o.jsx)(e7,{...s,itemClasses:l,style:{...c,...s.style}},r.key);let p=(0,o.jsx)(e5,{...s,classNames:(0,j.v)(l,null==(t=r.props)?void 0:t.classNames),shouldHighlightOnFocus:f,style:{...c,...s.style}},r.key);return r.wrapper&&(p=r.wrapper(p)),p},[F,R]=(0,c.useState)({isTop:!1,isBottom:!0,isMiddle:!1}),B=(0,o.jsxs)(t,{...b(),children:[!a.collection.size&&!d&&(0,o.jsx)("li",{children:(0,o.jsx)("div",{...x()})}),(0,o.jsx)("div",{...(0,h.$)(A()),ref:C,"data-bottom-scroll":F.isTop,"data-top-bottom-scroll":F.isMiddle,"data-top-scroll":F.isBottom,style:{height:z,overflow:"auto"},onScroll:e=>{R(th(e.target))},children:P>0&&D>0&&(0,o.jsx)("div",{style:{height:`${O.getTotalSize()}px`,width:"100%",position:"relative"},children:M.map(e=>I(e))})})]});return(0,o.jsxs)("div",{...r(),children:[s,B,p]})},tb=(0,u.Rf)(function(e,t){let{isVirtualized:a,...i}=e,n=function(e){var t;let a=(0,d.o)(),{ref:i,as:n,state:o,variant:l,color:r,onAction:s,children:p,onSelectionChange:u,disableAnimation:m=null!=(t=null==a?void 0:a.disableAnimation)&&t,itemClasses:f,className:g,topContent:x,bottomContent:b,emptyContent:w="No items.",hideSelectedIcon:k=!1,hideEmptyContent:E=!1,shouldHighlightOnFocus:D=!1,classNames:P,...C}=e,S=n||"ul",O="string"==typeof S,A=(0,v.zD)(i),I=(0,z.p)({...e,children:p,onSelectionChange:u}),F=o||I,{listBoxProps:R}=function(e,t,a){let i=(0,eM.$)(e,{labelable:!0}),n=e.selectionBehavior||"toggle",o=e.linkBehavior||("replace"===n?"action":"override");"toggle"===n&&"action"===o&&(o="override");let{listProps:l}=function(e){let{selectionManager:t,collection:a,disabledKeys:i,ref:n,keyboardDelegate:o,layoutDelegate:l}=e,r=M({usage:"search",sensitivity:"base"}),s=t.disabledBehavior,p=(0,c.useMemo)(()=>o||new eS({collection:a,disabledKeys:i,disabledBehavior:s,ref:n,collator:r,layoutDelegate:l}),[o,l,a,i,n,r,s]),{collectionProps:d}=(0,eV.y)({...e,ref:n,selectionManager:t,keyboardDelegate:p});return{listProps:d}}({...e,ref:a,selectionManager:t.selectionManager,collection:t.collection,disabledKeys:t.disabledKeys,linkBehavior:o}),{focusWithinProps:r}=(0,e_.R)({onFocusWithin:e.onFocus,onBlurWithin:e.onBlur,onFocusWithinChange:e.onFocusChange}),s=(0,eg.Bi)(e.id);e$.set(t,{id:s,shouldUseVirtualFocus:e.shouldUseVirtualFocus,shouldSelectOnPressUp:e.shouldSelectOnPressUp,shouldFocusOnHover:e.shouldFocusOnHover,isVirtualized:e.isVirtualized,onAction:e.onAction,linkBehavior:o});let{labelProps:p,fieldProps:d}=(0,eU.M)({...e,id:s,labelElementType:"span"});return{labelProps:p,listBoxProps:(0,j.v)(i,r,"multiple"===t.selectionManager.selectionMode?{"aria-multiselectable":"true"}:{},{role:"listbox",...(0,j.v)(d,l)})}}({...e,onAction:s},F,A),B=(0,c.useMemo)(()=>eG({className:g}),[g]),N=(0,y.$)(null==P?void 0:P.base,g);return{Component:S,state:F,variant:l,color:r,slots:B,classNames:P,topContent:x,bottomContent:b,emptyContent:w,hideEmptyContent:E,shouldHighlightOnFocus:D,hideSelectedIcon:k,disableAnimation:m,className:g,itemClasses:f,getBaseProps:(e={})=>({ref:A,"data-slot":"base",className:B.base({class:N}),...(0,h.$)(C,{enabled:O}),...e}),getListProps:(e={})=>({"data-slot":"list",className:B.list({class:null==P?void 0:P.list}),...R,...e}),getEmptyContentProps:(e={})=>({"data-slot":"empty-content",children:w,className:B.emptyContent({class:null==P?void 0:P.emptyContent}),...e})}}({...i,ref:t}),{Component:l,state:r,color:s,variant:p,itemClasses:u,getBaseProps:m,topContent:f,bottomContent:g,hideEmptyContent:x,hideSelectedIcon:b,shouldHighlightOnFocus:w,disableAnimation:k,getEmptyContentProps:E,getListProps:D}=n;if(a)return(0,o.jsx)(tx,{...e,...n});let P=(0,o.jsxs)(l,{...D(),children:[!r.collection.size&&!x&&(0,o.jsx)("li",{children:(0,o.jsx)("div",{...E()})}),[...r.collection].map(e=>{var t;let a={color:s,item:e,state:r,variant:p,disableAnimation:k,hideSelectedIcon:b,...e.props};if("section"===e.type)return(0,o.jsx)(e7,{...a,itemClasses:u},e.key);let i=(0,o.jsx)(e5,{...a,classNames:(0,j.v)(u,null==(t=e.props)?void 0:t.classNames),shouldHighlightOnFocus:w},e.key);return e.wrapper&&(i=e.wrapper(i)),i})]});return(0,o.jsxs)("div",{...m(),children:[f,P,g]})}),ty=a(97838),tw=a(50509),tk=a(31272);function tj(e,t){if(e.button>0)return!1;if(e.target){let t=e.target.ownerDocument;if(!t||!t.documentElement.contains(e.target)||e.target.closest("[data-react-aria-top-layer]"))return!1}return t.current&&!t.current.contains(e.target)}let tE=[];function tz(e,t=-1/0,a=1/0){return Math.min(Math.max(e,t),a)}let tD={top:"top",bottom:"top",left:"left",right:"left"},tP={top:"bottom",bottom:"top",left:"right",right:"left"},tC={top:"left",left:"top"},tS={top:"height",left:"width"},tO={width:"totalWidth",height:"totalHeight"},tM={},tA="undefined"!=typeof document?window.visualViewport:null;function tI(e){var t,a,i,n,o;let l=0,r=0,s=0,c=0,p=0,d=0,u={},m=(null!==(t=null==tA?void 0:tA.scale)&&void 0!==t?t:1)>1;if("BODY"===e.tagName){let t=document.documentElement;s=t.clientWidth,c=t.clientHeight,l=null!==(a=null==tA?void 0:tA.width)&&void 0!==a?a:s,r=null!==(i=null==tA?void 0:tA.height)&&void 0!==i?i:c,u.top=t.scrollTop||e.scrollTop,u.left=t.scrollLeft||e.scrollLeft,tA&&(p=tA.offsetTop,d=tA.offsetLeft)}else({width:l,height:r,top:p,left:d}=tT(e)),u.top=e.scrollTop,u.left=e.scrollLeft,s=l,c=r;return(0,eQ.Tc)()&&("BODY"===e.tagName||"HTML"===e.tagName)&&m&&(u.top=0,u.left=0,p=null!==(n=null==tA?void 0:tA.pageTop)&&void 0!==n?n:0,d=null!==(o=null==tA?void 0:tA.pageLeft)&&void 0!==o?o:0),{width:l,height:r,totalWidth:s,totalHeight:c,scroll:u,top:p,left:d}}function tF(e,t,a,i,n,o,l){var r;let s=null!==(r=n.scroll[e])&&void 0!==r?r:0,c=i[tS[e]],p=i.scroll[tD[e]]+o,d=c+i.scroll[tD[e]]-o,u=t-s+l[e]-i[tD[e]],m=t-s+a+l[e]-i[tD[e]];return u<p?p-u:m>d?Math.max(d-m,p-u):0}function tR(e){if(tM[e])return tM[e];let[t,a]=e.split(" "),i=tD[t]||"right",n=tC[i];tD[a]||(a="center");let o=tS[i],l=tS[n];return tM[e]={placement:t,crossPlacement:a,axis:i,crossAxis:n,size:o,crossSize:l},tM[e]}function tB(e,t,a,i,n,o,l,r,s,c){var p,d,u,m,f;let{placement:g,crossPlacement:v,axis:h,crossAxis:x,size:b,crossSize:y}=i,w={};w[x]=null!==(p=e[x])&&void 0!==p?p:0,"center"===v?w[x]+=((null!==(d=e[y])&&void 0!==d?d:0)-(null!==(u=a[y])&&void 0!==u?u:0))/2:v!==x&&(w[x]+=(null!==(m=e[y])&&void 0!==m?m:0)-(null!==(f=a[y])&&void 0!==f?f:0)),w[x]+=o;let k=e[x]-a[y]+s+c,j=e[x]+e[y]-s-c;if(w[x]=tz(w[x],k,j),g===h){let a=r?l[b]:t[tO[b]];w[tP[h]]=Math.floor(a-e[h]+n)}else w[h]=Math.floor(e[h]+e[b]+n);return w}function tN(e,t,a,i,n,o){var l,r,s;let{placement:c,axis:p,size:d}=o;return c===p?Math.max(0,a[p]-e[p]-(null!==(l=e.scroll[p])&&void 0!==l?l:0)+t[p]-(null!==(r=i[p])&&void 0!==r?r:0)-i[tP[p]]-n):Math.max(0,e[d]+e[p]+e.scroll[p]-t[p]-a[p]-a[d]-(null!==(s=i[p])&&void 0!==s?s:0)-i[tP[p]]-n)}function tT(e){let{top:t,left:a,width:i,height:n}=e.getBoundingClientRect(),{scrollTop:o,scrollLeft:l,clientTop:r,clientLeft:s}=document.documentElement;return{top:t+o-r,left:a+l-s,width:i,height:n}}function t_(e,t){let a,i=window.getComputedStyle(e);if("fixed"===i.position){let{top:t,left:i,width:n,height:o}=e.getBoundingClientRect();a={top:t,left:i,width:n,height:o}}else{a=tT(e);let i=tT(t),n=window.getComputedStyle(t);i.top+=(parseInt(n.borderTopWidth,10)||0)-t.scrollTop,i.left+=(parseInt(n.borderLeftWidth,10)||0)-t.scrollLeft,a.top-=i.top,a.left-=i.left}return a.top-=parseInt(i.marginTop,10)||0,a.left-=parseInt(i.marginLeft,10)||0,a}function tL(e){let t=window.getComputedStyle(e);return"none"!==t.transform||/transform|perspective/.test(t.willChange)||"none"!==t.filter||"paint"===t.contain||"backdropFilter"in t&&"none"!==t.backdropFilter||"WebkitBackdropFilter"in t&&"none"!==t.WebkitBackdropFilter}var tK=a(7717);function tW(e){let{ref:t,box:a,onResize:i}=e;(0,c.useEffect)(()=>{let e=null==t?void 0:t.current;if(e){if(void 0===window.ResizeObserver)return window.addEventListener("resize",i,!1),()=>{window.removeEventListener("resize",i,!1)};{let t=new window.ResizeObserver(e=>{e.length&&i()});return t.observe(e,{box:a}),()=>{e&&t.unobserve(e)}}}},[i,t,a])}let tq="undefined"!=typeof document?window.visualViewport:null;var tH=e=>{let t={top:{originY:1},bottom:{originY:0},left:{originX:1},right:{originX:0},"top-start":{originX:0,originY:1},"top-end":{originX:1,originY:1},"bottom-start":{originX:0,originY:0},"bottom-end":{originX:1,originY:0},"right-start":{originX:0,originY:0},"right-end":{originX:0,originY:1},"left-start":{originX:1,originY:0},"left-end":{originX:1,originY:1}};return(null==t?void 0:t[e])||{}},t$=e=>({top:"top",bottom:"bottom",left:"left",right:"right","top-start":"top start","top-end":"top end","bottom-start":"bottom start","bottom-end":"bottom end","left-start":"left top","left-end":"left bottom","right-start":"right top","right-end":"right bottom"})[e],tU=(e,t)=>{if(t.includes("-")){let[a]=t.split("-");if(a.includes(e))return!1}return!0},tV=(e,t)=>{if(t.includes("-")){let[,a]=t.split("-");return`${e}-${a}`}return e};function tG(e,t){let a=e;for((0,eC.o)(a,t)&&(a=a.parentElement);a&&!(0,eC.o)(a,t);)a=a.parentElement;return a||document.scrollingElement||document.documentElement}let tY="undefined"!=typeof document&&window.visualViewport,tX=new Set(["checkbox","radio","range","color","file","image","button","submit","reset"]),tZ=0;function tJ(e,t,a){let i=e.style[t];return e.style[t]=a,()=>{e.style[t]=i}}function tQ(e,t,a,i){return e.addEventListener(t,a,i),()=>{e.removeEventListener(t,a,i)}}function t0(e){let t=document.scrollingElement||document.documentElement,a=e;for(;a&&a!==t;){let e=tG(a);if(e!==document.documentElement&&e!==document.body&&e!==a){let t=e.getBoundingClientRect().top,i=a.getBoundingClientRect().top;i>t+a.clientHeight&&(e.scrollTop+=i-t)}a=e.parentElement}}function t1(e){return e instanceof HTMLInputElement&&!tX.has(e.type)||e instanceof HTMLTextAreaElement||e instanceof HTMLElement&&e.isContentEditable}new WeakMap;var t4=a(85044),t3=(0,m.tv)({slots:{base:["z-0","relative","bg-transparent","before:content-['']","before:hidden","before:z-[-1]","before:absolute","before:rotate-45","before:w-2.5","before:h-2.5","before:rounded-sm","data-[arrow=true]:before:block","data-[placement=top]:before:-bottom-[calc(theme(spacing.5)/4_-_1.5px)]","data-[placement=top]:before:left-1/2","data-[placement=top]:before:-translate-x-1/2","data-[placement=top-start]:before:-bottom-[calc(theme(spacing.5)/4_-_1.5px)]","data-[placement=top-start]:before:left-3","data-[placement=top-end]:before:-bottom-[calc(theme(spacing.5)/4_-_1.5px)]","data-[placement=top-end]:before:right-3","data-[placement=bottom]:before:-top-[calc(theme(spacing.5)/4_-_1.5px)]","data-[placement=bottom]:before:left-1/2","data-[placement=bottom]:before:-translate-x-1/2","data-[placement=bottom-start]:before:-top-[calc(theme(spacing.5)/4_-_1.5px)]","data-[placement=bottom-start]:before:left-3","data-[placement=bottom-end]:before:-top-[calc(theme(spacing.5)/4_-_1.5px)]","data-[placement=bottom-end]:before:right-3","data-[placement=left]:before:-right-[calc(theme(spacing.5)/4_-_2px)]","data-[placement=left]:before:top-1/2","data-[placement=left]:before:-translate-y-1/2","data-[placement=left-start]:before:-right-[calc(theme(spacing.5)/4_-_3px)]","data-[placement=left-start]:before:top-1/4","data-[placement=left-end]:before:-right-[calc(theme(spacing.5)/4_-_3px)]","data-[placement=left-end]:before:bottom-1/4","data-[placement=right]:before:-left-[calc(theme(spacing.5)/4_-_2px)]","data-[placement=right]:before:top-1/2","data-[placement=right]:before:-translate-y-1/2","data-[placement=right-start]:before:-left-[calc(theme(spacing.5)/4_-_3px)]","data-[placement=right-start]:before:top-1/4","data-[placement=right-end]:before:-left-[calc(theme(spacing.5)/4_-_3px)]","data-[placement=right-end]:before:bottom-1/4",...f.zb],content:["z-10","px-2.5","py-1","w-full","inline-flex","flex-col","items-center","justify-center","box-border","subpixel-antialiased","outline-none","box-border"],trigger:["z-10"],backdrop:["hidden"],arrow:[]},variants:{size:{sm:{content:"text-tiny"},md:{content:"text-small"},lg:{content:"text-medium"}},color:{default:{base:"before:bg-content1 before:shadow-small",content:"bg-content1"},foreground:{base:"before:bg-foreground",content:t4.k.solid.foreground},primary:{base:"before:bg-primary",content:t4.k.solid.primary},secondary:{base:"before:bg-secondary",content:t4.k.solid.secondary},success:{base:"before:bg-success",content:t4.k.solid.success},warning:{base:"before:bg-warning",content:t4.k.solid.warning},danger:{base:"before:bg-danger",content:t4.k.solid.danger}},radius:{none:{content:"rounded-none"},sm:{content:"rounded-small"},md:{content:"rounded-medium"},lg:{content:"rounded-large"},full:{content:"rounded-full"}},shadow:{sm:{content:"shadow-small"},md:{content:"shadow-medium"},lg:{content:"shadow-large"}},backdrop:{transparent:{},opaque:{backdrop:"bg-overlay/50 backdrop-opacity-disabled"},blur:{backdrop:"backdrop-blur-sm backdrop-saturate-150 bg-overlay/30"}},triggerScaleOnOpen:{true:{trigger:["aria-expanded:scale-[0.97]","aria-expanded:opacity-70","subpixel-antialiased"]},false:{}},disableAnimation:{true:{base:"animate-none"}},isTriggerDisabled:{true:{trigger:"opacity-disabled pointer-events-none"},false:{}}},defaultVariants:{color:"default",radius:"lg",size:"md",shadow:"md",backdrop:"transparent",triggerScaleOnOpen:!0},compoundVariants:[{backdrop:["opaque","blur"],class:{backdrop:"block w-full h-full fixed inset-0 -z-30"}}]});let t2=(0,c.createContext)({});var t5=a(87800);function t6({children:e}){let t=(0,c.useMemo)(()=>({register:()=>{}}),[]);return c.createElement(t5.F.Provider,{value:t},e)}let t8=c.createContext(null);function t7(e){var t;let a=(0,e2.wR)(),{portalContainer:i=a?null:document.body,isExiting:n}=e,[o,l]=(0,c.useState)(!1),r=(0,c.useMemo)(()=>({contain:o,setContain:l}),[o,l]),{getContainer:s}=null!==(t=(0,c.useContext)(t2))&&void 0!==t?t:{};if(!e.portalContainer&&s&&(i=s()),!i)return null;let p=e.children;return e.disableFocusManagement||(p=c.createElement(ty.n1,{restoreFocus:!0,contain:o&&!n},p)),p=c.createElement(t8.Provider,{value:r},c.createElement(t6,null,p)),e9.createPortal(p,i)}var t9={},ae={},at={},aa={},ai={},an={},ao={},al={},ar={},as={},ac={},ap={},ad={},au={},am={},af={},ag={},av={},ah={},ax={},ab={},ay={},aw={},ak={},aj={},aE={},az={},aD={},aP={},aC={},aS={},aO={},aM={},aA={},aI={};aI={"ar-AE":{dismiss:`\u{62A}\u{62C}\u{627}\u{647}\u{644}`},"bg-BG":{dismiss:`\u{41E}\u{442}\u{445}\u{432}\u{44A}\u{440}\u{43B}\u{44F}\u{43D}\u{435}`},"cs-CZ":{dismiss:"Odstranit"},"da-DK":{dismiss:"Luk"},"de-DE":{dismiss:`Schlie\xdfen`},"el-GR":{dismiss:`\u{391}\u{3C0}\u{3CC}\u{3C1}\u{3C1}\u{3B9}\u{3C8}\u{3B7}`},"en-US":{dismiss:"Dismiss"},"es-ES":{dismiss:"Descartar"},"et-EE":{dismiss:`L\xf5peta`},"fi-FI":{dismiss:`Hylk\xe4\xe4`},"fr-FR":{dismiss:"Rejeter"},"he-IL":{dismiss:`\u{5D4}\u{5EA}\u{5E2}\u{5DC}\u{5DD}`},"hr-HR":{dismiss:"Odbaci"},"hu-HU":{dismiss:`Elutas\xedt\xe1s`},"it-IT":{dismiss:"Ignora"},"ja-JP":{dismiss:`\u{9589}\u{3058}\u{308B}`},"ko-KR":{dismiss:`\u{BB34}\u{C2DC}`},"lt-LT":{dismiss:"Atmesti"},"lv-LV":{dismiss:`Ner\u{101}d\u{12B}t`},"nb-NO":{dismiss:"Lukk"},"nl-NL":{dismiss:"Negeren"},"pl-PL":{dismiss:"Zignoruj"},"pt-BR":{dismiss:"Descartar"},"pt-PT":{dismiss:"Dispensar"},"ro-RO":{dismiss:"Revocare"},"ru-RU":{dismiss:`\u{41F}\u{440}\u{43E}\u{43F}\u{443}\u{441}\u{442}\u{438}\u{442}\u{44C}`},"sk-SK":{dismiss:`Zru\u{161}i\u{165}`},"sl-SI":{dismiss:"Opusti"},"sr-SP":{dismiss:"Odbaci"},"sv-SE":{dismiss:"Avvisa"},"tr-TR":{dismiss:"Kapat"},"uk-UA":{dismiss:`\u{421}\u{43A}\u{430}\u{441}\u{443}\u{432}\u{430}\u{442}\u{438}`},"zh-CN":{dismiss:`\u{53D6}\u{6D88}`},"zh-TW":{dismiss:`\u{95DC}\u{9589}`}};var aF=a(37313);function aR(e){var t;let{onDismiss:a,...i}=e,n=ej((t=aI)&&t.__esModule?t.default:t,"@react-aria/overlays"),o=(0,aF.b)(i,n.format("dismiss"));return c.createElement(eW,null,c.createElement("button",{...o,tabIndex:-1,onClick:()=>{a&&a()},style:{width:1,height:1}}))}var aB=a(56757),aN=a(70079),aT={ease:[.36,.66,.4,1],easeIn:[.4,0,1,1],easeOut:[0,0,.2,1]};aT.easeOut,aT.easeIn;var a_={scaleSpring:{enter:{transform:"scale(1)",opacity:1,transition:{type:"spring",bounce:0,duration:.2}},exit:{transform:"scale(0.85)",opacity:0,transition:{type:"easeOut",duration:.15}}},scaleSpringOpacity:{initial:{opacity:0,transform:"scale(0.8)"},enter:{opacity:1,transform:"scale(1)",transition:{type:"spring",bounce:0,duration:.3}},exit:{opacity:0,transform:"scale(0.96)",transition:{type:"easeOut",bounce:0,duration:.15}}},scale:{enter:{scale:1},exit:{scale:.95}},scaleFadeIn:{enter:{transform:"scale(1)",opacity:1,transition:{duration:.25,ease:aT.easeIn}},exit:{transform:"scale(0.95)",opacity:0,transition:{duration:.2,ease:aT.easeOut}}},scaleInOut:{enter:{transform:"scale(1)",opacity:1,transition:{duration:.4,ease:aT.ease}},exit:{transform:"scale(1.03)",opacity:0,transition:{duration:.3,ease:aT.ease}}},fade:{enter:{opacity:1,transition:{duration:.4,ease:aT.ease}},exit:{opacity:0,transition:{duration:.3,ease:aT.ease}}},collapse:{enter:{opacity:1,height:"auto",transition:{height:{type:"spring",bounce:0,duration:.3},opacity:{easings:"ease",duration:.4}}},exit:{opacity:0,height:0,transition:{easings:"ease",duration:.3}}}},aL=a(43006),aK=()=>a.e(11).then(a.bind(a,70392)).then(e=>e.default),aW=(0,u.Rf)(({children:e,motionProps:t,placement:a,disableAnimation:i,style:n={},transformOrigin:l={},...r},s)=>{let c=n;return void 0!==l.originX||void 0!==l.originY?c={...c,transformOrigin:l}:a&&(c={...c,...tH("center"===a?"top":a)}),i?(0,o.jsx)("div",{...r,ref:s,children:e}):(0,o.jsx)(aB.F,{features:aK,children:(0,o.jsx)(aN.m.div,{ref:s,animate:"enter",exit:"exit",initial:"initial",style:c,variants:a_.scaleSpringOpacity,...(0,j.v)(r,t),children:e})})});aW.displayName="NextUI.FreeSoloPopoverWrapper";var aq=(0,u.Rf)(({children:e,transformOrigin:t,disableDialogFocus:a=!1,...n},l)=>{let{Component:r,state:s,placement:p,backdrop:m,portalContainer:f,disableAnimation:g,motionProps:h,isNonModal:x,getPopoverProps:E,getBackdropProps:z,getDialogProps:D,getContentProps:C}=function(e){var t,a,n;let o=(0,d.o)(),[l,r]=(0,u.rE)(e,t3.variantKeys),{as:s,ref:p,children:m,state:f,triggerRef:g,scrollRef:h,defaultOpen:x,onOpenChange:E,isOpen:z,isNonModal:D=!0,shouldFlip:C=!0,containerPadding:O=12,shouldBlockScroll:M=!1,isDismissable:A=!0,shouldCloseOnBlur:I,portalContainer:F,updatePositionDeps:R,dialogProps:B,placement:N="top",triggerType:T="dialog",showArrow:_=!1,offset:L=7,crossOffset:K=0,boundaryElement:W,isKeyboardDismissDisabled:q,shouldCloseOnInteractOutside:H,shouldCloseOnScroll:$,motionProps:U,className:V,classNames:G,onClose:Y,...X}=l,Z=(0,v.zD)(p),J=(0,c.useRef)(null),Q=(0,c.useRef)(!1),ee=g||J,et=null!=(a=null!=(t=e.disableAnimation)?t:null==o?void 0:o.disableAnimation)&&a,ea=P({isOpen:z,defaultOpen:x,onOpenChange:e=>{null==E||E(e),e||null==Y||Y()}}),ei=f||ea,{popoverProps:en,underlayProps:eo,placement:el}=function(e,t){let{triggerRef:a,popoverRef:i,showArrow:n,offset:o=7,crossOffset:l=0,scrollRef:r,shouldFlip:s,boundaryElement:p,isDismissable:d=!0,shouldCloseOnBlur:u=!0,shouldCloseOnScroll:m=!0,placement:f="top",containerPadding:g,shouldCloseOnInteractOutside:v,isNonModal:h,isKeyboardDismissDisabled:x,updatePositionDeps:b=[],...y}=e,{overlayProps:w,underlayProps:k}=function(e,t){let{onClose:a,shouldCloseOnBlur:i,isOpen:n,isDismissable:o=!1,isKeyboardDismissDisabled:l=!1,shouldCloseOnInteractOutside:r}=e;(0,c.useEffect)(()=>(n&&tE.push(t),()=>{let e=tE.indexOf(t);e>=0&&tE.splice(e,1)}),[n,t]);let s=()=>{tE[tE.length-1]===t&&a&&a()};!function(e){let{ref:t,onInteractOutside:a,isDisabled:i,onInteractOutsideStart:n}=e,o=(0,c.useRef)({isPointerDown:!1,ignoreEmulatedMouseEvents:!1}),l=(0,tw.J)(e=>{a&&tj(e,t)&&(n&&n(e),o.current.isPointerDown=!0)}),r=(0,tw.J)(e=>{a&&a(e)});(0,c.useEffect)(()=>{let e=o.current;if(i)return;let a=t.current,n=(0,tk.T)(a);if("undefined"!=typeof PointerEvent){let a=a=>{e.isPointerDown&&tj(a,t)&&r(a),e.isPointerDown=!1};return n.addEventListener("pointerdown",l,!0),n.addEventListener("pointerup",a,!0),()=>{n.removeEventListener("pointerdown",l,!0),n.removeEventListener("pointerup",a,!0)}}{let a=a=>{e.ignoreEmulatedMouseEvents?e.ignoreEmulatedMouseEvents=!1:e.isPointerDown&&tj(a,t)&&r(a),e.isPointerDown=!1},i=a=>{e.ignoreEmulatedMouseEvents=!0,e.isPointerDown&&tj(a,t)&&r(a),e.isPointerDown=!1};return n.addEventListener("mousedown",l,!0),n.addEventListener("mouseup",a,!0),n.addEventListener("touchstart",l,!0),n.addEventListener("touchend",i,!0),()=>{n.removeEventListener("mousedown",l,!0),n.removeEventListener("mouseup",a,!0),n.removeEventListener("touchstart",l,!0),n.removeEventListener("touchend",i,!0)}}},[t,i,l,r])}({ref:t,onInteractOutside:o&&n?e=>{(!r||r(e.target))&&(tE[tE.length-1]===t&&(e.stopPropagation(),e.preventDefault()),s())}:void 0,onInteractOutsideStart:e=>{(!r||r(e.target))&&tE[tE.length-1]===t&&(e.stopPropagation(),e.preventDefault())}});let{focusWithinProps:p}=(0,e_.R)({isDisabled:!i,onBlurWithin:e=>{!(!e.relatedTarget||(0,ty.Pu)(e.relatedTarget))&&(!r||r(e.relatedTarget))&&(null==a||a())}});return{overlayProps:{onKeyDown:e=>{"Escape"!==e.key||l||e.nativeEvent.isComposing||(e.stopPropagation(),e.preventDefault(),s())},...p},underlayProps:{onPointerDown:e=>{e.target===e.currentTarget&&e.preventDefault()}}}}({isOpen:t.isOpen,onClose:t.close,shouldCloseOnBlur:u,isDismissable:d,isKeyboardDismissDisabled:x,shouldCloseOnInteractOutside:v||(e=>eF(e,a,t))},i),{overlayProps:E,arrowProps:z,placement:D,updatePosition:P}=function(e){var t,a,i;let{direction:n}=(0,S.Y)(),{arrowSize:o=0,targetRef:l,overlayRef:r,scrollRef:s=r,placement:p="bottom",containerPadding:d=12,shouldFlip:u=!0,boundaryElement:m="undefined"!=typeof document?document.body:null,offset:f=0,crossOffset:g=0,shouldUpdatePosition:v=!0,isOpen:h=!0,onClose:x,maxHeight:b,arrowBoundaryOffset:y=0}=e,[w,k]=(0,c.useState)(null),j=[v,p,r.current,l.current,s.current,d,u,m,f,g,h,n,b,y,o],E=(0,c.useRef)(null==tq?void 0:tq.scale);(0,c.useEffect)(()=>{h&&(E.current=null==tq?void 0:tq.scale)},[h]);let z=(0,c.useCallback)(()=>{var e,t,a,i,c,x;if(!1===v||!h||!r.current||!l.current||!m||(null==tq?void 0:tq.scale)!==E.current)return;let w=null;if(s.current&&s.current.contains(document.activeElement)){let i=null===(e=document.activeElement)||void 0===e?void 0:e.getBoundingClientRect(),n=s.current.getBoundingClientRect();(w={type:"top",offset:(null!==(t=null==i?void 0:i.top)&&void 0!==t?t:0)-n.top}).offset>n.height/2&&(w.type="bottom",w.offset=(null!==(a=null==i?void 0:i.bottom)&&void 0!==a?a:0)-n.bottom)}let j=r.current;!b&&r.current&&(j.style.top="0px",j.style.bottom="",j.style.maxHeight=(null!==(c=null===(i=window.visualViewport)||void 0===i?void 0:i.height)&&void 0!==c?c:window.innerHeight)+"px");let z=function(e){var t,a,i,n;let o;let{placement:l,targetNode:r,overlayNode:s,scrollNode:c,padding:p,shouldFlip:d,boundaryElement:u,offset:m,crossOffset:f,maxHeight:g,arrowSize:v=0,arrowBoundaryOffset:h=0}=e,x=s instanceof HTMLElement?function(e){let t=e.offsetParent;if(t&&t===document.body&&"static"===window.getComputedStyle(t).position&&!tL(t)&&(t=document.documentElement),null==t)for(t=e.parentElement;t&&!tL(t);)t=t.parentElement;return t||document.documentElement}(s):document.documentElement,b=x===document.documentElement,y=window.getComputedStyle(x).position,w=b?tT(r):t_(r,x);if(!b){let{marginTop:e,marginLeft:t}=window.getComputedStyle(r);w.top+=parseInt(e,10)||0,w.left+=parseInt(t,10)||0}let k=tT(s),j={top:parseInt((o=window.getComputedStyle(s)).marginTop,10)||0,bottom:parseInt(o.marginBottom,10)||0,left:parseInt(o.marginLeft,10)||0,right:parseInt(o.marginRight,10)||0};k.width+=(null!==(t=j.left)&&void 0!==t?t:0)+(null!==(a=j.right)&&void 0!==a?a:0),k.height+=(null!==(i=j.top)&&void 0!==i?i:0)+(null!==(n=j.bottom)&&void 0!==n?n:0);let E={top:c.scrollTop,left:c.scrollLeft,width:c.scrollWidth,height:c.scrollHeight},z=tI(u),D=tI(x),P="BODY"===u.tagName?tT(x):t_(x,u);return"HTML"===x.tagName&&"BODY"===u.tagName&&(D.scroll.top=0,D.scroll.left=0),function(e,t,a,i,n,o,l,r,s,c,p,d,u,m,f,g){var v,h,x,b;let y=tR(e),{size:w,crossAxis:k,crossSize:j,placement:E,crossPlacement:z}=y,D=tB(t,r,a,y,p,d,c,u,f,g),P=p,C=tN(r,c,t,n,o+p,y);if(l&&i[w]>C){let e=tR(`${tP[E]} ${z}`),i=tB(t,r,a,e,p,d,c,u,f,g);tN(r,c,t,n,o+p,e)>C&&(y=e,D=i,P=p)}let S="bottom";"top"===y.axis?"top"===y.placement?S="top":"bottom"===y.placement&&(S="bottom"):"top"===y.crossAxis&&("top"===y.crossPlacement?S="bottom":"bottom"===y.crossPlacement&&(S="top"));let O=tF(k,D[k],a[j],r,s,o,c);D[k]+=O;let M=function(e,t,a,i,n,o,l,r){var s,c,p,d,u,m,f;let g=i?a.height:t[tO.height],v=null!=e.top?a.top+e.top:a.top+(g-(null!==(s=e.bottom)&&void 0!==s?s:0)-l),h="top"!==r?Math.max(0,t.height+t.top+(null!==(c=t.scroll.top)&&void 0!==c?c:0)-v-((null!==(p=n.top)&&void 0!==p?p:0)+(null!==(d=n.bottom)&&void 0!==d?d:0)+o)):Math.max(0,v+l-(t.top+(null!==(u=t.scroll.top)&&void 0!==u?u:0))-((null!==(m=n.top)&&void 0!==m?m:0)+(null!==(f=n.bottom)&&void 0!==f?f:0)+o));return Math.min(t.height-2*o,h)}(D,r,c,u,n,o,a.height,S);m&&m<M&&(M=m),a.height=Math.min(a.height,M),O=tF(k,(D=tB(t,r,a,y,P,d,c,u,f,g))[k],a[j],r,s,o,c),D[k]+=O;let A={},I=t[k]+.5*t[j]-D[k]-n[tD[k]],F=f/2+g,R="left"===tD[k]?(null!==(v=n.left)&&void 0!==v?v:0)+(null!==(h=n.right)&&void 0!==h?h:0):(null!==(x=n.top)&&void 0!==x?x:0)+(null!==(b=n.bottom)&&void 0!==b?b:0),B=a[j]-R-f/2-g,N=tz(I,t[k]+f/2-(D[k]+n[tD[k]]),t[k]+t[j]-f/2-(D[k]+n[tD[k]]));return A[k]=tz(N,F,B),{position:D,maxHeight:M,arrowOffsetLeft:A.left,arrowOffsetTop:A.top,placement:y.placement}}(l,w,k,E,j,p,d,z,D,P,m,f,!!y&&"static"!==y,g,v,h)}({placement:(x=p,"rtl"===n?x.replace("start","right").replace("end","left"):x.replace("start","left").replace("end","right")),overlayNode:r.current,targetNode:l.current,scrollNode:s.current||r.current,padding:d,shouldFlip:u,boundaryElement:m,offset:f,crossOffset:g,maxHeight:b,arrowSize:o,arrowBoundaryOffset:y});if(z.position){if(j.style.top="",j.style.bottom="",j.style.left="",j.style.right="",Object.keys(z.position).forEach(e=>j.style[e]=z.position[e]+"px"),j.style.maxHeight=null!=z.maxHeight?z.maxHeight+"px":"",w&&document.activeElement&&s.current){let e=document.activeElement.getBoundingClientRect(),t=s.current.getBoundingClientRect(),a=e[w.type]-t[w.type];s.current.scrollTop+=a-w.offset}k(z)}},j);(0,tK.N)(z,j),i=z,(0,tK.N)(()=>(window.addEventListener("resize",i,!1),()=>{window.removeEventListener("resize",i,!1)}),[i]),tW({ref:r,onResize:z}),tW({ref:l,onResize:z});let D=(0,c.useRef)(!1);(0,tK.N)(()=>{let e;let t=()=>{D.current=!0,clearTimeout(e),e=setTimeout(()=>{D.current=!1},500),z()},a=()=>{D.current&&t()};return null==tq||tq.addEventListener("resize",t),null==tq||tq.addEventListener("scroll",a),()=>{null==tq||tq.removeEventListener("resize",t),null==tq||tq.removeEventListener("scroll",a)}},[z]);let P=(0,c.useCallback)(()=>{D.current||null==x||x()},[x,D]);return function(e){let{triggerRef:t,isOpen:a,onClose:i}=e;(0,c.useEffect)(()=>{if(!a||null===i)return;let e=e=>{let a=e.target;if(!t.current||a instanceof Node&&!a.contains(t.current)||e.target instanceof HTMLInputElement||e.target instanceof HTMLTextAreaElement)return;let n=i||ez.get(t.current);n&&n()};return window.addEventListener("scroll",e,!0),()=>{window.removeEventListener("scroll",e,!0)}},[a,i,t])}({triggerRef:l,isOpen:h,onClose:x&&P}),{overlayProps:{style:{position:"absolute",zIndex:1e5,...null==w?void 0:w.position,maxHeight:null!==(t=null==w?void 0:w.maxHeight)&&void 0!==t?t:"100vh"}},placement:null!==(a=null==w?void 0:w.placement)&&void 0!==a?a:null,arrowProps:{"aria-hidden":"true",role:"presentation",style:{left:null==w?void 0:w.arrowOffsetLeft,top:null==w?void 0:w.arrowOffsetTop}},updatePosition:z}}({...y,shouldFlip:s,crossOffset:l,targetRef:a,overlayRef:i,isOpen:t.isOpen,scrollRef:r,boundaryElement:p,containerPadding:g,placement:t$(f),offset:n?o+3:o,onClose:(null==h||h)&&m?t.close:()=>{}});return(0,eI.U)(()=>{b.length&&P()},b),{popoverProps:(0,j.v)(w,E),arrowProps:z,underlayProps:k,placement:D}}({triggerRef:ee,isNonModal:D,popoverRef:Z,placement:N,offset:L,scrollRef:h,isDismissable:A,shouldCloseOnBlur:I,boundaryElement:W,crossOffset:K,shouldFlip:C,containerPadding:O,updatePositionDeps:R,isKeyboardDismissDisabled:q,shouldCloseOnScroll:$,shouldCloseOnInteractOutside:H},ei),er=(0,c.useMemo)(()=>el?tU(el,N)?el:N:null,[el,N]),{triggerProps:es}=eD({type:T},ei,ee),{isFocusVisible:ec,isFocused:ep,focusProps:ed}=(0,b.o)(),eu=(0,c.useMemo)(()=>t3({...r}),[(0,w.t6)(r)]),em=(0,y.$)(null==G?void 0:G.base,V);!function(e={}){let{isDisabled:t}=e;(0,tK.N)(()=>{if(!t){let e,t,a,n,o;return 1==++tZ&&(i=(0,eQ.un)()?(a=null,n=()=>{if(a)return;let e=window.pageXOffset,t=window.pageYOffset;a=(0,eA.c)(tQ(window,"scroll",()=>{window.scrollTo(0,0)}),tJ(document.documentElement,"paddingRight",`${window.innerWidth-document.documentElement.clientWidth}px`),tJ(document.documentElement,"overflow","hidden"),tJ(document.body,"marginTop",`-${t}px`),()=>{window.scrollTo(e,t)}),window.scrollTo(0,0)},o=(0,eA.c)(tQ(document,"touchstart",a=>{((e=tG(a.target,!0))!==document.documentElement||e!==document.body)&&e instanceof HTMLElement&&"auto"===window.getComputedStyle(e).overscrollBehavior&&(t=tJ(e,"overscrollBehavior","contain"))},{passive:!1,capture:!0}),tQ(document,"touchmove",t=>{if(!e||e===document.documentElement||e===document.body){t.preventDefault();return}e.scrollHeight===e.clientHeight&&e.scrollWidth===e.clientWidth&&t.preventDefault()},{passive:!1,capture:!0}),tQ(document,"touchend",e=>{let a=e.target;t1(a)&&a!==document.activeElement&&(e.preventDefault(),n(),a.style.transform="translateY(-2000px)",a.focus(),requestAnimationFrame(()=>{a.style.transform=""})),t&&t()},{passive:!1,capture:!0}),tQ(document,"focus",e=>{let t=e.target;t1(t)&&(n(),t.style.transform="translateY(-2000px)",requestAnimationFrame(()=>{t.style.transform="",tY&&(tY.height<window.innerHeight?requestAnimationFrame(()=>{t0(t)}):tY.addEventListener("resize",()=>t0(t),{once:!0}))}))},!0)),()=>{null==t||t(),null==a||a(),o()}):(0,eA.c)(tJ(document.documentElement,"paddingRight",`${window.innerWidth-document.documentElement.clientWidth}px`),tJ(document.documentElement,"overflow","hidden"))),()=>{0==--tZ&&i()}}},[t])}({isDisabled:!(M&&ei.isOpen)});let ef=(0,c.useCallback)((e={})=>({"data-slot":"content","data-open":(0,k.sE)(ei.isOpen),"data-arrow":(0,k.sE)(_),"data-placement":el?tV(el,N):void 0,className:eu.content({class:(0,y.$)(null==G?void 0:G.content,e.className)})}),[eu,ei.isOpen,_,er,N,G,el]),eg=(0,c.useCallback)(t=>{var a;let i;return"touch"===t.pointerType&&((null==e?void 0:e.backdrop)==="blur"||(null==e?void 0:e.backdrop)==="opaque")?i=setTimeout(()=>{Q.current=!0},100):Q.current=!0,null==(a=es.onPress)||a.call(es,t),()=>{clearTimeout(i)}},[null==es?void 0:es.onPress]),ev=(0,c.useCallback)((e={},t=null)=>{let{isDisabled:a,...i}=e;return{"data-slot":"trigger",...(0,j.v)({"aria-haspopup":"dialog"},es,i),onPress:eg,isDisabled:a,className:eu.trigger({class:(0,y.$)(null==G?void 0:G.trigger,e.className),isTriggerDisabled:a}),ref:function(...e){return 1===e.length&&e[0]?e[0]:t=>{for(let a of e)"function"==typeof a?a(t):null!=a&&(a.current=t)}}(t,ee)}},[ei,es,eg,ee]),eh=(0,c.useCallback)((e={})=>({"data-slot":"backdrop",className:eu.backdrop({class:null==G?void 0:G.backdrop}),onClick:e=>{if(!Q.current){e.preventDefault();return}ei.close(),Q.current=!1},...eo,...e}),[eu,ei.isOpen,G,eo]);return{state:ei,Component:s||"div",children:m,classNames:G,showArrow:_,triggerRef:ee,placement:er,isNonModal:D,popoverRef:Z,portalContainer:F,isOpen:ei.isOpen,onClose:ei.close,disableAnimation:et,shouldBlockScroll:M,backdrop:null!=(n=e.backdrop)?n:"transparent",motionProps:U,getBackdropProps:eh,getPopoverProps:(e={})=>({ref:Z,...(0,j.v)(en,X,e),style:(0,j.v)(en.style,X.style,e.style)}),getTriggerProps:ev,getDialogProps:(e={})=>({"data-slot":"base","data-open":(0,k.sE)(ei.isOpen),"data-focus":(0,k.sE)(ep),"data-arrow":(0,k.sE)(_),"data-focus-visible":(0,k.sE)(ec),"data-placement":el?tV(el,N):void 0,...(0,j.v)(ed,B,e),className:eu.base({class:(0,y.$)(em)}),style:{outline:"none"}}),getContentProps:ef}}({...n,ref:l}),O=c.useRef(null),{dialogProps:M,titleProps:A}=function(e,t){let a,i,{role:n="dialog"}=e,o=(0,eg.X1)();o=e["aria-label"]?void 0:o;let l=(0,c.useRef)(!1);return(0,c.useEffect)(()=>{if(t.current&&!t.current.contains(document.activeElement)){(0,aL.l)(t.current);let e=setTimeout(()=>{document.activeElement===t.current&&(l.current=!0,t.current&&(t.current.blur(),(0,aL.l)(t.current)),l.current=!1)},500);return()=>{clearTimeout(e)}}},[t]),i=null==(a=(0,c.useContext)(t8))?void 0:a.setContain,(0,tK.N)(()=>{null==i||i(!0)},[i]),{dialogProps:{...(0,eM.$)(e,{labelable:!0}),role:n,tabIndex:-1,"aria-labelledby":e["aria-labelledby"]||o,onBlur:e=>{l.current&&e.stopPropagation()}},titleProps:{id:o}}}({},O),I=D({...!a&&{ref:O},...M}),F=c.useMemo(()=>"transparent"===m?null:g?(0,o.jsx)("div",{...z()}):(0,o.jsx)(aB.F,{features:aK,children:(0,o.jsx)(aN.m.div,{animate:"enter",exit:"exit",initial:"exit",variants:a_.fade,...z()})}),[m,g,z]);return(0,o.jsxs)(t7,{portalContainer:f,children:[!x&&F,(0,o.jsx)(r,{...E(),children:(0,o.jsxs)(aW,{disableAnimation:g,motionProps:h,placement:p,tabIndex:-1,transformOrigin:t,...I,children:[!x&&(0,o.jsx)(aR,{onDismiss:s.close}),(0,o.jsx)("div",{...C(),children:"function"==typeof e?e(A):e}),(0,o.jsx)(aR,{onDismiss:s.close})]})})]})});aq.displayName="NextUI.FreeSoloPopover";var aH=({strokeWidth:e=1.5,...t})=>(0,o.jsx)("svg",{"aria-hidden":"true",fill:"none",focusable:"false",height:"1em",role:"presentation",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:e,viewBox:"0 0 24 24",width:"1em",...t,children:(0,o.jsx)("path",{d:"m6 9 6 6 6-6"})}),a$=a(63227),aU=a(79293),aV=a(88920),aG=(0,u.Rf)(function(e,t){let{Component:a,state:i,label:n,hasHelper:l,isLoading:r,triggerRef:s,selectorIcon:p=(0,o.jsx)(aH,{}),description:m,errorMessage:f,isInvalid:D,startContent:S,endContent:O,placeholder:F,renderValue:R,shouldLabelBeOutside:B,disableAnimation:N,getBaseProps:T,getLabelProps:_,getTriggerProps:L,getValueProps:K,getListboxProps:W,getPopoverProps:q,getSpinnerProps:H,getMainWrapperProps:$,getInnerWrapperProps:U,getHiddenSelectProps:V,getHelperWrapperProps:G,getListboxWrapperProps:Y,getDescriptionProps:X,getErrorMessageProps:Z,getSelectorIconProps:J}=function(e){var t,a,i,n,o,l;let r=(0,d.o)(),{validationBehavior:s}=(0,eR.CC)(eB.c)||{},[p,m]=(0,u.rE)(e,g.variantKeys),f=null!=(a=null!=(t=e.disableAnimation)?t:null==r?void 0:r.disableAnimation)&&a,{ref:D,as:S,label:O,name:F,isLoading:R,selectorIcon:B,isOpen:N,defaultOpen:T,onOpenChange:_,startContent:L,endContent:K,description:W,renderValue:q,onSelectionChange:H,placeholder:$,isVirtualized:U,itemHeight:V=36,maxListboxHeight:G=256,children:Y,disallowEmptySelection:X=!1,selectionMode:Z="single",spinnerRef:J,scrollRef:Q,popoverProps:ee={},scrollShadowProps:et={},listboxProps:ea={},spinnerProps:ei={},validationState:en,onChange:eo,onClose:el,className:er,classNames:es,validationBehavior:ec=null!=(i=null!=s?s:null==r?void 0:r.validationBehavior)?i:"native",hideEmptyContent:ep=!1,...ed}=p,eu=(0,v.zD)(Q),em={popoverProps:(0,j.v)({placement:"bottom",triggerScaleOnOpen:!1,offset:5,disableAnimation:f},ee),scrollShadowProps:(0,j.v)({ref:eu,isEnabled:null==(n=e.showScrollIndicators)||n,hideScrollBar:!0,offset:15},et),listboxProps:(0,j.v)({disableAnimation:f},ea)},ev=S||"button",eh="string"==typeof ev,ex=(0,v.zD)(D),eb=(0,c.useRef)(null),ey=(0,c.useRef)(null),ew=(0,c.useRef)(null),ek=function({validate:e,validationBehavior:t,...a}){let[i,n]=(0,c.useState)(!1),[o,l]=(0,c.useState)(null),r=function(e){let t=P(e),[a,i]=(0,c.useState)(null),[n,o]=(0,c.useState)([]),l=()=>{o([]),t.close()};return{focusStrategy:a,...t,open(e=null){i(e),t.open()},toggle(e=null){i(e),t.toggle()},close(){l()},expandedKeysStack:n,openSubmenu:(e,t)=>{o(a=>t>a.length?a:[...a.slice(0,t),e])},closeSubmenu:(e,t)=>{o(a=>a[t]===e?a.slice(0,t):a)}}}(a),s=function(e){let{collection:t,disabledKeys:a,selectionManager:i,selectionManager:{setSelectedKeys:n,selectedKeys:o,selectionMode:l}}=(0,z.p)(e),r=(0,c.useMemo)(()=>e.isLoading||0===o.size?[]:Array.from(o).filter(Boolean).filter(e=>!t.getItem(e)),[o,t]),s=0!==o.size?Array.from(o).map(e=>t.getItem(e)).filter(Boolean):null;return r.length&&console.warn(`Select: Keys "${r.join(", ")}" passed to "selectedKeys" are not present in the collection.`),{collection:t,disabledKeys:a,selectionManager:i,selectionMode:l,selectedKeys:o,setSelectedKeys:n.bind(i),selectedItems:s}}({...a,onSelectionChange:e=>{null!=a.onSelectionChange&&("all"===e?a.onSelectionChange(new Set(s.collection.getKeys())):a.onSelectionChange(e)),"single"===a.selectionMode&&r.close()}}),p=(0,C.KZ)({...a,validationBehavior:t,validate:t=>{if(!e)return;let i=Array.from(t);return e("single"===a.selectionMode?i[0]:i)},value:s.selectedKeys}),d=0===s.collection.size&&a.hideEmptyContent;return{...p,...s,...r,focusStrategy:o,close(){r.close()},open(e=null){d||(l(e),r.open())},toggle(e=null){d||(l(e),r.toggle())},isFocused:i,setFocused:n}}({...p,isOpen:N,selectionMode:Z,disallowEmptySelection:X,validationBehavior:ec,children:Y,isRequired:e.isRequired,isDisabled:e.isDisabled,isInvalid:e.isInvalid,defaultOpen:T,hideEmptyContent:ep,onOpenChange:e=>{null==_||_(e),e||null==el||el()},onSelectionChange:e=>{null==H||H(e),eo&&"function"==typeof eo&&eo({target:{...ex.current&&{...ex.current,name:ex.current.name},value:Array.from(e).join(",")}}),ek.commitValidation()}});ek={...ek,...e.isDisabled&&{disabledKeys:new Set([...ek.collection.getKeys()])}},(0,eI.U)(()=>{var e;(null==(e=ex.current)?void 0:e.value)&&ek.setSelectedKeys(new Set([...ek.selectedKeys,ex.current.value]))},[ex.current]);let{labelProps:ez,triggerProps:eP,valueProps:eC,menuProps:eT,descriptionProps:e_,errorMessageProps:eL,isInvalid:eK,validationErrors:eW,validationDetails:eq}=function(e,t,a){let{disallowEmptySelection:i,isDisabled:n}=e,o=M({usage:"search",sensitivity:"base"}),l=(0,c.useMemo)(()=>new eS(t.collection,t.disabledKeys,null,o),[t.collection,t.disabledKeys,o]),{menuTriggerProps:r,menuProps:s}=function(e,t,a){var i;let{type:n="menu",isDisabled:o,trigger:l="press"}=e,r=(0,eg.Bi)(),{triggerProps:s,overlayProps:c}=eD({type:n},t,a),p=ej((i=ef)&&i.__esModule?i.default:i,"@react-aria/menu"),{longPressProps:d}=(0,eE.H)({isDisabled:o||"longPress"!==l,accessibilityDescription:p.format("longPressMessage"),onLongPressStart(){t.close()},onLongPress(){t.open("first")}});return delete s.onPress,{menuTriggerProps:{...s,..."press"===l?{onPressStart(e){"touch"===e.pointerType||"keyboard"===e.pointerType||o||t.open("virtual"===e.pointerType?"first":null)},onPress(e){"touch"!==e.pointerType||o||t.toggle()}}:d,id:r,onKeyDown:e=>{if(!o&&("longPress"!==l||e.altKey)&&a&&a.current)switch(e.key){case"Enter":case" ":if("longPress"===l)return;case"ArrowDown":"continuePropagation"in e||e.stopPropagation(),e.preventDefault(),t.toggle("first");break;case"ArrowUp":"continuePropagation"in e||e.stopPropagation(),e.preventDefault(),t.toggle("last");break;default:"continuePropagation"in e&&e.continuePropagation()}}},menuProps:{...c,"aria-labelledby":r,autoFocus:t.focusStrategy||!0,onClose:t.close}}}({isDisabled:n,type:"listbox"},t,a),{typeSelectProps:p}=(0,eO.I)({keyboardDelegate:l,selectionManager:t.selectionManager,onTypeSelect(e){t.setSelectedKeys([e])}}),{isInvalid:d,validationErrors:u,validationDetails:m}=t.displayValidation,{labelProps:f,fieldProps:g,descriptionProps:v,errorMessageProps:h}=(0,I.M)({...e,labelElementType:"span",isInvalid:d,errorMessage:e.errorMessage||u});p.onKeyDown=p.onKeyDownCapture,delete p.onKeyDownCapture;let x=(0,eM.$)(e,{labelable:!0}),b=(0,j.v)(p,r,g),y=(0,eg.Bi)();return{labelProps:{...f,onClick:()=>{var t;e.isDisabled||(null==(t=a.current)||t.focus(),(0,A.Cl)("keyboard"))}},triggerProps:(0,j.v)(x,{...b,onKeyDown:(0,eA.c)(b.onKeyDown,e=>{if("single"===t.selectionMode)switch(e.key){case"ArrowLeft":{e.preventDefault();let a=t.selectedKeys.size>0?l.getKeyAbove(t.selectedKeys.values().next().value):l.getFirstKey();a&&t.setSelectedKeys([a]);break}case"ArrowRight":{e.preventDefault();let a=t.selectedKeys.size>0?l.getKeyBelow(t.selectedKeys.values().next().value):l.getFirstKey();a&&t.setSelectedKeys([a])}}},e.onKeyDown),onKeyUp:e.onKeyUp,"aria-labelledby":[y,void 0!==x["aria-label"]?void 0!==x["aria-labelledby"]?x["aria-labelledby"]:b.id:b["aria-labelledby"]].join(" "),onFocus(a){!t.isFocused&&(e.onFocus&&e.onFocus(a),t.setFocused(!0))},onBlur(a){!t.isOpen&&(e.onBlur&&e.onBlur(a),t.setFocused(!1))}}),valueProps:{id:y},menuProps:{...s,disallowEmptySelection:i,autoFocus:t.focusStrategy||!0,shouldSelectOnPressUp:!0,shouldFocusOnHover:!0,onBlur:a=>{!a.currentTarget.contains(a.relatedTarget)&&(e.onBlur&&e.onBlur(a),t.setFocused(!1))},onFocus:null==s?void 0:s.onFocus,"aria-labelledby":[g["aria-labelledby"],b["aria-label"]&&!g["aria-labelledby"]?b.id:null].filter(Boolean).join(" ")},descriptionProps:v,errorMessageProps:h,isInvalid:d,validationErrors:u,validationDetails:m}}({...p,disallowEmptySelection:X,isDisabled:e.isDisabled},ek,eb),eH=e.isInvalid||"invalid"===en||eK,{isPressed:e$,buttonProps:eU}=(0,x.l)(eP,eb),{focusProps:eV,isFocused:eG,isFocusVisible:eY}=(0,b.o)(),{isHovered:eX,hoverProps:eZ}=(0,E.M)({isDisabled:e.isDisabled}),eJ=(0,c.useMemo)(()=>{var t;return e.labelPlacement&&"inside"!==e.labelPlacement||O?null!=(t=e.labelPlacement)?t:"inside":"outside"},[e.labelPlacement,O]),eQ=!!$,e0="outside-left"===eJ||"outside"===eJ&&(!(eQ||W)||!!e.isMultiline),e1="inside"===eJ,e4="outside-left"===eJ,e3=ek.isOpen||eQ||!!(null==(o=ek.selectedItems)?void 0:o.length)||!!L||!!K||!!e.isMultiline,e2=!!(null==(l=ek.selectedItems)?void 0:l.length),e5=!!O,e6=(0,y.$)(null==es?void 0:es.base,er),e8=(0,c.useMemo)(()=>g({...m,isInvalid:eH,labelPlacement:eJ,disableAnimation:f,className:er}),[(0,w.t6)(m),eH,eJ,f,er]),e7="function"==typeof p.errorMessage?p.errorMessage({isInvalid:eH,validationErrors:eW,validationDetails:eq}):p.errorMessage||(null==eW?void 0:eW.join(" ")),e9=!!W||!!e7,te=(0,c.useCallback)((e={})=>({"data-slot":"base","data-filled":(0,k.sE)(e3),"data-has-value":(0,k.sE)(e2),"data-has-label":(0,k.sE)(e5),"data-has-helper":(0,k.sE)(e9),"data-invalid":(0,k.sE)(eH),className:e8.base({class:(0,y.$)(e6,e.className)}),...e}),[e8,e9,e2,e5,e3,e6]),tt=(0,c.useCallback)((t={})=>({ref:eb,"data-slot":"trigger","data-open":(0,k.sE)(ek.isOpen),"data-disabled":(0,k.sE)(null==e?void 0:e.isDisabled),"data-focus":(0,k.sE)(eG),"data-pressed":(0,k.sE)(e$),"data-focus-visible":(0,k.sE)(eY),"data-hover":(0,k.sE)(eX),className:e8.trigger({class:null==es?void 0:es.trigger}),...(0,j.v)(eU,eV,eZ,(0,h.$)(ed,{enabled:eh}),(0,h.$)(t))}),[e8,eb,ek.isOpen,null==es?void 0:es.trigger,null==e?void 0:e.isDisabled,eG,e$,eY,eX,eU,eV,eZ,ed,eh]),ta=(0,c.useCallback)((t={})=>({state:ek,triggerRef:eb,selectRef:ex,selectionMode:Z,label:null==e?void 0:e.label,name:null==e?void 0:e.name,isRequired:null==e?void 0:e.isRequired,autoComplete:null==e?void 0:e.autoComplete,isDisabled:null==e?void 0:e.isDisabled,onChange:eo,...t}),[ek,Z,null==e?void 0:e.label,null==e?void 0:e.autoComplete,null==e?void 0:e.name,null==e?void 0:e.isDisabled,eb]),ti=(0,c.useCallback)((e={})=>({"data-slot":"label",className:e8.label({class:(0,y.$)(null==es?void 0:es.label,e.className)}),...ez,...e}),[e8,null==es?void 0:es.label,ez]),tn=(0,c.useCallback)((e={})=>({"data-slot":"value",className:e8.value({class:(0,y.$)(null==es?void 0:es.value,e.className)}),...eC,...e}),[e8,null==es?void 0:es.value,eC]),to=(0,c.useCallback)((e={})=>({"data-slot":"listboxWrapper",className:e8.listboxWrapper({class:(0,y.$)(null==es?void 0:es.listboxWrapper,null==e?void 0:e.className)}),style:{maxHeight:null!=G?G:256,...e.style},...(0,j.v)(em.scrollShadowProps,e)}),[e8.listboxWrapper,null==es?void 0:es.listboxWrapper,em.scrollShadowProps,G]),tl=(0,c.useCallback)((e={})=>{var t,a;let i=(0,j.v)(em.popoverProps,e);return{state:ek,triggerRef:eb,ref:ew,"data-slot":"popover",scrollRef:ey,triggerType:"listbox",classNames:{content:e8.popoverContent({class:(0,y.$)(null==es?void 0:es.popoverContent,e.className)})},...i,offset:ek.selectedItems&&ek.selectedItems.length>0?1e-8*ek.selectedItems.length+((null==(t=em.popoverProps)?void 0:t.offset)||0):null==(a=em.popoverProps)?void 0:a.offset,shouldCloseOnInteractOutside:(null==i?void 0:i.shouldCloseOnInteractOutside)?i.shouldCloseOnInteractOutside:e=>eF(e,ex,ek)}},[e8,null==es?void 0:es.popoverContent,em.popoverProps,eb,ek,ek.selectedItems]),tr=(0,c.useCallback)(()=>({"data-slot":"selectorIcon","aria-hidden":(0,k.sE)(!0),"data-open":(0,k.sE)(ek.isOpen),className:e8.selectorIcon({class:null==es?void 0:es.selectorIcon})}),[e8,null==es?void 0:es.selectorIcon,ek.isOpen]),ts=(0,c.useCallback)((e={})=>({...e,"data-slot":"innerWrapper",className:e8.innerWrapper({class:(0,y.$)(null==es?void 0:es.innerWrapper,null==e?void 0:e.className)})}),[e8,null==es?void 0:es.innerWrapper]),tc=(0,c.useCallback)((e={})=>({...e,"data-slot":"helperWrapper",className:e8.helperWrapper({class:(0,y.$)(null==es?void 0:es.helperWrapper,null==e?void 0:e.className)})}),[e8,null==es?void 0:es.helperWrapper]),tp=(0,c.useCallback)((e={})=>({...e,...e_,"data-slot":"description",className:e8.description({class:(0,y.$)(null==es?void 0:es.description,null==e?void 0:e.className)})}),[e8,null==es?void 0:es.description]),td=(0,c.useCallback)((e={})=>({...e,"data-slot":"mainWrapper",className:e8.mainWrapper({class:(0,y.$)(null==es?void 0:es.mainWrapper,null==e?void 0:e.className)})}),[e8,null==es?void 0:es.mainWrapper]),tu=(0,c.useCallback)((e={})=>({...e,...eL,"data-slot":"error-message",className:e8.errorMessage({class:(0,y.$)(null==es?void 0:es.errorMessage,null==e?void 0:e.className)})}),[e8,eL,null==es?void 0:es.errorMessage]),tm=(0,c.useCallback)((e={})=>({"aria-hidden":(0,k.sE)(!0),"data-slot":"spinner",color:"current",size:"sm",...ei,...e,ref:J,className:e8.spinner({class:(0,y.$)(null==es?void 0:es.spinner,null==e?void 0:e.className)})}),[e8,J,ei,null==es?void 0:es.spinner]);return eN.set(ek,{isDisabled:null==e?void 0:e.isDisabled,isRequired:null==e?void 0:e.isRequired,name:null==e?void 0:e.name,isInvalid:eH,validationBehavior:ec}),{Component:ev,domRef:ex,state:ek,label:O,name:F,triggerRef:eb,isLoading:R,placeholder:$,startContent:L,endContent:K,description:W,selectorIcon:B,hasHelper:e9,labelPlacement:eJ,hasPlaceholder:eQ,renderValue:q,selectionMode:Z,disableAnimation:f,isOutsideLeft:e4,shouldLabelBeOutside:e0,shouldLabelBeInside:e1,isInvalid:eH,errorMessage:e7,getBaseProps:te,getTriggerProps:tt,getLabelProps:ti,getValueProps:tn,getListboxProps:(e={})=>{let t=null!=U?U:ek.collection.size>50;return{state:ek,ref:ey,isVirtualized:t,virtualization:t?{maxListboxHeight:G,itemHeight:V}:void 0,"data-slot":"listbox",className:e8.listbox({class:(0,y.$)(null==es?void 0:es.listbox,null==e?void 0:e.className)}),scrollShadowProps:em.scrollShadowProps,...(0,j.v)(em.listboxProps,e,eT)}},getPopoverProps:tl,getSpinnerProps:tm,getMainWrapperProps:td,getListboxWrapperProps:to,getHiddenSelectProps:ta,getInnerWrapperProps:ts,getHelperWrapperProps:tc,getDescriptionProps:tp,getErrorMessageProps:tu,getSelectorIconProps:tr}}({...e,ref:t}),Q=n?(0,o.jsx)("label",{..._(),children:n}):null,ee=(0,c.cloneElement)(p,J()),et=(0,c.useMemo)(()=>{let e=D&&f,t=e||m;return l&&t?(0,o.jsx)("div",{...G(),children:e?(0,o.jsx)("div",{...Z(),children:f}):(0,o.jsx)("div",{...X(),children:m})}):null},[l,D,f,m,G,Z,X]),ea=(0,c.useMemo)(()=>{var e;return(null==(e=i.selectedItems)?void 0:e.length)?R&&"function"==typeof R?R([...i.selectedItems].map(e=>({key:e.key,data:e.value,type:e.type,props:e.props,textValue:e.textValue,rendered:e.rendered,"aria-label":e["aria-label"]}))):i.selectedItems.map(e=>e.textValue).join(", "):F},[i.selectedItems,R,F]),ei=(0,c.useMemo)(()=>r?(0,o.jsx)(a$.o,{...H()}):ee,[r,ee,H]),en=(0,c.useMemo)(()=>i.isOpen?(0,o.jsx)(aq,{...q(),children:(0,o.jsx)(aU.H,{...Y(),children:(0,o.jsx)(tb,{...W()})})}):null,[i.isOpen,q,i,s,Y,W]);return(0,o.jsxs)("div",{...T(),children:[(0,o.jsx)(eH,{...V()}),B?Q:null,(0,o.jsxs)("div",{...$(),children:[(0,o.jsxs)(a,{...L(),children:[B?null:Q,(0,o.jsxs)("div",{...U(),children:[S,(0,o.jsx)("span",{...K(),children:ea}),O&&i.selectedItems&&(0,o.jsx)(eW,{elementType:"span",children:","}),O]}),ei]}),et]}),N?en:(0,o.jsx)(aV.N,{children:en})]})}),aY=a(11223).q,aX=a(66206);function aZ(){return(aZ=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var i in a)({}).hasOwnProperty.call(a,i)&&(e[i]=a[i])}return e}).apply(null,arguments)}var aJ="undefined"!=typeof document?c.useLayoutEffect:c.useEffect,aQ=function(e){var t=p().useRef(e);return aJ(function(){t.current=e}),t},a0=function(e,t){if("function"==typeof e){e(t);return}e.current=t},a1=function(e,t){var a=p().useRef();return p().useCallback(function(i){e.current=i,a.current&&a0(a.current,null),a.current=t,t&&a0(t,i)},[t])},a4="undefined"!=typeof document,a3={"min-height":"0","max-height":"none",height:"0",visibility:"hidden",overflow:"hidden",position:"absolute","z-index":"-1000",top:"0",right:"0",display:"block"},a2=function(e){Object.keys(a3).forEach(function(t){e.style.setProperty(t,a3[t],"important")})},a5=null,a6=function(e,t){var a=e.scrollHeight;return"border-box"===t.sizingStyle.boxSizing?a+t.borderSize:a-t.paddingSize},a8=function(){},a7=["borderBottomWidth","borderLeftWidth","borderRightWidth","borderTopWidth","boxSizing","fontFamily","fontSize","fontStyle","fontWeight","letterSpacing","lineHeight","paddingBottom","paddingLeft","paddingRight","paddingTop","tabSize","textIndent","textRendering","textTransform","width","wordBreak","wordSpacing","scrollbarGutter"],a9=!!a4&&!!document.documentElement.currentStyle,ie=function(e){var t=window.getComputedStyle(e);if(null===t)return null;var a=a7.reduce(function(e,a){return e[a]=t[a],e},{}),i=a.boxSizing;if(""===i)return null;a9&&"border-box"===i&&(a.width=parseFloat(a.width)+parseFloat(a.borderRightWidth)+parseFloat(a.borderLeftWidth)+parseFloat(a.paddingRight)+parseFloat(a.paddingLeft)+"px");var n=parseFloat(a.paddingBottom)+parseFloat(a.paddingTop),o=parseFloat(a.borderBottomWidth)+parseFloat(a.borderTopWidth);return{sizingStyle:a,paddingSize:n,borderSize:o}};function it(e,t,a){var i=aQ(a);c.useLayoutEffect(function(){var a=function(e){return i.current(e)};if(e)return e.addEventListener(t,a),function(){return e.removeEventListener(t,a)}},[])}var ia=function(e,t){it(document.body,"reset",function(a){e.current.form===a.target&&t(a)})},ii=function(e){it(window,"resize",e)},io=function(e){it(document.fonts,"loadingdone",e)},il=["cacheMeasurements","maxRows","minRows","onChange","onHeightChange"],ir=c.forwardRef(function(e,t){var a=e.cacheMeasurements,i=e.maxRows,n=e.minRows,o=e.onChange,l=void 0===o?a8:o,r=e.onHeightChange,s=void 0===r?a8:r,p=function(e,t){if(null==e)return{};var a={};for(var i in e)if(({}).hasOwnProperty.call(e,i)){if(-1!==t.indexOf(i))continue;a[i]=e[i]}return a}(e,il),d=void 0!==p.value,u=c.useRef(null),m=a1(u,t),f=c.useRef(0),g=c.useRef(),v=function(){var e,t,o,l,r,c,p,d,m,v,h,x=u.current,b=a&&g.current?g.current:ie(x);if(b){g.current=b;var y=(e=x.value||x.placeholder||"x",void 0===(t=n)&&(t=1),void 0===(o=i)&&(o=1/0),a5||((a5=document.createElement("textarea")).setAttribute("tabindex","-1"),a5.setAttribute("aria-hidden","true"),a2(a5)),null===a5.parentNode&&document.body.appendChild(a5),l=b.paddingSize,r=b.borderSize,p=(c=b.sizingStyle).boxSizing,Object.keys(c).forEach(function(e){a5.style[e]=c[e]}),a2(a5),a5.value=e,d=a6(a5,b),a5.value=e,d=a6(a5,b),a5.value="x",v=(m=a5.scrollHeight-l)*t,"border-box"===p&&(v=v+l+r),d=Math.max(v,d),h=m*o,"border-box"===p&&(h=h+l+r),[d=Math.min(h,d),m]),w=y[0],k=y[1];f.current!==w&&(f.current=w,x.style.setProperty("height",w+"px","important"),s(w,{rowHeight:k}))}};return a4?(c.useLayoutEffect(v),ia(u,function(){if(!d){var e=u.current.value;requestAnimationFrame(function(){var t=u.current;t&&e!==t.value&&v()})}}),ii(v),io(v),c.createElement("textarea",aZ({},p,{onChange:function(e){d||v(),l(e)},ref:m}))):c.createElement("textarea",aZ({},p,{onChange:l,ref:m}))}),is=a(23025),ic=(0,u.Rf)(({style:e,minRows:t=3,maxRows:a=8,cacheMeasurements:i=!1,disableAutosize:n=!1,onHeightChange:l,...r},s)=>{let{Component:p,label:d,description:u,startContent:m,endContent:f,hasHelper:g,shouldLabelBeOutside:v,shouldLabelBeInside:h,isInvalid:x,errorMessage:b,getBaseProps:y,getLabelProps:w,getInputProps:E,getInnerWrapperProps:z,getInputWrapperProps:D,getHelperWrapperProps:P,getDescriptionProps:C,getErrorMessageProps:S,isClearable:O,getClearButtonProps:M}=(0,aX.G)({...r,ref:s,isMultiline:!0}),[A,I]=(0,c.useState)(t>1),[F,R]=(0,c.useState)(!1),B=d?(0,o.jsx)("label",{...w(),children:d}):null,N=E(),T=n?(0,o.jsx)("textarea",{...N,style:(0,j.v)(N.style,null!=e?e:{})}):(0,o.jsx)(ir,{...N,cacheMeasurements:i,"data-hide-scroll":(0,k.sE)(!F),maxRows:a,minRows:t,style:(0,j.v)(N.style,null!=e?e:{}),onHeightChange:(e,i)=>{1===t&&I(e>=2*i.rowHeight),a>t&&R(e>=a*i.rowHeight),null==l||l(e,i)}}),_=(0,c.useMemo)(()=>O?(0,o.jsx)("button",{...M(),children:(0,o.jsx)(is.o,{})}):null,[O,M]),L=(0,c.useMemo)(()=>m||f?(0,o.jsxs)("div",{...z(),children:[m,T,f]}):(0,o.jsx)("div",{...z(),children:T}),[m,N,f,z]),K=x&&b,W=K||u;return(0,o.jsxs)(p,{...y(),children:[v?B:null,(0,o.jsxs)("div",{...D(),"data-has-multiple-rows":(0,k.sE)(A),children:[h?B:null,L,_]}),g&&W?(0,o.jsx)("div",{...P(),children:K?(0,o.jsx)("div",{...S(),children:b}):(0,o.jsx)("div",{...C(),children:u})}):null]})});ic.displayName="NextUI.Textarea";var ip=a(36424),id=a(49370),iu=a(45880);let im=["image/jpeg","image/png","image/webp","image/gif"],ig=iu.Ik({location:iu.Yj({required_error:"يرجى تحديد موقعك"}),emergency_type:iu.k5(["O","M","D"],{message:"يرجى تحديد نوع المساعدة الصحيح"}),images:iu.YO(iu.Nl(File)).optional().refine(e=>!e||e.every(e=>e.size<=5242880&&im.includes(e.type)),"يجب أن يكون حجم كل صورة أقل من 5 ميجابايت").refine(e=>!!e&&e.length<=5&&e.length>0,"يجب أن تحتوي الصور على صورة واحدة على الأقل ولا تزيد عن 5 صور"),description:iu.Yj().min(10,"يجب أن يحتوي الوصف على 10 أحرف على الأقل").max(500,"يجب أن لا يتجاوز الوصف 500 حرف")});var iv=a(11468),ih=a(87955);function ix(e,t,a,i){return new(a||(a=Promise))(function(n,o){function l(e){try{s(i.next(e))}catch(e){o(e)}}function r(e){try{s(i.throw(e))}catch(e){o(e)}}function s(e){var t;e.done?n(e.value):((t=e.value)instanceof a?t:new a(function(e){e(t)})).then(l,r)}s((i=i.apply(e,t||[])).next())})}Object.create;Object.create,"function"==typeof SuppressedError&&SuppressedError;let ib=new Map([["1km","application/vnd.1000minds.decision-model+xml"],["3dml","text/vnd.in3d.3dml"],["3ds","image/x-3ds"],["3g2","video/3gpp2"],["3gp","video/3gp"],["3gpp","video/3gpp"],["3mf","model/3mf"],["7z","application/x-7z-compressed"],["7zip","application/x-7z-compressed"],["123","application/vnd.lotus-1-2-3"],["aab","application/x-authorware-bin"],["aac","audio/x-acc"],["aam","application/x-authorware-map"],["aas","application/x-authorware-seg"],["abw","application/x-abiword"],["ac","application/vnd.nokia.n-gage.ac+xml"],["ac3","audio/ac3"],["acc","application/vnd.americandynamics.acc"],["ace","application/x-ace-compressed"],["acu","application/vnd.acucobol"],["acutc","application/vnd.acucorp"],["adp","audio/adpcm"],["aep","application/vnd.audiograph"],["afm","application/x-font-type1"],["afp","application/vnd.ibm.modcap"],["ahead","application/vnd.ahead.space"],["ai","application/pdf"],["aif","audio/x-aiff"],["aifc","audio/x-aiff"],["aiff","audio/x-aiff"],["air","application/vnd.adobe.air-application-installer-package+zip"],["ait","application/vnd.dvb.ait"],["ami","application/vnd.amiga.ami"],["amr","audio/amr"],["apk","application/vnd.android.package-archive"],["apng","image/apng"],["appcache","text/cache-manifest"],["application","application/x-ms-application"],["apr","application/vnd.lotus-approach"],["arc","application/x-freearc"],["arj","application/x-arj"],["asc","application/pgp-signature"],["asf","video/x-ms-asf"],["asm","text/x-asm"],["aso","application/vnd.accpac.simply.aso"],["asx","video/x-ms-asf"],["atc","application/vnd.acucorp"],["atom","application/atom+xml"],["atomcat","application/atomcat+xml"],["atomdeleted","application/atomdeleted+xml"],["atomsvc","application/atomsvc+xml"],["atx","application/vnd.antix.game-component"],["au","audio/x-au"],["avi","video/x-msvideo"],["avif","image/avif"],["aw","application/applixware"],["azf","application/vnd.airzip.filesecure.azf"],["azs","application/vnd.airzip.filesecure.azs"],["azv","image/vnd.airzip.accelerator.azv"],["azw","application/vnd.amazon.ebook"],["b16","image/vnd.pco.b16"],["bat","application/x-msdownload"],["bcpio","application/x-bcpio"],["bdf","application/x-font-bdf"],["bdm","application/vnd.syncml.dm+wbxml"],["bdoc","application/x-bdoc"],["bed","application/vnd.realvnc.bed"],["bh2","application/vnd.fujitsu.oasysprs"],["bin","application/octet-stream"],["blb","application/x-blorb"],["blorb","application/x-blorb"],["bmi","application/vnd.bmi"],["bmml","application/vnd.balsamiq.bmml+xml"],["bmp","image/bmp"],["book","application/vnd.framemaker"],["box","application/vnd.previewsystems.box"],["boz","application/x-bzip2"],["bpk","application/octet-stream"],["bpmn","application/octet-stream"],["bsp","model/vnd.valve.source.compiled-map"],["btif","image/prs.btif"],["buffer","application/octet-stream"],["bz","application/x-bzip"],["bz2","application/x-bzip2"],["c","text/x-c"],["c4d","application/vnd.clonk.c4group"],["c4f","application/vnd.clonk.c4group"],["c4g","application/vnd.clonk.c4group"],["c4p","application/vnd.clonk.c4group"],["c4u","application/vnd.clonk.c4group"],["c11amc","application/vnd.cluetrust.cartomobile-config"],["c11amz","application/vnd.cluetrust.cartomobile-config-pkg"],["cab","application/vnd.ms-cab-compressed"],["caf","audio/x-caf"],["cap","application/vnd.tcpdump.pcap"],["car","application/vnd.curl.car"],["cat","application/vnd.ms-pki.seccat"],["cb7","application/x-cbr"],["cba","application/x-cbr"],["cbr","application/x-cbr"],["cbt","application/x-cbr"],["cbz","application/x-cbr"],["cc","text/x-c"],["cco","application/x-cocoa"],["cct","application/x-director"],["ccxml","application/ccxml+xml"],["cdbcmsg","application/vnd.contact.cmsg"],["cda","application/x-cdf"],["cdf","application/x-netcdf"],["cdfx","application/cdfx+xml"],["cdkey","application/vnd.mediastation.cdkey"],["cdmia","application/cdmi-capability"],["cdmic","application/cdmi-container"],["cdmid","application/cdmi-domain"],["cdmio","application/cdmi-object"],["cdmiq","application/cdmi-queue"],["cdr","application/cdr"],["cdx","chemical/x-cdx"],["cdxml","application/vnd.chemdraw+xml"],["cdy","application/vnd.cinderella"],["cer","application/pkix-cert"],["cfs","application/x-cfs-compressed"],["cgm","image/cgm"],["chat","application/x-chat"],["chm","application/vnd.ms-htmlhelp"],["chrt","application/vnd.kde.kchart"],["cif","chemical/x-cif"],["cii","application/vnd.anser-web-certificate-issue-initiation"],["cil","application/vnd.ms-artgalry"],["cjs","application/node"],["cla","application/vnd.claymore"],["class","application/octet-stream"],["clkk","application/vnd.crick.clicker.keyboard"],["clkp","application/vnd.crick.clicker.palette"],["clkt","application/vnd.crick.clicker.template"],["clkw","application/vnd.crick.clicker.wordbank"],["clkx","application/vnd.crick.clicker"],["clp","application/x-msclip"],["cmc","application/vnd.cosmocaller"],["cmdf","chemical/x-cmdf"],["cml","chemical/x-cml"],["cmp","application/vnd.yellowriver-custom-menu"],["cmx","image/x-cmx"],["cod","application/vnd.rim.cod"],["coffee","text/coffeescript"],["com","application/x-msdownload"],["conf","text/plain"],["cpio","application/x-cpio"],["cpp","text/x-c"],["cpt","application/mac-compactpro"],["crd","application/x-mscardfile"],["crl","application/pkix-crl"],["crt","application/x-x509-ca-cert"],["crx","application/x-chrome-extension"],["cryptonote","application/vnd.rig.cryptonote"],["csh","application/x-csh"],["csl","application/vnd.citationstyles.style+xml"],["csml","chemical/x-csml"],["csp","application/vnd.commonspace"],["csr","application/octet-stream"],["css","text/css"],["cst","application/x-director"],["csv","text/csv"],["cu","application/cu-seeme"],["curl","text/vnd.curl"],["cww","application/prs.cww"],["cxt","application/x-director"],["cxx","text/x-c"],["dae","model/vnd.collada+xml"],["daf","application/vnd.mobius.daf"],["dart","application/vnd.dart"],["dataless","application/vnd.fdsn.seed"],["davmount","application/davmount+xml"],["dbf","application/vnd.dbf"],["dbk","application/docbook+xml"],["dcr","application/x-director"],["dcurl","text/vnd.curl.dcurl"],["dd2","application/vnd.oma.dd2+xml"],["ddd","application/vnd.fujixerox.ddd"],["ddf","application/vnd.syncml.dmddf+xml"],["dds","image/vnd.ms-dds"],["deb","application/x-debian-package"],["def","text/plain"],["deploy","application/octet-stream"],["der","application/x-x509-ca-cert"],["dfac","application/vnd.dreamfactory"],["dgc","application/x-dgc-compressed"],["dic","text/x-c"],["dir","application/x-director"],["dis","application/vnd.mobius.dis"],["disposition-notification","message/disposition-notification"],["dist","application/octet-stream"],["distz","application/octet-stream"],["djv","image/vnd.djvu"],["djvu","image/vnd.djvu"],["dll","application/octet-stream"],["dmg","application/x-apple-diskimage"],["dmn","application/octet-stream"],["dmp","application/vnd.tcpdump.pcap"],["dms","application/octet-stream"],["dna","application/vnd.dna"],["doc","application/msword"],["docm","application/vnd.ms-word.template.macroEnabled.12"],["docx","application/vnd.openxmlformats-officedocument.wordprocessingml.document"],["dot","application/msword"],["dotm","application/vnd.ms-word.template.macroEnabled.12"],["dotx","application/vnd.openxmlformats-officedocument.wordprocessingml.template"],["dp","application/vnd.osgi.dp"],["dpg","application/vnd.dpgraph"],["dra","audio/vnd.dra"],["drle","image/dicom-rle"],["dsc","text/prs.lines.tag"],["dssc","application/dssc+der"],["dtb","application/x-dtbook+xml"],["dtd","application/xml-dtd"],["dts","audio/vnd.dts"],["dtshd","audio/vnd.dts.hd"],["dump","application/octet-stream"],["dvb","video/vnd.dvb.file"],["dvi","application/x-dvi"],["dwd","application/atsc-dwd+xml"],["dwf","model/vnd.dwf"],["dwg","image/vnd.dwg"],["dxf","image/vnd.dxf"],["dxp","application/vnd.spotfire.dxp"],["dxr","application/x-director"],["ear","application/java-archive"],["ecelp4800","audio/vnd.nuera.ecelp4800"],["ecelp7470","audio/vnd.nuera.ecelp7470"],["ecelp9600","audio/vnd.nuera.ecelp9600"],["ecma","application/ecmascript"],["edm","application/vnd.novadigm.edm"],["edx","application/vnd.novadigm.edx"],["efif","application/vnd.picsel"],["ei6","application/vnd.pg.osasli"],["elc","application/octet-stream"],["emf","image/emf"],["eml","message/rfc822"],["emma","application/emma+xml"],["emotionml","application/emotionml+xml"],["emz","application/x-msmetafile"],["eol","audio/vnd.digital-winds"],["eot","application/vnd.ms-fontobject"],["eps","application/postscript"],["epub","application/epub+zip"],["es","application/ecmascript"],["es3","application/vnd.eszigno3+xml"],["esa","application/vnd.osgi.subsystem"],["esf","application/vnd.epson.esf"],["et3","application/vnd.eszigno3+xml"],["etx","text/x-setext"],["eva","application/x-eva"],["evy","application/x-envoy"],["exe","application/octet-stream"],["exi","application/exi"],["exp","application/express"],["exr","image/aces"],["ext","application/vnd.novadigm.ext"],["ez","application/andrew-inset"],["ez2","application/vnd.ezpix-album"],["ez3","application/vnd.ezpix-package"],["f","text/x-fortran"],["f4v","video/mp4"],["f77","text/x-fortran"],["f90","text/x-fortran"],["fbs","image/vnd.fastbidsheet"],["fcdt","application/vnd.adobe.formscentral.fcdt"],["fcs","application/vnd.isac.fcs"],["fdf","application/vnd.fdf"],["fdt","application/fdt+xml"],["fe_launch","application/vnd.denovo.fcselayout-link"],["fg5","application/vnd.fujitsu.oasysgp"],["fgd","application/x-director"],["fh","image/x-freehand"],["fh4","image/x-freehand"],["fh5","image/x-freehand"],["fh7","image/x-freehand"],["fhc","image/x-freehand"],["fig","application/x-xfig"],["fits","image/fits"],["flac","audio/x-flac"],["fli","video/x-fli"],["flo","application/vnd.micrografx.flo"],["flv","video/x-flv"],["flw","application/vnd.kde.kivio"],["flx","text/vnd.fmi.flexstor"],["fly","text/vnd.fly"],["fm","application/vnd.framemaker"],["fnc","application/vnd.frogans.fnc"],["fo","application/vnd.software602.filler.form+xml"],["for","text/x-fortran"],["fpx","image/vnd.fpx"],["frame","application/vnd.framemaker"],["fsc","application/vnd.fsc.weblaunch"],["fst","image/vnd.fst"],["ftc","application/vnd.fluxtime.clip"],["fti","application/vnd.anser-web-funds-transfer-initiation"],["fvt","video/vnd.fvt"],["fxp","application/vnd.adobe.fxp"],["fxpl","application/vnd.adobe.fxp"],["fzs","application/vnd.fuzzysheet"],["g2w","application/vnd.geoplan"],["g3","image/g3fax"],["g3w","application/vnd.geospace"],["gac","application/vnd.groove-account"],["gam","application/x-tads"],["gbr","application/rpki-ghostbusters"],["gca","application/x-gca-compressed"],["gdl","model/vnd.gdl"],["gdoc","application/vnd.google-apps.document"],["geo","application/vnd.dynageo"],["geojson","application/geo+json"],["gex","application/vnd.geometry-explorer"],["ggb","application/vnd.geogebra.file"],["ggt","application/vnd.geogebra.tool"],["ghf","application/vnd.groove-help"],["gif","image/gif"],["gim","application/vnd.groove-identity-message"],["glb","model/gltf-binary"],["gltf","model/gltf+json"],["gml","application/gml+xml"],["gmx","application/vnd.gmx"],["gnumeric","application/x-gnumeric"],["gpg","application/gpg-keys"],["gph","application/vnd.flographit"],["gpx","application/gpx+xml"],["gqf","application/vnd.grafeq"],["gqs","application/vnd.grafeq"],["gram","application/srgs"],["gramps","application/x-gramps-xml"],["gre","application/vnd.geometry-explorer"],["grv","application/vnd.groove-injector"],["grxml","application/srgs+xml"],["gsf","application/x-font-ghostscript"],["gsheet","application/vnd.google-apps.spreadsheet"],["gslides","application/vnd.google-apps.presentation"],["gtar","application/x-gtar"],["gtm","application/vnd.groove-tool-message"],["gtw","model/vnd.gtw"],["gv","text/vnd.graphviz"],["gxf","application/gxf"],["gxt","application/vnd.geonext"],["gz","application/gzip"],["gzip","application/gzip"],["h","text/x-c"],["h261","video/h261"],["h263","video/h263"],["h264","video/h264"],["hal","application/vnd.hal+xml"],["hbci","application/vnd.hbci"],["hbs","text/x-handlebars-template"],["hdd","application/x-virtualbox-hdd"],["hdf","application/x-hdf"],["heic","image/heic"],["heics","image/heic-sequence"],["heif","image/heif"],["heifs","image/heif-sequence"],["hej2","image/hej2k"],["held","application/atsc-held+xml"],["hh","text/x-c"],["hjson","application/hjson"],["hlp","application/winhlp"],["hpgl","application/vnd.hp-hpgl"],["hpid","application/vnd.hp-hpid"],["hps","application/vnd.hp-hps"],["hqx","application/mac-binhex40"],["hsj2","image/hsj2"],["htc","text/x-component"],["htke","application/vnd.kenameaapp"],["htm","text/html"],["html","text/html"],["hvd","application/vnd.yamaha.hv-dic"],["hvp","application/vnd.yamaha.hv-voice"],["hvs","application/vnd.yamaha.hv-script"],["i2g","application/vnd.intergeo"],["icc","application/vnd.iccprofile"],["ice","x-conference/x-cooltalk"],["icm","application/vnd.iccprofile"],["ico","image/x-icon"],["ics","text/calendar"],["ief","image/ief"],["ifb","text/calendar"],["ifm","application/vnd.shana.informed.formdata"],["iges","model/iges"],["igl","application/vnd.igloader"],["igm","application/vnd.insors.igm"],["igs","model/iges"],["igx","application/vnd.micrografx.igx"],["iif","application/vnd.shana.informed.interchange"],["img","application/octet-stream"],["imp","application/vnd.accpac.simply.imp"],["ims","application/vnd.ms-ims"],["in","text/plain"],["ini","text/plain"],["ink","application/inkml+xml"],["inkml","application/inkml+xml"],["install","application/x-install-instructions"],["iota","application/vnd.astraea-software.iota"],["ipfix","application/ipfix"],["ipk","application/vnd.shana.informed.package"],["irm","application/vnd.ibm.rights-management"],["irp","application/vnd.irepository.package+xml"],["iso","application/x-iso9660-image"],["itp","application/vnd.shana.informed.formtemplate"],["its","application/its+xml"],["ivp","application/vnd.immervision-ivp"],["ivu","application/vnd.immervision-ivu"],["jad","text/vnd.sun.j2me.app-descriptor"],["jade","text/jade"],["jam","application/vnd.jam"],["jar","application/java-archive"],["jardiff","application/x-java-archive-diff"],["java","text/x-java-source"],["jhc","image/jphc"],["jisp","application/vnd.jisp"],["jls","image/jls"],["jlt","application/vnd.hp-jlyt"],["jng","image/x-jng"],["jnlp","application/x-java-jnlp-file"],["joda","application/vnd.joost.joda-archive"],["jp2","image/jp2"],["jpe","image/jpeg"],["jpeg","image/jpeg"],["jpf","image/jpx"],["jpg","image/jpeg"],["jpg2","image/jp2"],["jpgm","video/jpm"],["jpgv","video/jpeg"],["jph","image/jph"],["jpm","video/jpm"],["jpx","image/jpx"],["js","application/javascript"],["json","application/json"],["json5","application/json5"],["jsonld","application/ld+json"],["jsonl","application/jsonl"],["jsonml","application/jsonml+json"],["jsx","text/jsx"],["jxr","image/jxr"],["jxra","image/jxra"],["jxrs","image/jxrs"],["jxs","image/jxs"],["jxsc","image/jxsc"],["jxsi","image/jxsi"],["jxss","image/jxss"],["kar","audio/midi"],["karbon","application/vnd.kde.karbon"],["kdb","application/octet-stream"],["kdbx","application/x-keepass2"],["key","application/x-iwork-keynote-sffkey"],["kfo","application/vnd.kde.kformula"],["kia","application/vnd.kidspiration"],["kml","application/vnd.google-earth.kml+xml"],["kmz","application/vnd.google-earth.kmz"],["kne","application/vnd.kinar"],["knp","application/vnd.kinar"],["kon","application/vnd.kde.kontour"],["kpr","application/vnd.kde.kpresenter"],["kpt","application/vnd.kde.kpresenter"],["kpxx","application/vnd.ds-keypoint"],["ksp","application/vnd.kde.kspread"],["ktr","application/vnd.kahootz"],["ktx","image/ktx"],["ktx2","image/ktx2"],["ktz","application/vnd.kahootz"],["kwd","application/vnd.kde.kword"],["kwt","application/vnd.kde.kword"],["lasxml","application/vnd.las.las+xml"],["latex","application/x-latex"],["lbd","application/vnd.llamagraphics.life-balance.desktop"],["lbe","application/vnd.llamagraphics.life-balance.exchange+xml"],["les","application/vnd.hhe.lesson-player"],["less","text/less"],["lgr","application/lgr+xml"],["lha","application/octet-stream"],["link66","application/vnd.route66.link66+xml"],["list","text/plain"],["list3820","application/vnd.ibm.modcap"],["listafp","application/vnd.ibm.modcap"],["litcoffee","text/coffeescript"],["lnk","application/x-ms-shortcut"],["log","text/plain"],["lostxml","application/lost+xml"],["lrf","application/octet-stream"],["lrm","application/vnd.ms-lrm"],["ltf","application/vnd.frogans.ltf"],["lua","text/x-lua"],["luac","application/x-lua-bytecode"],["lvp","audio/vnd.lucent.voice"],["lwp","application/vnd.lotus-wordpro"],["lzh","application/octet-stream"],["m1v","video/mpeg"],["m2a","audio/mpeg"],["m2v","video/mpeg"],["m3a","audio/mpeg"],["m3u","text/plain"],["m3u8","application/vnd.apple.mpegurl"],["m4a","audio/x-m4a"],["m4p","application/mp4"],["m4s","video/iso.segment"],["m4u","application/vnd.mpegurl"],["m4v","video/x-m4v"],["m13","application/x-msmediaview"],["m14","application/x-msmediaview"],["m21","application/mp21"],["ma","application/mathematica"],["mads","application/mads+xml"],["maei","application/mmt-aei+xml"],["mag","application/vnd.ecowin.chart"],["maker","application/vnd.framemaker"],["man","text/troff"],["manifest","text/cache-manifest"],["map","application/json"],["mar","application/octet-stream"],["markdown","text/markdown"],["mathml","application/mathml+xml"],["mb","application/mathematica"],["mbk","application/vnd.mobius.mbk"],["mbox","application/mbox"],["mc1","application/vnd.medcalcdata"],["mcd","application/vnd.mcd"],["mcurl","text/vnd.curl.mcurl"],["md","text/markdown"],["mdb","application/x-msaccess"],["mdi","image/vnd.ms-modi"],["mdx","text/mdx"],["me","text/troff"],["mesh","model/mesh"],["meta4","application/metalink4+xml"],["metalink","application/metalink+xml"],["mets","application/mets+xml"],["mfm","application/vnd.mfmp"],["mft","application/rpki-manifest"],["mgp","application/vnd.osgeo.mapguide.package"],["mgz","application/vnd.proteus.magazine"],["mid","audio/midi"],["midi","audio/midi"],["mie","application/x-mie"],["mif","application/vnd.mif"],["mime","message/rfc822"],["mj2","video/mj2"],["mjp2","video/mj2"],["mjs","application/javascript"],["mk3d","video/x-matroska"],["mka","audio/x-matroska"],["mkd","text/x-markdown"],["mks","video/x-matroska"],["mkv","video/x-matroska"],["mlp","application/vnd.dolby.mlp"],["mmd","application/vnd.chipnuts.karaoke-mmd"],["mmf","application/vnd.smaf"],["mml","text/mathml"],["mmr","image/vnd.fujixerox.edmics-mmr"],["mng","video/x-mng"],["mny","application/x-msmoney"],["mobi","application/x-mobipocket-ebook"],["mods","application/mods+xml"],["mov","video/quicktime"],["movie","video/x-sgi-movie"],["mp2","audio/mpeg"],["mp2a","audio/mpeg"],["mp3","audio/mpeg"],["mp4","video/mp4"],["mp4a","audio/mp4"],["mp4s","application/mp4"],["mp4v","video/mp4"],["mp21","application/mp21"],["mpc","application/vnd.mophun.certificate"],["mpd","application/dash+xml"],["mpe","video/mpeg"],["mpeg","video/mpeg"],["mpg","video/mpeg"],["mpg4","video/mp4"],["mpga","audio/mpeg"],["mpkg","application/vnd.apple.installer+xml"],["mpm","application/vnd.blueice.multipass"],["mpn","application/vnd.mophun.application"],["mpp","application/vnd.ms-project"],["mpt","application/vnd.ms-project"],["mpy","application/vnd.ibm.minipay"],["mqy","application/vnd.mobius.mqy"],["mrc","application/marc"],["mrcx","application/marcxml+xml"],["ms","text/troff"],["mscml","application/mediaservercontrol+xml"],["mseed","application/vnd.fdsn.mseed"],["mseq","application/vnd.mseq"],["msf","application/vnd.epson.msf"],["msg","application/vnd.ms-outlook"],["msh","model/mesh"],["msi","application/x-msdownload"],["msl","application/vnd.mobius.msl"],["msm","application/octet-stream"],["msp","application/octet-stream"],["msty","application/vnd.muvee.style"],["mtl","model/mtl"],["mts","model/vnd.mts"],["mus","application/vnd.musician"],["musd","application/mmt-usd+xml"],["musicxml","application/vnd.recordare.musicxml+xml"],["mvb","application/x-msmediaview"],["mvt","application/vnd.mapbox-vector-tile"],["mwf","application/vnd.mfer"],["mxf","application/mxf"],["mxl","application/vnd.recordare.musicxml"],["mxmf","audio/mobile-xmf"],["mxml","application/xv+xml"],["mxs","application/vnd.triscape.mxs"],["mxu","video/vnd.mpegurl"],["n-gage","application/vnd.nokia.n-gage.symbian.install"],["n3","text/n3"],["nb","application/mathematica"],["nbp","application/vnd.wolfram.player"],["nc","application/x-netcdf"],["ncx","application/x-dtbncx+xml"],["nfo","text/x-nfo"],["ngdat","application/vnd.nokia.n-gage.data"],["nitf","application/vnd.nitf"],["nlu","application/vnd.neurolanguage.nlu"],["nml","application/vnd.enliven"],["nnd","application/vnd.noblenet-directory"],["nns","application/vnd.noblenet-sealer"],["nnw","application/vnd.noblenet-web"],["npx","image/vnd.net-fpx"],["nq","application/n-quads"],["nsc","application/x-conference"],["nsf","application/vnd.lotus-notes"],["nt","application/n-triples"],["ntf","application/vnd.nitf"],["numbers","application/x-iwork-numbers-sffnumbers"],["nzb","application/x-nzb"],["oa2","application/vnd.fujitsu.oasys2"],["oa3","application/vnd.fujitsu.oasys3"],["oas","application/vnd.fujitsu.oasys"],["obd","application/x-msbinder"],["obgx","application/vnd.openblox.game+xml"],["obj","model/obj"],["oda","application/oda"],["odb","application/vnd.oasis.opendocument.database"],["odc","application/vnd.oasis.opendocument.chart"],["odf","application/vnd.oasis.opendocument.formula"],["odft","application/vnd.oasis.opendocument.formula-template"],["odg","application/vnd.oasis.opendocument.graphics"],["odi","application/vnd.oasis.opendocument.image"],["odm","application/vnd.oasis.opendocument.text-master"],["odp","application/vnd.oasis.opendocument.presentation"],["ods","application/vnd.oasis.opendocument.spreadsheet"],["odt","application/vnd.oasis.opendocument.text"],["oga","audio/ogg"],["ogex","model/vnd.opengex"],["ogg","audio/ogg"],["ogv","video/ogg"],["ogx","application/ogg"],["omdoc","application/omdoc+xml"],["onepkg","application/onenote"],["onetmp","application/onenote"],["onetoc","application/onenote"],["onetoc2","application/onenote"],["opf","application/oebps-package+xml"],["opml","text/x-opml"],["oprc","application/vnd.palm"],["opus","audio/ogg"],["org","text/x-org"],["osf","application/vnd.yamaha.openscoreformat"],["osfpvg","application/vnd.yamaha.openscoreformat.osfpvg+xml"],["osm","application/vnd.openstreetmap.data+xml"],["otc","application/vnd.oasis.opendocument.chart-template"],["otf","font/otf"],["otg","application/vnd.oasis.opendocument.graphics-template"],["oth","application/vnd.oasis.opendocument.text-web"],["oti","application/vnd.oasis.opendocument.image-template"],["otp","application/vnd.oasis.opendocument.presentation-template"],["ots","application/vnd.oasis.opendocument.spreadsheet-template"],["ott","application/vnd.oasis.opendocument.text-template"],["ova","application/x-virtualbox-ova"],["ovf","application/x-virtualbox-ovf"],["owl","application/rdf+xml"],["oxps","application/oxps"],["oxt","application/vnd.openofficeorg.extension"],["p","text/x-pascal"],["p7a","application/x-pkcs7-signature"],["p7b","application/x-pkcs7-certificates"],["p7c","application/pkcs7-mime"],["p7m","application/pkcs7-mime"],["p7r","application/x-pkcs7-certreqresp"],["p7s","application/pkcs7-signature"],["p8","application/pkcs8"],["p10","application/x-pkcs10"],["p12","application/x-pkcs12"],["pac","application/x-ns-proxy-autoconfig"],["pages","application/x-iwork-pages-sffpages"],["pas","text/x-pascal"],["paw","application/vnd.pawaafile"],["pbd","application/vnd.powerbuilder6"],["pbm","image/x-portable-bitmap"],["pcap","application/vnd.tcpdump.pcap"],["pcf","application/x-font-pcf"],["pcl","application/vnd.hp-pcl"],["pclxl","application/vnd.hp-pclxl"],["pct","image/x-pict"],["pcurl","application/vnd.curl.pcurl"],["pcx","image/x-pcx"],["pdb","application/x-pilot"],["pde","text/x-processing"],["pdf","application/pdf"],["pem","application/x-x509-user-cert"],["pfa","application/x-font-type1"],["pfb","application/x-font-type1"],["pfm","application/x-font-type1"],["pfr","application/font-tdpfr"],["pfx","application/x-pkcs12"],["pgm","image/x-portable-graymap"],["pgn","application/x-chess-pgn"],["pgp","application/pgp"],["php","application/x-httpd-php"],["php3","application/x-httpd-php"],["php4","application/x-httpd-php"],["phps","application/x-httpd-php-source"],["phtml","application/x-httpd-php"],["pic","image/x-pict"],["pkg","application/octet-stream"],["pki","application/pkixcmp"],["pkipath","application/pkix-pkipath"],["pkpass","application/vnd.apple.pkpass"],["pl","application/x-perl"],["plb","application/vnd.3gpp.pic-bw-large"],["plc","application/vnd.mobius.plc"],["plf","application/vnd.pocketlearn"],["pls","application/pls+xml"],["pm","application/x-perl"],["pml","application/vnd.ctc-posml"],["png","image/png"],["pnm","image/x-portable-anymap"],["portpkg","application/vnd.macports.portpkg"],["pot","application/vnd.ms-powerpoint"],["potm","application/vnd.ms-powerpoint.presentation.macroEnabled.12"],["potx","application/vnd.openxmlformats-officedocument.presentationml.template"],["ppa","application/vnd.ms-powerpoint"],["ppam","application/vnd.ms-powerpoint.addin.macroEnabled.12"],["ppd","application/vnd.cups-ppd"],["ppm","image/x-portable-pixmap"],["pps","application/vnd.ms-powerpoint"],["ppsm","application/vnd.ms-powerpoint.slideshow.macroEnabled.12"],["ppsx","application/vnd.openxmlformats-officedocument.presentationml.slideshow"],["ppt","application/powerpoint"],["pptm","application/vnd.ms-powerpoint.presentation.macroEnabled.12"],["pptx","application/vnd.openxmlformats-officedocument.presentationml.presentation"],["pqa","application/vnd.palm"],["prc","application/x-pilot"],["pre","application/vnd.lotus-freelance"],["prf","application/pics-rules"],["provx","application/provenance+xml"],["ps","application/postscript"],["psb","application/vnd.3gpp.pic-bw-small"],["psd","application/x-photoshop"],["psf","application/x-font-linux-psf"],["pskcxml","application/pskc+xml"],["pti","image/prs.pti"],["ptid","application/vnd.pvi.ptid1"],["pub","application/x-mspublisher"],["pvb","application/vnd.3gpp.pic-bw-var"],["pwn","application/vnd.3m.post-it-notes"],["pya","audio/vnd.ms-playready.media.pya"],["pyv","video/vnd.ms-playready.media.pyv"],["qam","application/vnd.epson.quickanime"],["qbo","application/vnd.intu.qbo"],["qfx","application/vnd.intu.qfx"],["qps","application/vnd.publishare-delta-tree"],["qt","video/quicktime"],["qwd","application/vnd.quark.quarkxpress"],["qwt","application/vnd.quark.quarkxpress"],["qxb","application/vnd.quark.quarkxpress"],["qxd","application/vnd.quark.quarkxpress"],["qxl","application/vnd.quark.quarkxpress"],["qxt","application/vnd.quark.quarkxpress"],["ra","audio/x-realaudio"],["ram","audio/x-pn-realaudio"],["raml","application/raml+yaml"],["rapd","application/route-apd+xml"],["rar","application/x-rar"],["ras","image/x-cmu-raster"],["rcprofile","application/vnd.ipunplugged.rcprofile"],["rdf","application/rdf+xml"],["rdz","application/vnd.data-vision.rdz"],["relo","application/p2p-overlay+xml"],["rep","application/vnd.businessobjects"],["res","application/x-dtbresource+xml"],["rgb","image/x-rgb"],["rif","application/reginfo+xml"],["rip","audio/vnd.rip"],["ris","application/x-research-info-systems"],["rl","application/resource-lists+xml"],["rlc","image/vnd.fujixerox.edmics-rlc"],["rld","application/resource-lists-diff+xml"],["rm","audio/x-pn-realaudio"],["rmi","audio/midi"],["rmp","audio/x-pn-realaudio-plugin"],["rms","application/vnd.jcp.javame.midlet-rms"],["rmvb","application/vnd.rn-realmedia-vbr"],["rnc","application/relax-ng-compact-syntax"],["rng","application/xml"],["roa","application/rpki-roa"],["roff","text/troff"],["rp9","application/vnd.cloanto.rp9"],["rpm","audio/x-pn-realaudio-plugin"],["rpss","application/vnd.nokia.radio-presets"],["rpst","application/vnd.nokia.radio-preset"],["rq","application/sparql-query"],["rs","application/rls-services+xml"],["rsa","application/x-pkcs7"],["rsat","application/atsc-rsat+xml"],["rsd","application/rsd+xml"],["rsheet","application/urc-ressheet+xml"],["rss","application/rss+xml"],["rtf","text/rtf"],["rtx","text/richtext"],["run","application/x-makeself"],["rusd","application/route-usd+xml"],["rv","video/vnd.rn-realvideo"],["s","text/x-asm"],["s3m","audio/s3m"],["saf","application/vnd.yamaha.smaf-audio"],["sass","text/x-sass"],["sbml","application/sbml+xml"],["sc","application/vnd.ibm.secure-container"],["scd","application/x-msschedule"],["scm","application/vnd.lotus-screencam"],["scq","application/scvp-cv-request"],["scs","application/scvp-cv-response"],["scss","text/x-scss"],["scurl","text/vnd.curl.scurl"],["sda","application/vnd.stardivision.draw"],["sdc","application/vnd.stardivision.calc"],["sdd","application/vnd.stardivision.impress"],["sdkd","application/vnd.solent.sdkm+xml"],["sdkm","application/vnd.solent.sdkm+xml"],["sdp","application/sdp"],["sdw","application/vnd.stardivision.writer"],["sea","application/octet-stream"],["see","application/vnd.seemail"],["seed","application/vnd.fdsn.seed"],["sema","application/vnd.sema"],["semd","application/vnd.semd"],["semf","application/vnd.semf"],["senmlx","application/senml+xml"],["sensmlx","application/sensml+xml"],["ser","application/java-serialized-object"],["setpay","application/set-payment-initiation"],["setreg","application/set-registration-initiation"],["sfd-hdstx","application/vnd.hydrostatix.sof-data"],["sfs","application/vnd.spotfire.sfs"],["sfv","text/x-sfv"],["sgi","image/sgi"],["sgl","application/vnd.stardivision.writer-global"],["sgm","text/sgml"],["sgml","text/sgml"],["sh","application/x-sh"],["shar","application/x-shar"],["shex","text/shex"],["shf","application/shf+xml"],["shtml","text/html"],["sid","image/x-mrsid-image"],["sieve","application/sieve"],["sig","application/pgp-signature"],["sil","audio/silk"],["silo","model/mesh"],["sis","application/vnd.symbian.install"],["sisx","application/vnd.symbian.install"],["sit","application/x-stuffit"],["sitx","application/x-stuffitx"],["siv","application/sieve"],["skd","application/vnd.koan"],["skm","application/vnd.koan"],["skp","application/vnd.koan"],["skt","application/vnd.koan"],["sldm","application/vnd.ms-powerpoint.slide.macroenabled.12"],["sldx","application/vnd.openxmlformats-officedocument.presentationml.slide"],["slim","text/slim"],["slm","text/slim"],["sls","application/route-s-tsid+xml"],["slt","application/vnd.epson.salt"],["sm","application/vnd.stepmania.stepchart"],["smf","application/vnd.stardivision.math"],["smi","application/smil"],["smil","application/smil"],["smv","video/x-smv"],["smzip","application/vnd.stepmania.package"],["snd","audio/basic"],["snf","application/x-font-snf"],["so","application/octet-stream"],["spc","application/x-pkcs7-certificates"],["spdx","text/spdx"],["spf","application/vnd.yamaha.smaf-phrase"],["spl","application/x-futuresplash"],["spot","text/vnd.in3d.spot"],["spp","application/scvp-vp-response"],["spq","application/scvp-vp-request"],["spx","audio/ogg"],["sql","application/x-sql"],["src","application/x-wais-source"],["srt","application/x-subrip"],["sru","application/sru+xml"],["srx","application/sparql-results+xml"],["ssdl","application/ssdl+xml"],["sse","application/vnd.kodak-descriptor"],["ssf","application/vnd.epson.ssf"],["ssml","application/ssml+xml"],["sst","application/octet-stream"],["st","application/vnd.sailingtracker.track"],["stc","application/vnd.sun.xml.calc.template"],["std","application/vnd.sun.xml.draw.template"],["stf","application/vnd.wt.stf"],["sti","application/vnd.sun.xml.impress.template"],["stk","application/hyperstudio"],["stl","model/stl"],["stpx","model/step+xml"],["stpxz","model/step-xml+zip"],["stpz","model/step+zip"],["str","application/vnd.pg.format"],["stw","application/vnd.sun.xml.writer.template"],["styl","text/stylus"],["stylus","text/stylus"],["sub","text/vnd.dvb.subtitle"],["sus","application/vnd.sus-calendar"],["susp","application/vnd.sus-calendar"],["sv4cpio","application/x-sv4cpio"],["sv4crc","application/x-sv4crc"],["svc","application/vnd.dvb.service"],["svd","application/vnd.svd"],["svg","image/svg+xml"],["svgz","image/svg+xml"],["swa","application/x-director"],["swf","application/x-shockwave-flash"],["swi","application/vnd.aristanetworks.swi"],["swidtag","application/swid+xml"],["sxc","application/vnd.sun.xml.calc"],["sxd","application/vnd.sun.xml.draw"],["sxg","application/vnd.sun.xml.writer.global"],["sxi","application/vnd.sun.xml.impress"],["sxm","application/vnd.sun.xml.math"],["sxw","application/vnd.sun.xml.writer"],["t","text/troff"],["t3","application/x-t3vm-image"],["t38","image/t38"],["taglet","application/vnd.mynfc"],["tao","application/vnd.tao.intent-module-archive"],["tap","image/vnd.tencent.tap"],["tar","application/x-tar"],["tcap","application/vnd.3gpp2.tcap"],["tcl","application/x-tcl"],["td","application/urc-targetdesc+xml"],["teacher","application/vnd.smart.teacher"],["tei","application/tei+xml"],["teicorpus","application/tei+xml"],["tex","application/x-tex"],["texi","application/x-texinfo"],["texinfo","application/x-texinfo"],["text","text/plain"],["tfi","application/thraud+xml"],["tfm","application/x-tex-tfm"],["tfx","image/tiff-fx"],["tga","image/x-tga"],["tgz","application/x-tar"],["thmx","application/vnd.ms-officetheme"],["tif","image/tiff"],["tiff","image/tiff"],["tk","application/x-tcl"],["tmo","application/vnd.tmobile-livetv"],["toml","application/toml"],["torrent","application/x-bittorrent"],["tpl","application/vnd.groove-tool-template"],["tpt","application/vnd.trid.tpt"],["tr","text/troff"],["tra","application/vnd.trueapp"],["trig","application/trig"],["trm","application/x-msterminal"],["ts","video/mp2t"],["tsd","application/timestamped-data"],["tsv","text/tab-separated-values"],["ttc","font/collection"],["ttf","font/ttf"],["ttl","text/turtle"],["ttml","application/ttml+xml"],["twd","application/vnd.simtech-mindmapper"],["twds","application/vnd.simtech-mindmapper"],["txd","application/vnd.genomatix.tuxedo"],["txf","application/vnd.mobius.txf"],["txt","text/plain"],["u8dsn","message/global-delivery-status"],["u8hdr","message/global-headers"],["u8mdn","message/global-disposition-notification"],["u8msg","message/global"],["u32","application/x-authorware-bin"],["ubj","application/ubjson"],["udeb","application/x-debian-package"],["ufd","application/vnd.ufdl"],["ufdl","application/vnd.ufdl"],["ulx","application/x-glulx"],["umj","application/vnd.umajin"],["unityweb","application/vnd.unity"],["uoml","application/vnd.uoml+xml"],["uri","text/uri-list"],["uris","text/uri-list"],["urls","text/uri-list"],["usdz","model/vnd.usdz+zip"],["ustar","application/x-ustar"],["utz","application/vnd.uiq.theme"],["uu","text/x-uuencode"],["uva","audio/vnd.dece.audio"],["uvd","application/vnd.dece.data"],["uvf","application/vnd.dece.data"],["uvg","image/vnd.dece.graphic"],["uvh","video/vnd.dece.hd"],["uvi","image/vnd.dece.graphic"],["uvm","video/vnd.dece.mobile"],["uvp","video/vnd.dece.pd"],["uvs","video/vnd.dece.sd"],["uvt","application/vnd.dece.ttml+xml"],["uvu","video/vnd.uvvu.mp4"],["uvv","video/vnd.dece.video"],["uvva","audio/vnd.dece.audio"],["uvvd","application/vnd.dece.data"],["uvvf","application/vnd.dece.data"],["uvvg","image/vnd.dece.graphic"],["uvvh","video/vnd.dece.hd"],["uvvi","image/vnd.dece.graphic"],["uvvm","video/vnd.dece.mobile"],["uvvp","video/vnd.dece.pd"],["uvvs","video/vnd.dece.sd"],["uvvt","application/vnd.dece.ttml+xml"],["uvvu","video/vnd.uvvu.mp4"],["uvvv","video/vnd.dece.video"],["uvvx","application/vnd.dece.unspecified"],["uvvz","application/vnd.dece.zip"],["uvx","application/vnd.dece.unspecified"],["uvz","application/vnd.dece.zip"],["vbox","application/x-virtualbox-vbox"],["vbox-extpack","application/x-virtualbox-vbox-extpack"],["vcard","text/vcard"],["vcd","application/x-cdlink"],["vcf","text/x-vcard"],["vcg","application/vnd.groove-vcard"],["vcs","text/x-vcalendar"],["vcx","application/vnd.vcx"],["vdi","application/x-virtualbox-vdi"],["vds","model/vnd.sap.vds"],["vhd","application/x-virtualbox-vhd"],["vis","application/vnd.visionary"],["viv","video/vnd.vivo"],["vlc","application/videolan"],["vmdk","application/x-virtualbox-vmdk"],["vob","video/x-ms-vob"],["vor","application/vnd.stardivision.writer"],["vox","application/x-authorware-bin"],["vrml","model/vrml"],["vsd","application/vnd.visio"],["vsf","application/vnd.vsf"],["vss","application/vnd.visio"],["vst","application/vnd.visio"],["vsw","application/vnd.visio"],["vtf","image/vnd.valve.source.texture"],["vtt","text/vtt"],["vtu","model/vnd.vtu"],["vxml","application/voicexml+xml"],["w3d","application/x-director"],["wad","application/x-doom"],["wadl","application/vnd.sun.wadl+xml"],["war","application/java-archive"],["wasm","application/wasm"],["wav","audio/x-wav"],["wax","audio/x-ms-wax"],["wbmp","image/vnd.wap.wbmp"],["wbs","application/vnd.criticaltools.wbs+xml"],["wbxml","application/wbxml"],["wcm","application/vnd.ms-works"],["wdb","application/vnd.ms-works"],["wdp","image/vnd.ms-photo"],["weba","audio/webm"],["webapp","application/x-web-app-manifest+json"],["webm","video/webm"],["webmanifest","application/manifest+json"],["webp","image/webp"],["wg","application/vnd.pmi.widget"],["wgt","application/widget"],["wks","application/vnd.ms-works"],["wm","video/x-ms-wm"],["wma","audio/x-ms-wma"],["wmd","application/x-ms-wmd"],["wmf","image/wmf"],["wml","text/vnd.wap.wml"],["wmlc","application/wmlc"],["wmls","text/vnd.wap.wmlscript"],["wmlsc","application/vnd.wap.wmlscriptc"],["wmv","video/x-ms-wmv"],["wmx","video/x-ms-wmx"],["wmz","application/x-msmetafile"],["woff","font/woff"],["woff2","font/woff2"],["word","application/msword"],["wpd","application/vnd.wordperfect"],["wpl","application/vnd.ms-wpl"],["wps","application/vnd.ms-works"],["wqd","application/vnd.wqd"],["wri","application/x-mswrite"],["wrl","model/vrml"],["wsc","message/vnd.wfa.wsc"],["wsdl","application/wsdl+xml"],["wspolicy","application/wspolicy+xml"],["wtb","application/vnd.webturbo"],["wvx","video/x-ms-wvx"],["x3d","model/x3d+xml"],["x3db","model/x3d+fastinfoset"],["x3dbz","model/x3d+binary"],["x3dv","model/x3d-vrml"],["x3dvz","model/x3d+vrml"],["x3dz","model/x3d+xml"],["x32","application/x-authorware-bin"],["x_b","model/vnd.parasolid.transmit.binary"],["x_t","model/vnd.parasolid.transmit.text"],["xaml","application/xaml+xml"],["xap","application/x-silverlight-app"],["xar","application/vnd.xara"],["xav","application/xcap-att+xml"],["xbap","application/x-ms-xbap"],["xbd","application/vnd.fujixerox.docuworks.binder"],["xbm","image/x-xbitmap"],["xca","application/xcap-caps+xml"],["xcs","application/calendar+xml"],["xdf","application/xcap-diff+xml"],["xdm","application/vnd.syncml.dm+xml"],["xdp","application/vnd.adobe.xdp+xml"],["xdssc","application/dssc+xml"],["xdw","application/vnd.fujixerox.docuworks"],["xel","application/xcap-el+xml"],["xenc","application/xenc+xml"],["xer","application/patch-ops-error+xml"],["xfdf","application/vnd.adobe.xfdf"],["xfdl","application/vnd.xfdl"],["xht","application/xhtml+xml"],["xhtml","application/xhtml+xml"],["xhvml","application/xv+xml"],["xif","image/vnd.xiff"],["xl","application/excel"],["xla","application/vnd.ms-excel"],["xlam","application/vnd.ms-excel.addin.macroEnabled.12"],["xlc","application/vnd.ms-excel"],["xlf","application/xliff+xml"],["xlm","application/vnd.ms-excel"],["xls","application/vnd.ms-excel"],["xlsb","application/vnd.ms-excel.sheet.binary.macroEnabled.12"],["xlsm","application/vnd.ms-excel.sheet.macroEnabled.12"],["xlsx","application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"],["xlt","application/vnd.ms-excel"],["xltm","application/vnd.ms-excel.template.macroEnabled.12"],["xltx","application/vnd.openxmlformats-officedocument.spreadsheetml.template"],["xlw","application/vnd.ms-excel"],["xm","audio/xm"],["xml","application/xml"],["xns","application/xcap-ns+xml"],["xo","application/vnd.olpc-sugar"],["xop","application/xop+xml"],["xpi","application/x-xpinstall"],["xpl","application/xproc+xml"],["xpm","image/x-xpixmap"],["xpr","application/vnd.is-xpr"],["xps","application/vnd.ms-xpsdocument"],["xpw","application/vnd.intercon.formnet"],["xpx","application/vnd.intercon.formnet"],["xsd","application/xml"],["xsl","application/xml"],["xslt","application/xslt+xml"],["xsm","application/vnd.syncml+xml"],["xspf","application/xspf+xml"],["xul","application/vnd.mozilla.xul+xml"],["xvm","application/xv+xml"],["xvml","application/xv+xml"],["xwd","image/x-xwindowdump"],["xyz","chemical/x-xyz"],["xz","application/x-xz"],["yaml","text/yaml"],["yang","application/yang"],["yin","application/yin+xml"],["yml","text/yaml"],["ymp","text/x-suse-ymp"],["z","application/x-compress"],["z1","application/x-zmachine"],["z2","application/x-zmachine"],["z3","application/x-zmachine"],["z4","application/x-zmachine"],["z5","application/x-zmachine"],["z6","application/x-zmachine"],["z7","application/x-zmachine"],["z8","application/x-zmachine"],["zaz","application/vnd.zzazz.deck+xml"],["zip","application/zip"],["zir","application/vnd.zul"],["zirz","application/vnd.zul"],["zmm","application/vnd.handheld-entertainment+xml"],["zsh","text/x-scriptzsh"]]);function iy(e,t,a){let i=function(e){let{name:t}=e;if(t&&-1!==t.lastIndexOf(".")&&!e.type){let a=t.split(".").pop().toLowerCase(),i=ib.get(a);i&&Object.defineProperty(e,"type",{value:i,writable:!1,configurable:!1,enumerable:!0})}return e}(e),{webkitRelativePath:n}=e,o="string"==typeof t?t:"string"==typeof n&&n.length>0?n:`./${e.name}`;return"string"!=typeof i.path&&iw(i,"path",o),void 0!==a&&Object.defineProperty(i,"handle",{value:a,writable:!1,configurable:!1,enumerable:!0}),iw(i,"relativePath",o),i}function iw(e,t,a){Object.defineProperty(e,t,{value:a,writable:!1,configurable:!1,enumerable:!0})}let ik=[".DS_Store","Thumbs.db"];function ij(e){return"object"==typeof e&&null!==e}function iE(e){return e.filter(e=>-1===ik.indexOf(e.name))}function iz(e){if(null===e)return[];let t=[];for(let a=0;a<e.length;a++){let i=e[a];t.push(i)}return t}function iD(e){if("function"!=typeof e.webkitGetAsEntry)return iP(e);let t=e.webkitGetAsEntry();return t&&t.isDirectory?iS(t):iP(e,t)}function iP(e,t){return ix(this,void 0,void 0,function*(){var a;if(globalThis.isSecureContext&&"function"==typeof e.getAsFileSystemHandle){let t=yield e.getAsFileSystemHandle();if(null===t)throw Error(`${e} is not a File`);if(void 0!==t){let e=yield t.getFile();return e.handle=t,iy(e)}}let i=e.getAsFile();if(!i)throw Error(`${e} is not a File`);return iy(i,null!==(a=null==t?void 0:t.fullPath)&&void 0!==a?a:void 0)})}function iC(e){return ix(this,void 0,void 0,function*(){return e.isDirectory?iS(e):function(e){return ix(this,void 0,void 0,function*(){return new Promise((t,a)=>{e.file(a=>{t(iy(a,e.fullPath))},e=>{a(e)})})})}(e)})}function iS(e){let t=e.createReader();return new Promise((e,a)=>{let i=[];!function n(){t.readEntries(t=>ix(this,void 0,void 0,function*(){if(t.length){let e=Promise.all(t.map(iC));i.push(e),n()}else try{let t=yield Promise.all(i);e(t)}catch(e){a(e)}}),e=>{a(e)})}()})}var iO=a(27406);function iM(e){return function(e){if(Array.isArray(e))return iN(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||iB(e)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function iA(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),a.push.apply(a,i)}return a}function iI(e){for(var t=1;t<arguments.length;t++){var a=null!=arguments[t]?arguments[t]:{};t%2?iA(Object(a),!0).forEach(function(t){iF(e,t,a[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):iA(Object(a)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))})}return e}function iF(e,t,a){return t in e?Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[t]=a,e}function iR(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var a,i,n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var o=[],l=!0,r=!1;try{for(n=n.call(e);!(l=(a=n.next()).done)&&(o.push(a.value),!t||o.length!==t);l=!0);}catch(e){r=!0,i=e}finally{try{l||null==n.return||n.return()}finally{if(r)throw i}}return o}}(e,t)||iB(e,t)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function iB(e,t){if(e){if("string"==typeof e)return iN(e,t);var a=Object.prototype.toString.call(e).slice(8,-1);if("Object"===a&&e.constructor&&(a=e.constructor.name),"Map"===a||"Set"===a)return Array.from(e);if("Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a))return iN(e,t)}}function iN(e,t){(null==t||t>e.length)&&(t=e.length);for(var a=0,i=Array(t);a<t;a++)i[a]=e[a];return i}var iT="function"==typeof iO?iO:iO.default,i_=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=e.split(","),a=t.length>1?"one of ".concat(t.join(", ")):t[0];return{code:"file-invalid-type",message:"File type must be ".concat(a)}},iL=function(e){return{code:"file-too-large",message:"File is larger than ".concat(e," ").concat(1===e?"byte":"bytes")}},iK=function(e){return{code:"file-too-small",message:"File is smaller than ".concat(e," ").concat(1===e?"byte":"bytes")}},iW={code:"too-many-files",message:"Too many files"};function iq(e,t){var a="application/x-moz-file"===e.type||iT(e,t);return[a,a?null:i_(t)]}function iH(e,t,a){if(i$(e.size)){if(i$(t)&&i$(a)){if(e.size>a)return[!1,iL(a)];if(e.size<t)return[!1,iK(t)]}else if(i$(t)&&e.size<t)return[!1,iK(t)];else if(i$(a)&&e.size>a)return[!1,iL(a)]}return[!0,null]}function i$(e){return null!=e}function iU(e){return"function"==typeof e.isPropagationStopped?e.isPropagationStopped():void 0!==e.cancelBubble&&e.cancelBubble}function iV(e){return e.dataTransfer?Array.prototype.some.call(e.dataTransfer.types,function(e){return"Files"===e||"application/x-moz-file"===e}):!!e.target&&!!e.target.files}function iG(e){e.preventDefault()}function iY(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return function(e){for(var a=arguments.length,i=Array(a>1?a-1:0),n=1;n<a;n++)i[n-1]=arguments[n];return t.some(function(t){return!iU(e)&&t&&t.apply(void 0,[e].concat(i)),iU(e)})}}function iX(e){return"audio/*"===e||"video/*"===e||"image/*"===e||"text/*"===e||"application/*"===e||/\w+\/[-+.\w]+/g.test(e)}function iZ(e){return/^.*\.[\w]+$/.test(e)}var iJ=["children"],iQ=["open"],i0=["refKey","role","onKeyDown","onFocus","onBlur","onClick","onDragEnter","onDragOver","onDragLeave","onDrop"],i1=["refKey","onChange","onClick"];function i4(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var a,i,n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var o=[],l=!0,r=!1;try{for(n=n.call(e);!(l=(a=n.next()).done)&&(o.push(a.value),!t||o.length!==t);l=!0);}catch(e){r=!0,i=e}finally{try{l||null==n.return||n.return()}finally{if(r)throw i}}return o}}(e,t)||i3(e,t)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function i3(e,t){if(e){if("string"==typeof e)return i2(e,t);var a=Object.prototype.toString.call(e).slice(8,-1);if("Object"===a&&e.constructor&&(a=e.constructor.name),"Map"===a||"Set"===a)return Array.from(e);if("Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a))return i2(e,t)}}function i2(e,t){(null==t||t>e.length)&&(t=e.length);for(var a=0,i=Array(t);a<t;a++)i[a]=e[a];return i}function i5(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),a.push.apply(a,i)}return a}function i6(e){for(var t=1;t<arguments.length;t++){var a=null!=arguments[t]?arguments[t]:{};t%2?i5(Object(a),!0).forEach(function(t){i8(e,t,a[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):i5(Object(a)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))})}return e}function i8(e,t,a){return t in e?Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[t]=a,e}function i7(e,t){if(null==e)return{};var a,i,n=function(e,t){if(null==e)return{};var a,i,n={},o=Object.keys(e);for(i=0;i<o.length;i++)a=o[i],t.indexOf(a)>=0||(n[a]=e[a]);return n}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(i=0;i<o.length;i++)a=o[i],!(t.indexOf(a)>=0)&&Object.prototype.propertyIsEnumerable.call(e,a)&&(n[a]=e[a])}return n}var i9=(0,c.forwardRef)(function(e,t){var a=e.children,i=na(i7(e,iJ)),n=i.open,o=i7(i,iQ);return(0,c.useImperativeHandle)(t,function(){return{open:n}},[n]),c.createElement(c.Fragment,null,a(i6(i6({},o),{},{open:n})))});i9.displayName="Dropzone";var ne={disabled:!1,getFilesFromEvent:function(e){return ix(this,void 0,void 0,function*(){var t;if(ij(e)&&ij(e.dataTransfer))return function(e,t){return ix(this,void 0,void 0,function*(){if(e.items){let a=iz(e.items).filter(e=>"file"===e.kind);return"drop"!==t?a:iE(function e(t){return t.reduce((t,a)=>[...t,...Array.isArray(a)?e(a):[a]],[])}((yield Promise.all(a.map(iD)))))}return iE(iz(e.files).map(e=>iy(e)))})}(e.dataTransfer,e.type);if(ij(t=e)&&ij(t.target))return iz(e.target.files).map(e=>iy(e));return Array.isArray(e)&&e.every(e=>"getFile"in e&&"function"==typeof e.getFile)?function(e){return ix(this,void 0,void 0,function*(){return(yield Promise.all(e.map(e=>e.getFile()))).map(e=>iy(e))})}(e):[]})},maxSize:1/0,minSize:0,multiple:!0,maxFiles:0,preventDropOnDocument:!0,noClick:!1,noKeyboard:!1,noDrag:!1,noDragEventsBubbling:!1,validator:null,useFsAccessApi:!1,autoFocus:!1};i9.defaultProps=ne,i9.propTypes={children:ih.func,accept:ih.objectOf(ih.arrayOf(ih.string)),multiple:ih.bool,preventDropOnDocument:ih.bool,noClick:ih.bool,noKeyboard:ih.bool,noDrag:ih.bool,noDragEventsBubbling:ih.bool,minSize:ih.number,maxSize:ih.number,maxFiles:ih.number,disabled:ih.bool,getFilesFromEvent:ih.func,onFileDialogCancel:ih.func,onFileDialogOpen:ih.func,useFsAccessApi:ih.bool,autoFocus:ih.bool,onDragEnter:ih.func,onDragLeave:ih.func,onDragOver:ih.func,onDrop:ih.func,onDropAccepted:ih.func,onDropRejected:ih.func,onError:ih.func,validator:ih.func};var nt={isFocused:!1,isFileDialogActive:!1,isDragActive:!1,isDragAccept:!1,isDragReject:!1,acceptedFiles:[],fileRejections:[]};function na(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=i6(i6({},ne),e),a=t.accept,i=t.disabled,n=t.getFilesFromEvent,o=t.maxSize,l=t.minSize,r=t.multiple,s=t.maxFiles,p=t.onDragEnter,d=t.onDragLeave,u=t.onDragOver,m=t.onDrop,f=t.onDropAccepted,g=t.onDropRejected,v=t.onFileDialogCancel,h=t.onFileDialogOpen,x=t.useFsAccessApi,b=t.autoFocus,y=t.preventDropOnDocument,w=t.noClick,k=t.noKeyboard,j=t.noDrag,E=t.noDragEventsBubbling,z=t.onError,D=t.validator,P=(0,c.useMemo)(function(){return function(e){if(i$(e))return Object.entries(e).reduce(function(e,t){var a=iR(t,2),i=a[0],n=a[1];return[].concat(iM(e),[i],iM(n))},[]).filter(function(e){return iX(e)||iZ(e)}).join(",")}(a)},[a]),C=(0,c.useMemo)(function(){return i$(a)?[{description:"Files",accept:Object.entries(a).filter(function(e){var t=iR(e,2),a=t[0],i=t[1],n=!0;return iX(a)||(console.warn('Skipped "'.concat(a,'" because it is not a valid MIME type. Check https://developer.mozilla.org/en-US/docs/Web/HTTP/Basics_of_HTTP/MIME_types/Common_types for a list of valid MIME types.')),n=!1),Array.isArray(i)&&i.every(iZ)||(console.warn('Skipped "'.concat(a,'" because an invalid file extension was provided.')),n=!1),n}).reduce(function(e,t){var a=iR(t,2),i=a[0],n=a[1];return iI(iI({},e),{},iF({},i,n))},{})}]:a},[a]),S=(0,c.useMemo)(function(){return"function"==typeof h?h:nn},[h]),O=(0,c.useMemo)(function(){return"function"==typeof v?v:nn},[v]),M=(0,c.useRef)(null),A=(0,c.useRef)(null),I=i4((0,c.useReducer)(ni,nt),2),F=I[0],R=I[1],B=F.isFocused,N=F.isFileDialogActive,T=(0,c.useRef)("undefined"!=typeof window&&window.isSecureContext&&x&&"showOpenFilePicker"in window),_=function(){!T.current&&N&&setTimeout(function(){A.current&&!A.current.files.length&&(R({type:"closeDialog"}),O())},300)};(0,c.useEffect)(function(){return window.addEventListener("focus",_,!1),function(){window.removeEventListener("focus",_,!1)}},[A,N,O,T]);var L=(0,c.useRef)([]),K=function(e){!(M.current&&M.current.contains(e.target))&&(e.preventDefault(),L.current=[])};(0,c.useEffect)(function(){return y&&(document.addEventListener("dragover",iG,!1),document.addEventListener("drop",K,!1)),function(){y&&(document.removeEventListener("dragover",iG),document.removeEventListener("drop",K))}},[M,y]),(0,c.useEffect)(function(){return!i&&b&&M.current&&M.current.focus(),function(){}},[M,b,i]);var W=(0,c.useCallback)(function(e){z?z(e):console.error(e)},[z]),q=(0,c.useCallback)(function(e){var t;e.preventDefault(),e.persist(),ea(e),L.current=[].concat(function(e){if(Array.isArray(e))return i2(e)}(t=L.current)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(t)||i3(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),[e.target]),iV(e)&&Promise.resolve(n(e)).then(function(t){if(!iU(e)||E){var a,i,n,c,d,u,m,f,g=t.length,v=g>0&&(i=(a={files:t,accept:P,minSize:l,maxSize:o,multiple:r,maxFiles:s,validator:D}).files,n=a.accept,c=a.minSize,d=a.maxSize,u=a.multiple,m=a.maxFiles,f=a.validator,(!!u||!(i.length>1))&&(!u||!(m>=1)||!(i.length>m))&&i.every(function(e){var t=iR(iq(e,n),1)[0],a=iR(iH(e,c,d),1)[0],i=f?f(e):null;return t&&a&&!i}));R({isDragAccept:v,isDragReject:g>0&&!v,isDragActive:!0,type:"setDraggedFiles"}),p&&p(e)}}).catch(function(e){return W(e)})},[n,p,W,E,P,l,o,r,s,D]),H=(0,c.useCallback)(function(e){e.preventDefault(),e.persist(),ea(e);var t=iV(e);if(t&&e.dataTransfer)try{e.dataTransfer.dropEffect="copy"}catch(e){}return t&&u&&u(e),!1},[u,E]),$=(0,c.useCallback)(function(e){e.preventDefault(),e.persist(),ea(e);var t=L.current.filter(function(e){return M.current&&M.current.contains(e)}),a=t.indexOf(e.target);-1!==a&&t.splice(a,1),L.current=t,!(t.length>0)&&(R({type:"setDraggedFiles",isDragActive:!1,isDragAccept:!1,isDragReject:!1}),iV(e)&&d&&d(e))},[M,d,E]),U=(0,c.useCallback)(function(e,t){var a=[],i=[];e.forEach(function(e){var t=i4(iq(e,P),2),n=t[0],r=t[1],s=i4(iH(e,l,o),2),c=s[0],p=s[1],d=D?D(e):null;if(n&&c&&!d)a.push(e);else{var u=[r,p];d&&(u=u.concat(d)),i.push({file:e,errors:u.filter(function(e){return e})})}}),(!r&&a.length>1||r&&s>=1&&a.length>s)&&(a.forEach(function(e){i.push({file:e,errors:[iW]})}),a.splice(0)),R({acceptedFiles:a,fileRejections:i,isDragReject:i.length>0,type:"setFiles"}),m&&m(a,i,t),i.length>0&&g&&g(i,t),a.length>0&&f&&f(a,t)},[R,r,P,l,o,s,m,f,g,D]),V=(0,c.useCallback)(function(e){e.preventDefault(),e.persist(),ea(e),L.current=[],iV(e)&&Promise.resolve(n(e)).then(function(t){(!iU(e)||E)&&U(t,e)}).catch(function(e){return W(e)}),R({type:"reset"})},[n,U,W,E]),G=(0,c.useCallback)(function(){if(T.current){R({type:"openDialog"}),S(),window.showOpenFilePicker({multiple:r,types:C}).then(function(e){return n(e)}).then(function(e){U(e,null),R({type:"closeDialog"})}).catch(function(e){e instanceof DOMException&&("AbortError"===e.name||e.code===e.ABORT_ERR)?(O(e),R({type:"closeDialog"})):e instanceof DOMException&&("SecurityError"===e.name||e.code===e.SECURITY_ERR)?(T.current=!1,A.current?(A.current.value=null,A.current.click()):W(Error("Cannot open the file picker because the https://developer.mozilla.org/en-US/docs/Web/API/File_System_Access_API is not supported and no <input> was provided."))):W(e)});return}A.current&&(R({type:"openDialog"}),S(),A.current.value=null,A.current.click())},[R,S,O,x,U,W,C,r]),Y=(0,c.useCallback)(function(e){M.current&&M.current.isEqualNode(e.target)&&(" "===e.key||"Enter"===e.key||32===e.keyCode||13===e.keyCode)&&(e.preventDefault(),G())},[M,G]),X=(0,c.useCallback)(function(){R({type:"focus"})},[]),Z=(0,c.useCallback)(function(){R({type:"blur"})},[]),J=(0,c.useCallback)(function(){!w&&(function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:window.navigator.userAgent;return -1!==e.indexOf("MSIE")||-1!==e.indexOf("Trident/")||-1!==e.indexOf("Edge/")}()?setTimeout(G,0):G())},[w,G]),Q=function(e){return i?null:e},ee=function(e){return k?null:Q(e)},et=function(e){return j?null:Q(e)},ea=function(e){E&&e.stopPropagation()},ei=(0,c.useMemo)(function(){return function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.refKey,a=e.role,n=e.onKeyDown,o=e.onFocus,l=e.onBlur,r=e.onClick,s=e.onDragEnter,c=e.onDragOver,p=e.onDragLeave,d=e.onDrop,u=i7(e,i0);return i6(i6(i8({onKeyDown:ee(iY(n,Y)),onFocus:ee(iY(o,X)),onBlur:ee(iY(l,Z)),onClick:Q(iY(r,J)),onDragEnter:et(iY(s,q)),onDragOver:et(iY(c,H)),onDragLeave:et(iY(p,$)),onDrop:et(iY(d,V)),role:"string"==typeof a&&""!==a?a:"presentation"},void 0===t?"ref":t,M),i||k?{}:{tabIndex:0}),u)}},[M,Y,X,Z,J,q,H,$,V,k,j,i]),en=(0,c.useCallback)(function(e){e.stopPropagation()},[]),eo=(0,c.useMemo)(function(){return function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.refKey,a=e.onChange,i=e.onClick,n=i7(e,i1);return i6(i6({},i8({accept:P,multiple:r,type:"file",style:{border:0,clip:"rect(0, 0, 0, 0)",clipPath:"inset(50%)",height:"1px",margin:"0 -1px -1px 0",overflow:"hidden",padding:0,position:"absolute",width:"1px",whiteSpace:"nowrap"},onChange:Q(iY(a,V)),onClick:Q(iY(i,en)),tabIndex:-1},void 0===t?"ref":t,A)),n)}},[A,a,r,V,i]);return i6(i6({},F),{},{isFocused:B&&!i,getRootProps:ei,getInputProps:eo,rootRef:M,inputRef:A,open:Q(G)})}function ni(e,t){switch(t.type){case"focus":return i6(i6({},e),{},{isFocused:!0});case"blur":return i6(i6({},e),{},{isFocused:!1});case"openDialog":return i6(i6({},nt),{},{isFileDialogActive:!0});case"closeDialog":return i6(i6({},e),{},{isFileDialogActive:!1});case"setDraggedFiles":return i6(i6({},e),{},{isDragActive:t.isDragActive,isDragAccept:t.isDragAccept,isDragReject:t.isDragReject});case"setFiles":return i6(i6({},e),{},{acceptedFiles:t.acceptedFiles,fileRejections:t.fileRejections,isDragReject:t.isDragReject});case"reset":return i6({},nt);default:return e}}function nn(){}let no=(0,a(62688).A)("Upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]]);var nl=a(39297);let nr={initial:{x:0,y:0},animate:{x:20,y:-20,opacity:.9}},ns={initial:{opacity:0},animate:{opacity:1}},nc=({onChange:e,isMultiple:t})=>{let[a,i]=(0,c.useState)([]),n=(0,c.useRef)(null),l=t=>{i(e=>[...e,...t]),e&&e(t)},{getRootProps:r,isDragActive:s}=na({multiple:!1,noClick:!0,onDrop:l,onDropRejected:e=>{console.log(e)}});return(0,o.jsx)("div",{className:"w-full",...r(),children:(0,o.jsxs)(iv.P.div,{onClick:()=>{n.current?.click()},whileHover:"animate",className:"p-10 group/file block rounded-lg cursor-pointer w-full relative overflow-hidden",children:[(0,o.jsx)("input",{ref:n,id:"file-upload-handle",type:"file",multiple:t,onChange:e=>l(Array.from(e.target.files||[])),className:"hidden"}),(0,o.jsx)("div",{className:"absolute inset-0 [mask-image:radial-gradient(ellipse_at_center,white,transparent)]",children:(0,o.jsx)(np,{})}),(0,o.jsx)("div",{className:"flex flex-col items-center justify-center",children:(0,o.jsxs)("div",{className:"relative w-full mt-10 max-w-xl mx-auto",children:[a.length>0&&a.map((t,n)=>(0,o.jsxs)(iv.P.div,{layoutId:0===n?"file-upload":"file-upload-"+n,className:(0,nl.cn)("relative z-40 bg-white dark:bg-neutral-900 flex flex-col items-start justify-start md:h-24 p-4 mt-4 w-full mx-auto rounded-md","shadow-sm"),children:[(0,o.jsxs)("div",{className:"flex justify-between w-full items-center gap-4",children:[(0,o.jsx)(iv.P.p,{initial:{opacity:0},animate:{opacity:1},layout:!0,className:"text-base text-neutral-700 dark:text-neutral-300 truncate max-w-xs",children:t.name}),(0,o.jsxs)(iv.P.p,{initial:{opacity:0},animate:{opacity:1},layout:!0,className:"rounded-lg px-2 py-1 w-fit flex-shrink-0 text-sm text-neutral-600 dark:bg-neutral-800 dark:text-white shadow-input",children:[(t.size/1048576).toFixed(2)," MB"]}),(0,o.jsx)(ip.T,{size:"sm",variant:"light",color:"danger",className:"min-w-2 font-bold text-lg absolute -top-3 -right-3",onClick:()=>{i(e=>e.filter((e,t)=>t!==n)),e&&e(a)},children:"X"})]}),(0,o.jsxs)("div",{className:"flex text-sm md:flex-row flex-col items-start md:items-center w-full mt-2 justify-between text-neutral-600 dark:text-neutral-400",children:[(0,o.jsx)(iv.P.p,{initial:{opacity:0},animate:{opacity:1},layout:!0,className:"px-1 py-0.5 rounded-md bg-gray-100 dark:bg-neutral-800 ",children:t.type}),(0,o.jsxs)(iv.P.p,{initial:{opacity:0},animate:{opacity:1},layout:!0,children:["modified ",new Date(t.lastModified).toLocaleDateString()]})]})]},"file"+n)),!a.length&&(0,o.jsx)(iv.P.div,{layoutId:"file-upload",variants:nr,transition:{type:"spring",stiffness:300,damping:20},className:(0,nl.cn)("relative group-hover/file:shadow-2xl z-40 bg-white dark:bg-neutral-900 flex items-center justify-center h-32 mt-4 w-full max-w-[8rem] mx-auto rounded-md","shadow-[0px_10px_50px_rgba(0,0,0,0.1)]"),children:s?(0,o.jsxs)(iv.P.p,{initial:{opacity:0},animate:{opacity:1},className:"text-neutral-600 flex flex-col items-center",children:["Drop it",(0,o.jsx)(no,{className:"h-4 w-4 text-neutral-600 dark:text-neutral-400"})]}):(0,o.jsx)(no,{className:"h-4 w-4 text-neutral-600 dark:text-neutral-300"})}),!a.length&&(0,o.jsx)(iv.P.div,{variants:ns,className:"absolute opacity-0 border border-dashed border-sky-400 inset-0 z-30 bg-transparent flex items-center justify-center h-32 mt-4 w-full max-w-[8rem] mx-auto rounded-md"})]})})]})})};function np(){return(0,o.jsx)("div",{className:"flex bg-gray-100 dark:bg-neutral-900 flex-shrink-0 flex-wrap justify-center items-center gap-x-px gap-y-px  scale-105",children:Array.from({length:11}).map((e,t)=>Array.from({length:41}).map((e,a)=>(0,o.jsx)("div",{className:`w-10 h-10 flex flex-shrink-0 rounded-[2px] ${(41*t+a)%2==0?"bg-gray-50 dark:bg-neutral-950":"bg-gray-50 dark:bg-neutral-950 shadow-[0px_0px_1px_3px_rgba(255,255,255,1)_inset] dark:shadow-[0px_0px_1px_3px_rgba(0,0,0,1)_inset]"}`},`${a}-${t}`)))})}var nd=a(52581),nu=a(16189);let nm=["القدس","رام الله","بيت لحم","الخليل","نابلس","جنين","طولكرم","قلقيلية","أريحا"],nf=[{value:"M",text:"طبية"},{value:"O",text:"إغاثة"},{value:"D",text:"خطر"}];function ng({token:e}){let t=(0,nu.useRouter)(),[a,i]=(0,c.useState)([]),{control:n,handleSubmit:p,setValue:d,formState:{errors:u,isSubmitting:m}}=(0,l.mN)({resolver:(0,r.u)(ig),defaultValues:{location:"",description:"",images:[]}});async function f(a){try{let i=new FormData;a.images?.forEach(e=>{i.append("images",e)}),i.append("location",a.location),i.append("emergency_type",a.emergency_type),i.append("description",a.description);let n=await fetch("http://localhost:8000/emergency/create/",{method:"POST",body:i,headers:{Authorization:`Bearer ${e}`}});if(201===n.status)nd.o.success("تم إرسال الطلب بنجاح"),t.refresh(),t.push("/");else{let e=await n.json();console.error("Error submitting form:",e),nd.o.error("حدث خطأ أثناء إرسال الطلب, برجاء اعادة المحاولة")}}catch(e){console.error("Error submitting form:",e),nd.o.error("حدث خطأ أثناء إرسال الطلب, برجاء اعادة المحاولة")}}return(0,o.jsxs)("div",{className:"w-full max-w-2xl mx-auto p-6",children:[(0,o.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,o.jsx)("h1",{className:"text-2xl font-bold",children:"أرسل إشعار للطوارئ"}),(0,o.jsx)(s.A,{className:"w-6 h-6 text-blue-500"})]}),(0,o.jsxs)("form",{onSubmit:p(f),className:"space-y-6",children:[(0,o.jsxs)("div",{className:"space-y-2",children:[(0,o.jsx)("label",{className:"text-sm font-medium",children:"حدد موقعك (إجباري) *"}),(0,o.jsx)(l.xI,{name:"location",control:n,render:({field:e})=>(0,o.jsx)(aG,{label:"من فضلك اختر موقعك",...e,errorMessage:u.location?.message,isInvalid:!!u.location,children:nm.map(e=>(0,o.jsx)(aY,{value:e,children:e},e))})})]}),(0,o.jsxs)("div",{className:"space-y-2",children:[(0,o.jsx)("label",{className:"text-sm font-medium",children:"نوع المساعدة (إجباري) *"}),(0,o.jsx)(l.xI,{name:"emergency_type",control:n,render:({field:e})=>(0,o.jsx)(aG,{label:"من فضلك اختر نوع المساعدة",...e,errorMessage:u.emergency_type?.message,isInvalid:!!u.emergency_type,children:nf.map(e=>(0,o.jsx)(aY,{value:e.value,children:e.text},e.value))})})]}),(0,o.jsxs)("div",{className:"space-y-2",children:[(0,o.jsx)("label",{className:"text-sm font-medium",children:"ارفق صور للحالة (إجباري) *"}),(0,o.jsx)(nc,{isMultiple:!0,onChange:e=>{if(!e||e.length<=0)return;let t=Array.from(e).filter(e=>e.type.startsWith("image/"));i(e=>[...e,...t]),d("images",t)}}),u.images&&(0,o.jsx)("p",{className:"text-xs text-danger",children:u.images.message})]}),(0,o.jsxs)("div",{className:"space-y-2",children:[(0,o.jsx)("label",{className:"text-sm font-medium",children:"وصف الحالة (إجباري) *"}),(0,o.jsx)(l.xI,{name:"description",control:n,render:({field:e})=>(0,o.jsx)(ic,{placeholder:"من فضلك اكتب وصف الحالة",minRows:25,...e,errorMessage:u.description?.message,isInvalid:!!u.description})})]}),(0,o.jsxs)("div",{className:"flex justify-between items-center gap-2",children:[(0,o.jsx)(ip.T,{as:id.h,href:"/",type:"button",variant:"bordered",color:"default",children:"إلغاء"}),(0,o.jsx)(ip.T,{type:"submit",color:"primary",isLoading:m,children:"أرسل الطلب"})]})]})]})}},60463:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>l.a,__next_app__:()=>d,pages:()=>p,routeModule:()=>u,tree:()=>c});var i=a(65239),n=a(48088),o=a(88170),l=a.n(o),r=a(30893),s={};for(let e in r)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(s[e]=()=>r[e]);a.d(t,s);let c={children:["",{children:["(home)",{children:["add-application",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,63946)),"G:\\Graduation project 2025\\app (2)\\app\\frontend\\src\\app\\(home)\\add-application\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,89282)),"G:\\Graduation project 2025\\app (2)\\app\\frontend\\src\\app\\(home)\\layout.tsx"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,94431)),"G:\\Graduation project 2025\\app (2)\\app\\frontend\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(a.bind(a,54431)),"G:\\Graduation project 2025\\app (2)\\app\\frontend\\src\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(a.bind(a,54413)),"G:\\Graduation project 2025\\app (2)\\app\\frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,p=["G:\\Graduation project 2025\\app (2)\\app\\frontend\\src\\app\\(home)\\add-application\\page.tsx"],d={require:a,loadChunk:()=>Promise.resolve()},u=new i.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/(home)/add-application/page",pathname:"/add-application",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63946:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>r});var i=a(37413),n=a(12535),o=a(44999),l=a(39916);async function r(){let e=await (0,o.UL)(),t=e.get("access")?.value??"";return t||(0,l.redirect)("/auth?error=not-logged-in"),(0,i.jsx)("main",{className:"min-h-screen bg-gray-50",children:(0,i.jsx)(n.EmergencyForm,{token:t})})}},79551:e=>{"use strict";e.exports=require("url")},81333:(e,t,a)=>{Promise.resolve().then(a.bind(a,12535))},84031:(e,t,a)=>{"use strict";var i=a(34452);function n(){}function o(){}o.resetWarningCache=n,e.exports=function(){function e(e,t,a,n,o,l){if(l!==i){var r=Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw r.name="Invariant Violation",r}}function t(){return e}e.isRequired=e;var a={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:o,resetWarningCache:n};return a.PropTypes=a,a}},87955:(e,t,a)=>{e.exports=a(84031)()},87957:(e,t,a)=>{Promise.resolve().then(a.bind(a,57607))}};var t=require("../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),i=t.X(0,[447,172,443,966,994,416,513,749,200],()=>a(60463));module.exports=i})();